/* This file was generated by upbc (the upb compiler) from the input
 * file:
 *
 *     envoy/api/v2/core/health_check.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#ifndef ENVOY_API_V2_CORE_HEALTH_CHECK_PROTO_UPB_H_
#define ENVOY_API_V2_CORE_HEALTH_CHECK_PROTO_UPB_H_

#include "upb/generated_util.h"
#include "upb/msg.h"
#include "upb/decode.h"
#include "upb/encode.h"

#include "upb/port_def.inc"

#ifdef __cplusplus
extern "C" {
#endif

struct envoy_api_v2_core_HealthCheck;
struct envoy_api_v2_core_HealthCheck_Payload;
struct envoy_api_v2_core_HealthCheck_HttpHealthCheck;
struct envoy_api_v2_core_HealthCheck_TcpHealthCheck;
struct envoy_api_v2_core_HealthCheck_RedisHealthCheck;
struct envoy_api_v2_core_HealthCheck_GrpcHealthCheck;
struct envoy_api_v2_core_HealthCheck_CustomHealthCheck;
typedef struct envoy_api_v2_core_HealthCheck envoy_api_v2_core_HealthCheck;
typedef struct envoy_api_v2_core_HealthCheck_Payload envoy_api_v2_core_HealthCheck_Payload;
typedef struct envoy_api_v2_core_HealthCheck_HttpHealthCheck envoy_api_v2_core_HealthCheck_HttpHealthCheck;
typedef struct envoy_api_v2_core_HealthCheck_TcpHealthCheck envoy_api_v2_core_HealthCheck_TcpHealthCheck;
typedef struct envoy_api_v2_core_HealthCheck_RedisHealthCheck envoy_api_v2_core_HealthCheck_RedisHealthCheck;
typedef struct envoy_api_v2_core_HealthCheck_GrpcHealthCheck envoy_api_v2_core_HealthCheck_GrpcHealthCheck;
typedef struct envoy_api_v2_core_HealthCheck_CustomHealthCheck envoy_api_v2_core_HealthCheck_CustomHealthCheck;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_msginit;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_Payload_msginit;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_HttpHealthCheck_msginit;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_TcpHealthCheck_msginit;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_RedisHealthCheck_msginit;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_GrpcHealthCheck_msginit;
extern const upb_msglayout envoy_api_v2_core_HealthCheck_CustomHealthCheck_msginit;
struct envoy_api_v2_core_HeaderValueOption;
struct envoy_type_Int64Range;
struct google_protobuf_Any;
struct google_protobuf_BoolValue;
struct google_protobuf_Duration;
struct google_protobuf_Struct;
struct google_protobuf_UInt32Value;
extern const upb_msglayout envoy_api_v2_core_HeaderValueOption_msginit;
extern const upb_msglayout envoy_type_Int64Range_msginit;
extern const upb_msglayout google_protobuf_Any_msginit;
extern const upb_msglayout google_protobuf_BoolValue_msginit;
extern const upb_msglayout google_protobuf_Duration_msginit;
extern const upb_msglayout google_protobuf_Struct_msginit;
extern const upb_msglayout google_protobuf_UInt32Value_msginit;

typedef enum {
  envoy_api_v2_core_UNKNOWN = 0,
  envoy_api_v2_core_HEALTHY = 1,
  envoy_api_v2_core_UNHEALTHY = 2,
  envoy_api_v2_core_DRAINING = 3,
  envoy_api_v2_core_TIMEOUT = 4,
  envoy_api_v2_core_DEGRADED = 5
} envoy_api_v2_core_HealthStatus;


/* envoy.api.v2.core.HealthCheck */

UPB_INLINE envoy_api_v2_core_HealthCheck *envoy_api_v2_core_HealthCheck_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck *)upb_msg_new(&envoy_api_v2_core_HealthCheck_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck *envoy_api_v2_core_HealthCheck_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck *ret = envoy_api_v2_core_HealthCheck_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_serialize(const envoy_api_v2_core_HealthCheck *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_msginit, arena, len);
}

typedef enum {
  envoy_api_v2_core_HealthCheck_health_checker_http_health_check = 8,
  envoy_api_v2_core_HealthCheck_health_checker_tcp_health_check = 9,
  envoy_api_v2_core_HealthCheck_health_checker_grpc_health_check = 11,
  envoy_api_v2_core_HealthCheck_health_checker_custom_health_check = 13,
  envoy_api_v2_core_HealthCheck_health_checker_NOT_SET = 0
} envoy_api_v2_core_HealthCheck_health_checker_oneofcases;
UPB_INLINE envoy_api_v2_core_HealthCheck_health_checker_oneofcases envoy_api_v2_core_HealthCheck_health_checker_case(const envoy_api_v2_core_HealthCheck* msg) { return (envoy_api_v2_core_HealthCheck_health_checker_oneofcases)UPB_FIELD_AT(msg, int32_t, UPB_SIZE(68, 128)); }

UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_timeout(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(16, 24)); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_interval(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(20, 32)); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_interval_jitter(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(24, 40)); }
UPB_INLINE const struct google_protobuf_UInt32Value* envoy_api_v2_core_HealthCheck_unhealthy_threshold(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_UInt32Value*, UPB_SIZE(28, 48)); }
UPB_INLINE const struct google_protobuf_UInt32Value* envoy_api_v2_core_HealthCheck_healthy_threshold(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_UInt32Value*, UPB_SIZE(32, 56)); }
UPB_INLINE const struct google_protobuf_UInt32Value* envoy_api_v2_core_HealthCheck_alt_port(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_UInt32Value*, UPB_SIZE(36, 64)); }
UPB_INLINE const struct google_protobuf_BoolValue* envoy_api_v2_core_HealthCheck_reuse_connection(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_BoolValue*, UPB_SIZE(40, 72)); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_has_http_health_check(const envoy_api_v2_core_HealthCheck *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(68, 128), 8); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_HttpHealthCheck* envoy_api_v2_core_HealthCheck_http_health_check(const envoy_api_v2_core_HealthCheck *msg) { return UPB_READ_ONEOF(msg, const envoy_api_v2_core_HealthCheck_HttpHealthCheck*, UPB_SIZE(64, 120), UPB_SIZE(68, 128), 8, NULL); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_has_tcp_health_check(const envoy_api_v2_core_HealthCheck *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(68, 128), 9); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_TcpHealthCheck* envoy_api_v2_core_HealthCheck_tcp_health_check(const envoy_api_v2_core_HealthCheck *msg) { return UPB_READ_ONEOF(msg, const envoy_api_v2_core_HealthCheck_TcpHealthCheck*, UPB_SIZE(64, 120), UPB_SIZE(68, 128), 9, NULL); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_has_grpc_health_check(const envoy_api_v2_core_HealthCheck *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(68, 128), 11); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_GrpcHealthCheck* envoy_api_v2_core_HealthCheck_grpc_health_check(const envoy_api_v2_core_HealthCheck *msg) { return UPB_READ_ONEOF(msg, const envoy_api_v2_core_HealthCheck_GrpcHealthCheck*, UPB_SIZE(64, 120), UPB_SIZE(68, 128), 11, NULL); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_no_traffic_interval(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(44, 80)); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_has_custom_health_check(const envoy_api_v2_core_HealthCheck *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(68, 128), 13); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_CustomHealthCheck* envoy_api_v2_core_HealthCheck_custom_health_check(const envoy_api_v2_core_HealthCheck *msg) { return UPB_READ_ONEOF(msg, const envoy_api_v2_core_HealthCheck_CustomHealthCheck*, UPB_SIZE(64, 120), UPB_SIZE(68, 128), 13, NULL); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_unhealthy_interval(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(48, 88)); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_unhealthy_edge_interval(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(52, 96)); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_healthy_edge_interval(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(56, 104)); }
UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_event_log_path(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 8)); }
UPB_INLINE uint32_t envoy_api_v2_core_HealthCheck_interval_jitter_percent(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, uint32_t, UPB_SIZE(0, 0)); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_always_log_health_check_failures(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, bool, UPB_SIZE(4, 4)); }
UPB_INLINE const struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_initial_jitter(const envoy_api_v2_core_HealthCheck *msg) { return UPB_FIELD_AT(msg, const struct google_protobuf_Duration*, UPB_SIZE(60, 112)); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_set_timeout(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(16, 24)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_timeout(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_timeout(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_timeout(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_interval(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(20, 32)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_interval(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_interval(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_interval(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_interval_jitter(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(24, 40)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_interval_jitter(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_interval_jitter(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_interval_jitter(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_unhealthy_threshold(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_UInt32Value* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_UInt32Value*, UPB_SIZE(28, 48)) = value;
}
UPB_INLINE struct google_protobuf_UInt32Value* envoy_api_v2_core_HealthCheck_mutable_unhealthy_threshold(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_UInt32Value* sub = (struct google_protobuf_UInt32Value*)envoy_api_v2_core_HealthCheck_unhealthy_threshold(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_UInt32Value*)upb_msg_new(&google_protobuf_UInt32Value_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_unhealthy_threshold(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_healthy_threshold(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_UInt32Value* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_UInt32Value*, UPB_SIZE(32, 56)) = value;
}
UPB_INLINE struct google_protobuf_UInt32Value* envoy_api_v2_core_HealthCheck_mutable_healthy_threshold(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_UInt32Value* sub = (struct google_protobuf_UInt32Value*)envoy_api_v2_core_HealthCheck_healthy_threshold(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_UInt32Value*)upb_msg_new(&google_protobuf_UInt32Value_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_healthy_threshold(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_alt_port(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_UInt32Value* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_UInt32Value*, UPB_SIZE(36, 64)) = value;
}
UPB_INLINE struct google_protobuf_UInt32Value* envoy_api_v2_core_HealthCheck_mutable_alt_port(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_UInt32Value* sub = (struct google_protobuf_UInt32Value*)envoy_api_v2_core_HealthCheck_alt_port(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_UInt32Value*)upb_msg_new(&google_protobuf_UInt32Value_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_alt_port(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_reuse_connection(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_BoolValue* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_BoolValue*, UPB_SIZE(40, 72)) = value;
}
UPB_INLINE struct google_protobuf_BoolValue* envoy_api_v2_core_HealthCheck_mutable_reuse_connection(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_BoolValue* sub = (struct google_protobuf_BoolValue*)envoy_api_v2_core_HealthCheck_reuse_connection(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_BoolValue*)upb_msg_new(&google_protobuf_BoolValue_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_reuse_connection(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_http_health_check(envoy_api_v2_core_HealthCheck *msg, envoy_api_v2_core_HealthCheck_HttpHealthCheck* value) {
  UPB_WRITE_ONEOF(msg, envoy_api_v2_core_HealthCheck_HttpHealthCheck*, UPB_SIZE(64, 120), value, UPB_SIZE(68, 128), 8);
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_HttpHealthCheck* envoy_api_v2_core_HealthCheck_mutable_http_health_check(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_HttpHealthCheck* sub = (struct envoy_api_v2_core_HealthCheck_HttpHealthCheck*)envoy_api_v2_core_HealthCheck_http_health_check(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_HttpHealthCheck*)upb_msg_new(&envoy_api_v2_core_HealthCheck_HttpHealthCheck_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_http_health_check(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_tcp_health_check(envoy_api_v2_core_HealthCheck *msg, envoy_api_v2_core_HealthCheck_TcpHealthCheck* value) {
  UPB_WRITE_ONEOF(msg, envoy_api_v2_core_HealthCheck_TcpHealthCheck*, UPB_SIZE(64, 120), value, UPB_SIZE(68, 128), 9);
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_TcpHealthCheck* envoy_api_v2_core_HealthCheck_mutable_tcp_health_check(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_TcpHealthCheck* sub = (struct envoy_api_v2_core_HealthCheck_TcpHealthCheck*)envoy_api_v2_core_HealthCheck_tcp_health_check(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_TcpHealthCheck*)upb_msg_new(&envoy_api_v2_core_HealthCheck_TcpHealthCheck_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_tcp_health_check(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_grpc_health_check(envoy_api_v2_core_HealthCheck *msg, envoy_api_v2_core_HealthCheck_GrpcHealthCheck* value) {
  UPB_WRITE_ONEOF(msg, envoy_api_v2_core_HealthCheck_GrpcHealthCheck*, UPB_SIZE(64, 120), value, UPB_SIZE(68, 128), 11);
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_GrpcHealthCheck* envoy_api_v2_core_HealthCheck_mutable_grpc_health_check(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_GrpcHealthCheck* sub = (struct envoy_api_v2_core_HealthCheck_GrpcHealthCheck*)envoy_api_v2_core_HealthCheck_grpc_health_check(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_GrpcHealthCheck*)upb_msg_new(&envoy_api_v2_core_HealthCheck_GrpcHealthCheck_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_grpc_health_check(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_no_traffic_interval(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(44, 80)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_no_traffic_interval(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_no_traffic_interval(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_no_traffic_interval(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_custom_health_check(envoy_api_v2_core_HealthCheck *msg, envoy_api_v2_core_HealthCheck_CustomHealthCheck* value) {
  UPB_WRITE_ONEOF(msg, envoy_api_v2_core_HealthCheck_CustomHealthCheck*, UPB_SIZE(64, 120), value, UPB_SIZE(68, 128), 13);
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_CustomHealthCheck* envoy_api_v2_core_HealthCheck_mutable_custom_health_check(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_CustomHealthCheck* sub = (struct envoy_api_v2_core_HealthCheck_CustomHealthCheck*)envoy_api_v2_core_HealthCheck_custom_health_check(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_CustomHealthCheck*)upb_msg_new(&envoy_api_v2_core_HealthCheck_CustomHealthCheck_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_custom_health_check(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_unhealthy_interval(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(48, 88)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_unhealthy_interval(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_unhealthy_interval(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_unhealthy_interval(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_unhealthy_edge_interval(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(52, 96)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_unhealthy_edge_interval(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_unhealthy_edge_interval(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_unhealthy_edge_interval(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_healthy_edge_interval(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(56, 104)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_healthy_edge_interval(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_healthy_edge_interval(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_healthy_edge_interval(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_event_log_path(envoy_api_v2_core_HealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 8)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_interval_jitter_percent(envoy_api_v2_core_HealthCheck *msg, uint32_t value) {
  UPB_FIELD_AT(msg, uint32_t, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_always_log_health_check_failures(envoy_api_v2_core_HealthCheck *msg, bool value) {
  UPB_FIELD_AT(msg, bool, UPB_SIZE(4, 4)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_set_initial_jitter(envoy_api_v2_core_HealthCheck *msg, struct google_protobuf_Duration* value) {
  UPB_FIELD_AT(msg, struct google_protobuf_Duration*, UPB_SIZE(60, 112)) = value;
}
UPB_INLINE struct google_protobuf_Duration* envoy_api_v2_core_HealthCheck_mutable_initial_jitter(envoy_api_v2_core_HealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Duration* sub = (struct google_protobuf_Duration*)envoy_api_v2_core_HealthCheck_initial_jitter(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Duration*)upb_msg_new(&google_protobuf_Duration_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_set_initial_jitter(msg, sub);
  }
  return sub;
}

/* envoy.api.v2.core.HealthCheck.Payload */

UPB_INLINE envoy_api_v2_core_HealthCheck_Payload *envoy_api_v2_core_HealthCheck_Payload_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_Payload *)upb_msg_new(&envoy_api_v2_core_HealthCheck_Payload_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_Payload *envoy_api_v2_core_HealthCheck_Payload_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck_Payload *ret = envoy_api_v2_core_HealthCheck_Payload_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_Payload_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_Payload_serialize(const envoy_api_v2_core_HealthCheck_Payload *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_Payload_msginit, arena, len);
}

typedef enum {
  envoy_api_v2_core_HealthCheck_Payload_payload_text = 1,
  envoy_api_v2_core_HealthCheck_Payload_payload_binary = 2,
  envoy_api_v2_core_HealthCheck_Payload_payload_NOT_SET = 0
} envoy_api_v2_core_HealthCheck_Payload_payload_oneofcases;
UPB_INLINE envoy_api_v2_core_HealthCheck_Payload_payload_oneofcases envoy_api_v2_core_HealthCheck_Payload_payload_case(const envoy_api_v2_core_HealthCheck_Payload* msg) { return (envoy_api_v2_core_HealthCheck_Payload_payload_oneofcases)UPB_FIELD_AT(msg, int32_t, UPB_SIZE(8, 16)); }

UPB_INLINE bool envoy_api_v2_core_HealthCheck_Payload_has_text(const envoy_api_v2_core_HealthCheck_Payload *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(8, 16), 1); }
UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_Payload_text(const envoy_api_v2_core_HealthCheck_Payload *msg) { return UPB_READ_ONEOF(msg, upb_strview, UPB_SIZE(0, 0), UPB_SIZE(8, 16), 1, upb_strview_make("", strlen(""))); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_Payload_has_binary(const envoy_api_v2_core_HealthCheck_Payload *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(8, 16), 2); }
UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_Payload_binary(const envoy_api_v2_core_HealthCheck_Payload *msg) { return UPB_READ_ONEOF(msg, upb_strview, UPB_SIZE(0, 0), UPB_SIZE(8, 16), 2, upb_strview_make("", strlen(""))); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_Payload_set_text(envoy_api_v2_core_HealthCheck_Payload *msg, upb_strview value) {
  UPB_WRITE_ONEOF(msg, upb_strview, UPB_SIZE(0, 0), value, UPB_SIZE(8, 16), 1);
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_Payload_set_binary(envoy_api_v2_core_HealthCheck_Payload *msg, upb_strview value) {
  UPB_WRITE_ONEOF(msg, upb_strview, UPB_SIZE(0, 0), value, UPB_SIZE(8, 16), 2);
}

/* envoy.api.v2.core.HealthCheck.HttpHealthCheck */

UPB_INLINE envoy_api_v2_core_HealthCheck_HttpHealthCheck *envoy_api_v2_core_HealthCheck_HttpHealthCheck_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_HttpHealthCheck *)upb_msg_new(&envoy_api_v2_core_HealthCheck_HttpHealthCheck_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_HttpHealthCheck *envoy_api_v2_core_HealthCheck_HttpHealthCheck_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck_HttpHealthCheck *ret = envoy_api_v2_core_HealthCheck_HttpHealthCheck_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_HttpHealthCheck_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_HttpHealthCheck_serialize(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_HttpHealthCheck_msginit, arena, len);
}

UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_HttpHealthCheck_host(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(12, 16)); }
UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_HttpHealthCheck_path(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(20, 32)); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_HttpHealthCheck_send(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, const envoy_api_v2_core_HealthCheck_Payload*, UPB_SIZE(36, 64)); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_HttpHealthCheck_receive(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, const envoy_api_v2_core_HealthCheck_Payload*, UPB_SIZE(40, 72)); }
UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_HttpHealthCheck_service_name(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(28, 48)); }
UPB_INLINE const struct envoy_api_v2_core_HeaderValueOption* const* envoy_api_v2_core_HealthCheck_HttpHealthCheck_request_headers_to_add(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t *len) { return (const struct envoy_api_v2_core_HeaderValueOption* const*)_upb_array_accessor(msg, UPB_SIZE(44, 80), len); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_HttpHealthCheck_use_http2(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, bool, UPB_SIZE(8, 8)); }
UPB_INLINE upb_strview const* envoy_api_v2_core_HealthCheck_HttpHealthCheck_request_headers_to_remove(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t *len) { return (upb_strview const*)_upb_array_accessor(msg, UPB_SIZE(48, 88), len); }
UPB_INLINE const struct envoy_type_Int64Range* const* envoy_api_v2_core_HealthCheck_HttpHealthCheck_expected_statuses(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t *len) { return (const struct envoy_type_Int64Range* const*)_upb_array_accessor(msg, UPB_SIZE(52, 96), len); }
UPB_INLINE int32_t envoy_api_v2_core_HealthCheck_HttpHealthCheck_codec_client_type(const envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg) { return UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_host(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(12, 16)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_path(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(20, 32)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_send(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, envoy_api_v2_core_HealthCheck_Payload* value) {
  UPB_FIELD_AT(msg, envoy_api_v2_core_HealthCheck_Payload*, UPB_SIZE(36, 64)) = value;
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_HttpHealthCheck_mutable_send(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_Payload* sub = (struct envoy_api_v2_core_HealthCheck_Payload*)envoy_api_v2_core_HealthCheck_HttpHealthCheck_send(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_Payload*)upb_msg_new(&envoy_api_v2_core_HealthCheck_Payload_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_send(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_receive(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, envoy_api_v2_core_HealthCheck_Payload* value) {
  UPB_FIELD_AT(msg, envoy_api_v2_core_HealthCheck_Payload*, UPB_SIZE(40, 72)) = value;
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_HttpHealthCheck_mutable_receive(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_Payload* sub = (struct envoy_api_v2_core_HealthCheck_Payload*)envoy_api_v2_core_HealthCheck_HttpHealthCheck_receive(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_Payload*)upb_msg_new(&envoy_api_v2_core_HealthCheck_Payload_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_receive(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_service_name(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(28, 48)) = value;
}
UPB_INLINE struct envoy_api_v2_core_HeaderValueOption** envoy_api_v2_core_HealthCheck_HttpHealthCheck_mutable_request_headers_to_add(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t *len) {
  return (struct envoy_api_v2_core_HeaderValueOption**)_upb_array_mutable_accessor(msg, UPB_SIZE(44, 80), len);
}
UPB_INLINE struct envoy_api_v2_core_HeaderValueOption** envoy_api_v2_core_HealthCheck_HttpHealthCheck_resize_request_headers_to_add(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t len, upb_arena *arena) {
  return (struct envoy_api_v2_core_HeaderValueOption**)_upb_array_resize_accessor(msg, UPB_SIZE(44, 80), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct envoy_api_v2_core_HeaderValueOption* envoy_api_v2_core_HealthCheck_HttpHealthCheck_add_request_headers_to_add(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HeaderValueOption* sub = (struct envoy_api_v2_core_HeaderValueOption*)upb_msg_new(&envoy_api_v2_core_HeaderValueOption_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(44, 80), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_use_http2(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, bool value) {
  UPB_FIELD_AT(msg, bool, UPB_SIZE(8, 8)) = value;
}
UPB_INLINE upb_strview* envoy_api_v2_core_HealthCheck_HttpHealthCheck_mutable_request_headers_to_remove(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t *len) {
  return (upb_strview*)_upb_array_mutable_accessor(msg, UPB_SIZE(48, 88), len);
}
UPB_INLINE upb_strview* envoy_api_v2_core_HealthCheck_HttpHealthCheck_resize_request_headers_to_remove(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t len, upb_arena *arena) {
  return (upb_strview*)_upb_array_resize_accessor(msg, UPB_SIZE(48, 88), len, UPB_SIZE(8, 16), UPB_TYPE_STRING, arena);
}
UPB_INLINE bool envoy_api_v2_core_HealthCheck_HttpHealthCheck_add_request_headers_to_remove(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_strview val, upb_arena *arena) {
  return _upb_array_append_accessor(
      msg, UPB_SIZE(48, 88), UPB_SIZE(8, 16), UPB_TYPE_STRING, &val, arena);
}
UPB_INLINE struct envoy_type_Int64Range** envoy_api_v2_core_HealthCheck_HttpHealthCheck_mutable_expected_statuses(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t *len) {
  return (struct envoy_type_Int64Range**)_upb_array_mutable_accessor(msg, UPB_SIZE(52, 96), len);
}
UPB_INLINE struct envoy_type_Int64Range** envoy_api_v2_core_HealthCheck_HttpHealthCheck_resize_expected_statuses(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, size_t len, upb_arena *arena) {
  return (struct envoy_type_Int64Range**)_upb_array_resize_accessor(msg, UPB_SIZE(52, 96), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct envoy_type_Int64Range* envoy_api_v2_core_HealthCheck_HttpHealthCheck_add_expected_statuses(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, upb_arena *arena) {
  struct envoy_type_Int64Range* sub = (struct envoy_type_Int64Range*)upb_msg_new(&envoy_type_Int64Range_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(52, 96), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_HttpHealthCheck_set_codec_client_type(envoy_api_v2_core_HealthCheck_HttpHealthCheck *msg, int32_t value) {
  UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)) = value;
}

/* envoy.api.v2.core.HealthCheck.TcpHealthCheck */

UPB_INLINE envoy_api_v2_core_HealthCheck_TcpHealthCheck *envoy_api_v2_core_HealthCheck_TcpHealthCheck_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_TcpHealthCheck *)upb_msg_new(&envoy_api_v2_core_HealthCheck_TcpHealthCheck_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_TcpHealthCheck *envoy_api_v2_core_HealthCheck_TcpHealthCheck_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck_TcpHealthCheck *ret = envoy_api_v2_core_HealthCheck_TcpHealthCheck_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_TcpHealthCheck_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_TcpHealthCheck_serialize(const envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_TcpHealthCheck_msginit, arena, len);
}

UPB_INLINE const envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_TcpHealthCheck_send(const envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg) { return UPB_FIELD_AT(msg, const envoy_api_v2_core_HealthCheck_Payload*, UPB_SIZE(0, 0)); }
UPB_INLINE const envoy_api_v2_core_HealthCheck_Payload* const* envoy_api_v2_core_HealthCheck_TcpHealthCheck_receive(const envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, size_t *len) { return (const envoy_api_v2_core_HealthCheck_Payload* const*)_upb_array_accessor(msg, UPB_SIZE(4, 8), len); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_TcpHealthCheck_set_send(envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, envoy_api_v2_core_HealthCheck_Payload* value) {
  UPB_FIELD_AT(msg, envoy_api_v2_core_HealthCheck_Payload*, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_TcpHealthCheck_mutable_send(envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_Payload* sub = (struct envoy_api_v2_core_HealthCheck_Payload*)envoy_api_v2_core_HealthCheck_TcpHealthCheck_send(msg);
  if (sub == NULL) {
    sub = (struct envoy_api_v2_core_HealthCheck_Payload*)upb_msg_new(&envoy_api_v2_core_HealthCheck_Payload_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_TcpHealthCheck_set_send(msg, sub);
  }
  return sub;
}
UPB_INLINE envoy_api_v2_core_HealthCheck_Payload** envoy_api_v2_core_HealthCheck_TcpHealthCheck_mutable_receive(envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, size_t *len) {
  return (envoy_api_v2_core_HealthCheck_Payload**)_upb_array_mutable_accessor(msg, UPB_SIZE(4, 8), len);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_Payload** envoy_api_v2_core_HealthCheck_TcpHealthCheck_resize_receive(envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, size_t len, upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_Payload**)_upb_array_resize_accessor(msg, UPB_SIZE(4, 8), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct envoy_api_v2_core_HealthCheck_Payload* envoy_api_v2_core_HealthCheck_TcpHealthCheck_add_receive(envoy_api_v2_core_HealthCheck_TcpHealthCheck *msg, upb_arena *arena) {
  struct envoy_api_v2_core_HealthCheck_Payload* sub = (struct envoy_api_v2_core_HealthCheck_Payload*)upb_msg_new(&envoy_api_v2_core_HealthCheck_Payload_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(4, 8), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}

/* envoy.api.v2.core.HealthCheck.RedisHealthCheck */

UPB_INLINE envoy_api_v2_core_HealthCheck_RedisHealthCheck *envoy_api_v2_core_HealthCheck_RedisHealthCheck_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_RedisHealthCheck *)upb_msg_new(&envoy_api_v2_core_HealthCheck_RedisHealthCheck_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_RedisHealthCheck *envoy_api_v2_core_HealthCheck_RedisHealthCheck_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck_RedisHealthCheck *ret = envoy_api_v2_core_HealthCheck_RedisHealthCheck_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_RedisHealthCheck_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_RedisHealthCheck_serialize(const envoy_api_v2_core_HealthCheck_RedisHealthCheck *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_RedisHealthCheck_msginit, arena, len);
}

UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_RedisHealthCheck_key(const envoy_api_v2_core_HealthCheck_RedisHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_RedisHealthCheck_set_key(envoy_api_v2_core_HealthCheck_RedisHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}

/* envoy.api.v2.core.HealthCheck.GrpcHealthCheck */

UPB_INLINE envoy_api_v2_core_HealthCheck_GrpcHealthCheck *envoy_api_v2_core_HealthCheck_GrpcHealthCheck_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_GrpcHealthCheck *)upb_msg_new(&envoy_api_v2_core_HealthCheck_GrpcHealthCheck_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_GrpcHealthCheck *envoy_api_v2_core_HealthCheck_GrpcHealthCheck_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck_GrpcHealthCheck *ret = envoy_api_v2_core_HealthCheck_GrpcHealthCheck_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_GrpcHealthCheck_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_GrpcHealthCheck_serialize(const envoy_api_v2_core_HealthCheck_GrpcHealthCheck *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_GrpcHealthCheck_msginit, arena, len);
}

UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_GrpcHealthCheck_service_name(const envoy_api_v2_core_HealthCheck_GrpcHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }
UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_GrpcHealthCheck_authority(const envoy_api_v2_core_HealthCheck_GrpcHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 16)); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_GrpcHealthCheck_set_service_name(envoy_api_v2_core_HealthCheck_GrpcHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_GrpcHealthCheck_set_authority(envoy_api_v2_core_HealthCheck_GrpcHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 16)) = value;
}

/* envoy.api.v2.core.HealthCheck.CustomHealthCheck */

UPB_INLINE envoy_api_v2_core_HealthCheck_CustomHealthCheck *envoy_api_v2_core_HealthCheck_CustomHealthCheck_new(upb_arena *arena) {
  return (envoy_api_v2_core_HealthCheck_CustomHealthCheck *)upb_msg_new(&envoy_api_v2_core_HealthCheck_CustomHealthCheck_msginit, arena);
}
UPB_INLINE envoy_api_v2_core_HealthCheck_CustomHealthCheck *envoy_api_v2_core_HealthCheck_CustomHealthCheck_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  envoy_api_v2_core_HealthCheck_CustomHealthCheck *ret = envoy_api_v2_core_HealthCheck_CustomHealthCheck_new(arena);
  return (ret && upb_decode(buf, size, ret, &envoy_api_v2_core_HealthCheck_CustomHealthCheck_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *envoy_api_v2_core_HealthCheck_CustomHealthCheck_serialize(const envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &envoy_api_v2_core_HealthCheck_CustomHealthCheck_msginit, arena, len);
}

typedef enum {
  envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_config = 2,
  envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_typed_config = 3,
  envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_NOT_SET = 0
} envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_oneofcases;
UPB_INLINE envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_oneofcases envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_case(const envoy_api_v2_core_HealthCheck_CustomHealthCheck* msg) { return (envoy_api_v2_core_HealthCheck_CustomHealthCheck_config_type_oneofcases)UPB_FIELD_AT(msg, int32_t, UPB_SIZE(12, 24)); }

UPB_INLINE upb_strview envoy_api_v2_core_HealthCheck_CustomHealthCheck_name(const envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_CustomHealthCheck_has_config(const envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(12, 24), 2); }
UPB_INLINE const struct google_protobuf_Struct* envoy_api_v2_core_HealthCheck_CustomHealthCheck_config(const envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg) { return UPB_READ_ONEOF(msg, const struct google_protobuf_Struct*, UPB_SIZE(8, 16), UPB_SIZE(12, 24), 2, NULL); }
UPB_INLINE bool envoy_api_v2_core_HealthCheck_CustomHealthCheck_has_typed_config(const envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(12, 24), 3); }
UPB_INLINE const struct google_protobuf_Any* envoy_api_v2_core_HealthCheck_CustomHealthCheck_typed_config(const envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg) { return UPB_READ_ONEOF(msg, const struct google_protobuf_Any*, UPB_SIZE(8, 16), UPB_SIZE(12, 24), 3, NULL); }

UPB_INLINE void envoy_api_v2_core_HealthCheck_CustomHealthCheck_set_name(envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_CustomHealthCheck_set_config(envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg, struct google_protobuf_Struct* value) {
  UPB_WRITE_ONEOF(msg, struct google_protobuf_Struct*, UPB_SIZE(8, 16), value, UPB_SIZE(12, 24), 2);
}
UPB_INLINE struct google_protobuf_Struct* envoy_api_v2_core_HealthCheck_CustomHealthCheck_mutable_config(envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Struct* sub = (struct google_protobuf_Struct*)envoy_api_v2_core_HealthCheck_CustomHealthCheck_config(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Struct*)upb_msg_new(&google_protobuf_Struct_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_CustomHealthCheck_set_config(msg, sub);
  }
  return sub;
}
UPB_INLINE void envoy_api_v2_core_HealthCheck_CustomHealthCheck_set_typed_config(envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg, struct google_protobuf_Any* value) {
  UPB_WRITE_ONEOF(msg, struct google_protobuf_Any*, UPB_SIZE(8, 16), value, UPB_SIZE(12, 24), 3);
}
UPB_INLINE struct google_protobuf_Any* envoy_api_v2_core_HealthCheck_CustomHealthCheck_mutable_typed_config(envoy_api_v2_core_HealthCheck_CustomHealthCheck *msg, upb_arena *arena) {
  struct google_protobuf_Any* sub = (struct google_protobuf_Any*)envoy_api_v2_core_HealthCheck_CustomHealthCheck_typed_config(msg);
  if (sub == NULL) {
    sub = (struct google_protobuf_Any*)upb_msg_new(&google_protobuf_Any_msginit, arena);
    if (!sub) return NULL;
    envoy_api_v2_core_HealthCheck_CustomHealthCheck_set_typed_config(msg, sub);
  }
  return sub;
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port_undef.inc"

#endif  /* ENVOY_API_V2_CORE_HEALTH_CHECK_PROTO_UPB_H_ */
