/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CONVERTTFQUANTOPSTOMHLO
#define GEN_PASS_DECL_CONVERTTFQUANTTYPES
#define GEN_PASS_DECL_OPTIMIZEINTGRAPH
#define GEN_PASS_DECL_VERIFYQUANTLEGALIZATION
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ConvertTFQuantOpsToMHLO
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTFQUANTOPSTOMHLO
#undef GEN_PASS_DECL_CONVERTTFQUANTOPSTOMHLO
#endif // GEN_PASS_DECL_CONVERTTFQUANTOPSTOMHLO
#ifdef GEN_PASS_DEF_CONVERTTFQUANTOPSTOMHLO
namespace impl {

template <typename DerivedT>
class ConvertTFQuantOpsToMHLOBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ConvertTFQuantOpsToMHLOBase;

  ConvertTFQuantOpsToMHLOBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTFQuantOpsToMHLOBase(const ConvertTFQuantOpsToMHLOBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  ConvertTFQuantOpsToMHLOBase& operator=(const ConvertTFQuantOpsToMHLOBase &) = delete;
  ConvertTFQuantOpsToMHLOBase(ConvertTFQuantOpsToMHLOBase &&) = delete;
  ConvertTFQuantOpsToMHLOBase& operator=(ConvertTFQuantOpsToMHLOBase &&) = delete;
  ~ConvertTFQuantOpsToMHLOBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-tf-quant-ops-to-mhlo");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-tf-quant-ops-to-mhlo"; }

  ::llvm::StringRef getDescription() const override { return "Convert TF Quant ops to MHLO quantizated ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTFQuantOpsToMHLO");
  }
  ::llvm::StringRef getName() const override { return "ConvertTFQuantOpsToMHLO"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<TF::TensorFlowDialect>();
    registry.insert<chlo::ChloDialect>();
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_type::TFTypeDialect>();
    registry.insert<quant::QuantDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTFQuantOpsToMHLOBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTFQUANTOPSTOMHLO
#endif // GEN_PASS_DEF_CONVERTTFQUANTOPSTOMHLO

//===----------------------------------------------------------------------===//
// ConvertTFQuantTypes
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTFQUANTTYPES
#undef GEN_PASS_DECL_CONVERTTFQUANTTYPES
#endif // GEN_PASS_DECL_CONVERTTFQUANTTYPES
#ifdef GEN_PASS_DEF_CONVERTTFQUANTTYPES
namespace impl {

template <typename DerivedT>
class ConvertTFQuantTypesBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ConvertTFQuantTypesBase;

  ConvertTFQuantTypesBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTFQuantTypesBase(const ConvertTFQuantTypesBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  ConvertTFQuantTypesBase& operator=(const ConvertTFQuantTypesBase &) = delete;
  ConvertTFQuantTypesBase(ConvertTFQuantTypesBase &&) = delete;
  ConvertTFQuantTypesBase& operator=(ConvertTFQuantTypesBase &&) = delete;
  ~ConvertTFQuantTypesBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tf-quant-types");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tf-quant-types"; }

  ::llvm::StringRef getDescription() const override { return "Replace TensorFlow qint types with int types."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTFQuantTypes");
  }
  ::llvm::StringRef getName() const override { return "ConvertTFQuantTypes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<TF::TensorFlowDialect>();
    registry.insert<tf_type::TFTypeDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTFQuantTypesBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTFQUANTTYPES
#endif // GEN_PASS_DEF_CONVERTTFQUANTTYPES

//===----------------------------------------------------------------------===//
// OptimizeIntGraph
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_OPTIMIZEINTGRAPH
#undef GEN_PASS_DECL_OPTIMIZEINTGRAPH
#endif // GEN_PASS_DECL_OPTIMIZEINTGRAPH
#ifdef GEN_PASS_DEF_OPTIMIZEINTGRAPH
namespace impl {

template <typename DerivedT>
class OptimizeIntGraphBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = OptimizeIntGraphBase;

  OptimizeIntGraphBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeIntGraphBase(const OptimizeIntGraphBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  OptimizeIntGraphBase& operator=(const OptimizeIntGraphBase &) = delete;
  OptimizeIntGraphBase(OptimizeIntGraphBase &&) = delete;
  OptimizeIntGraphBase& operator=(OptimizeIntGraphBase &&) = delete;
  ~OptimizeIntGraphBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("optimize-int-graph");
  }
  ::llvm::StringRef getArgument() const override { return "optimize-int-graph"; }

  ::llvm::StringRef getDescription() const override { return "Optimization patterns for quantized integer graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeIntGraph");
  }
  ::llvm::StringRef getName() const override { return "OptimizeIntGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OptimizeIntGraphBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_OPTIMIZEINTGRAPH
#endif // GEN_PASS_DEF_OPTIMIZEINTGRAPH

//===----------------------------------------------------------------------===//
// VerifyQuantLegalization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VERIFYQUANTLEGALIZATION
#undef GEN_PASS_DECL_VERIFYQUANTLEGALIZATION
#endif // GEN_PASS_DECL_VERIFYQUANTLEGALIZATION
#ifdef GEN_PASS_DEF_VERIFYQUANTLEGALIZATION
namespace impl {

template <typename DerivedT>
class VerifyQuantLegalizationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyQuantLegalizationBase;

  VerifyQuantLegalizationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyQuantLegalizationBase(const VerifyQuantLegalizationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyQuantLegalizationBase& operator=(const VerifyQuantLegalizationBase &) = delete;
  VerifyQuantLegalizationBase(VerifyQuantLegalizationBase &&) = delete;
  VerifyQuantLegalizationBase& operator=(VerifyQuantLegalizationBase &&) = delete;
  ~VerifyQuantLegalizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("verify-quant-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "verify-quant-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that all TF quant ops and types have been legalized."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyQuantLegalization");
  }
  ::llvm::StringRef getName() const override { return "VerifyQuantLegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_type::TFTypeDialect>();
    registry.insert<quant::QuantDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyQuantLegalizationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VERIFYQUANTLEGALIZATION
#endif // GEN_PASS_DEF_VERIFYQUANTLEGALIZATION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertTFQuantOpsToMHLO Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTFQuantOpsToMHLO() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateConvertTFQuantOpsToMHLOPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertTFQuantOpsToMHLOPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateConvertTFQuantOpsToMHLOPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertTFQuantTypes Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTFQuantTypes() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateConvertTFQuantTypesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertTFQuantTypesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateConvertTFQuantTypesPass();
  });
}

//===----------------------------------------------------------------------===//
// OptimizeIntGraph Registration
//===----------------------------------------------------------------------===//

inline void registerOptimizeIntGraph() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateOptimizeIntGraphPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOptimizeIntGraphPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateOptimizeIntGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// VerifyQuantLegalization Registration
//===----------------------------------------------------------------------===//

inline void registerVerifyQuantLegalization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateVerifyQuantLegalizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVerifyQuantLegalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quant::stablehlo::CreateVerifyQuantLegalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// Bridge Registration
//===----------------------------------------------------------------------===//

inline void registerBridgePasses() {
  registerConvertTFQuantOpsToMHLO();
  registerConvertTFQuantTypes();
  registerOptimizeIntGraph();
  registerVerifyQuantLegalization();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ConvertTFQuantOpsToMHLOBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ConvertTFQuantOpsToMHLOBase;

  ConvertTFQuantOpsToMHLOBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTFQuantOpsToMHLOBase(const ConvertTFQuantOpsToMHLOBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  ConvertTFQuantOpsToMHLOBase& operator=(const ConvertTFQuantOpsToMHLOBase &) = delete;
  ConvertTFQuantOpsToMHLOBase(ConvertTFQuantOpsToMHLOBase &&) = delete;
  ConvertTFQuantOpsToMHLOBase& operator=(ConvertTFQuantOpsToMHLOBase &&) = delete;
  ~ConvertTFQuantOpsToMHLOBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-tf-quant-ops-to-mhlo");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-tf-quant-ops-to-mhlo"; }

  ::llvm::StringRef getDescription() const override { return "Convert TF Quant ops to MHLO quantizated ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTFQuantOpsToMHLO");
  }
  ::llvm::StringRef getName() const override { return "ConvertTFQuantOpsToMHLO"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<TF::TensorFlowDialect>();
    registry.insert<chlo::ChloDialect>();
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_type::TFTypeDialect>();
    registry.insert<quant::QuantDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTFQuantOpsToMHLOBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertTFQuantTypesBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ConvertTFQuantTypesBase;

  ConvertTFQuantTypesBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTFQuantTypesBase(const ConvertTFQuantTypesBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  ConvertTFQuantTypesBase& operator=(const ConvertTFQuantTypesBase &) = delete;
  ConvertTFQuantTypesBase(ConvertTFQuantTypesBase &&) = delete;
  ConvertTFQuantTypesBase& operator=(ConvertTFQuantTypesBase &&) = delete;
  ~ConvertTFQuantTypesBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tf-quant-types");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tf-quant-types"; }

  ::llvm::StringRef getDescription() const override { return "Replace TensorFlow qint types with int types."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTFQuantTypes");
  }
  ::llvm::StringRef getName() const override { return "ConvertTFQuantTypes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<TF::TensorFlowDialect>();
    registry.insert<tf_type::TFTypeDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTFQuantTypesBase<DerivedT>)

protected:
};

template <typename DerivedT>
class OptimizeIntGraphBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = OptimizeIntGraphBase;

  OptimizeIntGraphBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeIntGraphBase(const OptimizeIntGraphBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  OptimizeIntGraphBase& operator=(const OptimizeIntGraphBase &) = delete;
  OptimizeIntGraphBase(OptimizeIntGraphBase &&) = delete;
  OptimizeIntGraphBase& operator=(OptimizeIntGraphBase &&) = delete;
  ~OptimizeIntGraphBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("optimize-int-graph");
  }
  ::llvm::StringRef getArgument() const override { return "optimize-int-graph"; }

  ::llvm::StringRef getDescription() const override { return "Optimization patterns for quantized integer graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeIntGraph");
  }
  ::llvm::StringRef getName() const override { return "OptimizeIntGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OptimizeIntGraphBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VerifyQuantLegalizationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyQuantLegalizationBase;

  VerifyQuantLegalizationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyQuantLegalizationBase(const VerifyQuantLegalizationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyQuantLegalizationBase& operator=(const VerifyQuantLegalizationBase &) = delete;
  VerifyQuantLegalizationBase(VerifyQuantLegalizationBase &&) = delete;
  VerifyQuantLegalizationBase& operator=(VerifyQuantLegalizationBase &&) = delete;
  ~VerifyQuantLegalizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("verify-quant-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "verify-quant-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that all TF quant ops and types have been legalized."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyQuantLegalization");
  }
  ::llvm::StringRef getName() const override { return "VerifyQuantLegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_type::TFTypeDialect>();
    registry.insert<quant::QuantDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyQuantLegalizationBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
