# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

ASSETS_DIRECTORY: str
ASSETS_KEY: str
DEBUG_DIRECTORY: str
DEBUG_INFO_FILENAME_PB: str
EXTRA_ASSETS_DIRECTORY: str
FINGERPRINT_FILENAME: str
INIT_OP_SIGNATURE_KEY: str
LEGACY_INIT_OP_KEY: str
MAIN_OP_KEY: str
SAVED_MODEL_FILENAME_CPB: str
SAVED_MODEL_FILENAME_PB: str
SAVED_MODEL_FILENAME_PBTXT: str
SAVED_MODEL_FILENAME_PREFIX: str
SAVED_MODEL_SCHEMA_VERSION: int
TRAIN_OP_KEY: str
TRAIN_OP_SIGNATURE_KEY: str
VARIABLES_DIRECTORY: str
VARIABLES_FILENAME: str
