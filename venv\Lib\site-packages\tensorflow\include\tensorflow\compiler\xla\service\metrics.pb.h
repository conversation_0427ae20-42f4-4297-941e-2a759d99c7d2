// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/metrics.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fmetrics_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fmetrics_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/timestamp.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fmetrics_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fmetrics_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fmetrics_2eproto;
namespace xla {
class CompilationLogEntry;
struct CompilationLogEntryDefaultTypeInternal;
extern CompilationLogEntryDefaultTypeInternal _CompilationLogEntry_default_instance_;
class JobInfo;
struct JobInfoDefaultTypeInternal;
extern JobInfoDefaultTypeInternal _JobInfo_default_instance_;
class KeyValueMetric;
struct KeyValueMetricDefaultTypeInternal;
extern KeyValueMetricDefaultTypeInternal _KeyValueMetric_default_instance_;
class PassMetrics;
struct PassMetricsDefaultTypeInternal;
extern PassMetricsDefaultTypeInternal _PassMetrics_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::CompilationLogEntry* Arena::CreateMaybeMessage<::xla::CompilationLogEntry>(Arena*);
template<> ::xla::JobInfo* Arena::CreateMaybeMessage<::xla::JobInfo>(Arena*);
template<> ::xla::KeyValueMetric* Arena::CreateMaybeMessage<::xla::KeyValueMetric>(Arena*);
template<> ::xla::PassMetrics* Arena::CreateMaybeMessage<::xla::PassMetrics>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

enum CompilationLogEntry_CompilationStage : int {
  CompilationLogEntry_CompilationStage_UNSPECIFIED = 0,
  CompilationLogEntry_CompilationStage_END_TO_END = 1,
  CompilationLogEntry_CompilationStage_HLO_PASSES = 2,
  CompilationLogEntry_CompilationStage_CODE_GENERATION = 3,
  CompilationLogEntry_CompilationStage_BACKEND_PASSES = 4,
  CompilationLogEntry_CompilationStage_CompilationLogEntry_CompilationStage_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CompilationLogEntry_CompilationStage_CompilationLogEntry_CompilationStage_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CompilationLogEntry_CompilationStage_IsValid(int value);
constexpr CompilationLogEntry_CompilationStage CompilationLogEntry_CompilationStage_CompilationStage_MIN = CompilationLogEntry_CompilationStage_UNSPECIFIED;
constexpr CompilationLogEntry_CompilationStage CompilationLogEntry_CompilationStage_CompilationStage_MAX = CompilationLogEntry_CompilationStage_BACKEND_PASSES;
constexpr int CompilationLogEntry_CompilationStage_CompilationStage_ARRAYSIZE = CompilationLogEntry_CompilationStage_CompilationStage_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CompilationLogEntry_CompilationStage_descriptor();
template<typename T>
inline const std::string& CompilationLogEntry_CompilationStage_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CompilationLogEntry_CompilationStage>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CompilationLogEntry_CompilationStage_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CompilationLogEntry_CompilationStage_descriptor(), enum_t_value);
}
inline bool CompilationLogEntry_CompilationStage_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CompilationLogEntry_CompilationStage* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CompilationLogEntry_CompilationStage>(
    CompilationLogEntry_CompilationStage_descriptor(), name, value);
}
// ===================================================================

class KeyValueMetric final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.KeyValueMetric) */ {
 public:
  inline KeyValueMetric() : KeyValueMetric(nullptr) {}
  ~KeyValueMetric() override;
  explicit PROTOBUF_CONSTEXPR KeyValueMetric(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KeyValueMetric(const KeyValueMetric& from);
  KeyValueMetric(KeyValueMetric&& from) noexcept
    : KeyValueMetric() {
    *this = ::std::move(from);
  }

  inline KeyValueMetric& operator=(const KeyValueMetric& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeyValueMetric& operator=(KeyValueMetric&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KeyValueMetric& default_instance() {
    return *internal_default_instance();
  }
  static inline const KeyValueMetric* internal_default_instance() {
    return reinterpret_cast<const KeyValueMetric*>(
               &_KeyValueMetric_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(KeyValueMetric& a, KeyValueMetric& b) {
    a.Swap(&b);
  }
  inline void Swap(KeyValueMetric* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KeyValueMetric* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KeyValueMetric* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KeyValueMetric>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KeyValueMetric& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KeyValueMetric& from) {
    KeyValueMetric::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeyValueMetric* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.KeyValueMetric";
  }
  protected:
  explicit KeyValueMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // int64 value = 2;
  void clear_value();
  int64_t value() const;
  void set_value(int64_t value);
  private:
  int64_t _internal_value() const;
  void _internal_set_value(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.KeyValueMetric)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    int64_t value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class PassMetrics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.PassMetrics) */ {
 public:
  inline PassMetrics() : PassMetrics(nullptr) {}
  ~PassMetrics() override;
  explicit PROTOBUF_CONSTEXPR PassMetrics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PassMetrics(const PassMetrics& from);
  PassMetrics(PassMetrics&& from) noexcept
    : PassMetrics() {
    *this = ::std::move(from);
  }

  inline PassMetrics& operator=(const PassMetrics& from) {
    CopyFrom(from);
    return *this;
  }
  inline PassMetrics& operator=(PassMetrics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PassMetrics& default_instance() {
    return *internal_default_instance();
  }
  static inline const PassMetrics* internal_default_instance() {
    return reinterpret_cast<const PassMetrics*>(
               &_PassMetrics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PassMetrics& a, PassMetrics& b) {
    a.Swap(&b);
  }
  inline void Swap(PassMetrics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PassMetrics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PassMetrics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PassMetrics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PassMetrics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PassMetrics& from) {
    PassMetrics::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PassMetrics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.PassMetrics";
  }
  protected:
  explicit PassMetrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKvMetricsFieldNumber = 5,
    kPassNameFieldNumber = 2,
    kPassDurationFieldNumber = 3,
    kCustomMetricsFieldNumber = 4,
    kModuleIdFieldNumber = 1,
  };
  // repeated .xla.KeyValueMetric kv_metrics = 5;
  int kv_metrics_size() const;
  private:
  int _internal_kv_metrics_size() const;
  public:
  void clear_kv_metrics();
  ::xla::KeyValueMetric* mutable_kv_metrics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::KeyValueMetric >*
      mutable_kv_metrics();
  private:
  const ::xla::KeyValueMetric& _internal_kv_metrics(int index) const;
  ::xla::KeyValueMetric* _internal_add_kv_metrics();
  public:
  const ::xla::KeyValueMetric& kv_metrics(int index) const;
  ::xla::KeyValueMetric* add_kv_metrics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::KeyValueMetric >&
      kv_metrics() const;

  // string pass_name = 2;
  void clear_pass_name();
  const std::string& pass_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pass_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pass_name();
  PROTOBUF_NODISCARD std::string* release_pass_name();
  void set_allocated_pass_name(std::string* pass_name);
  private:
  const std::string& _internal_pass_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pass_name(const std::string& value);
  std::string* _internal_mutable_pass_name();
  public:

  // .google.protobuf.Duration pass_duration = 3;
  bool has_pass_duration() const;
  private:
  bool _internal_has_pass_duration() const;
  public:
  void clear_pass_duration();
  const ::PROTOBUF_NAMESPACE_ID::Duration& pass_duration() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Duration* release_pass_duration();
  ::PROTOBUF_NAMESPACE_ID::Duration* mutable_pass_duration();
  void set_allocated_pass_duration(::PROTOBUF_NAMESPACE_ID::Duration* pass_duration);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Duration& _internal_pass_duration() const;
  ::PROTOBUF_NAMESPACE_ID::Duration* _internal_mutable_pass_duration();
  public:
  void unsafe_arena_set_allocated_pass_duration(
      ::PROTOBUF_NAMESPACE_ID::Duration* pass_duration);
  ::PROTOBUF_NAMESPACE_ID::Duration* unsafe_arena_release_pass_duration();

  // .google.protobuf.Any custom_metrics = 4;
  bool has_custom_metrics() const;
  private:
  bool _internal_has_custom_metrics() const;
  public:
  void clear_custom_metrics();
  const ::PROTOBUF_NAMESPACE_ID::Any& custom_metrics() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_custom_metrics();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_custom_metrics();
  void set_allocated_custom_metrics(::PROTOBUF_NAMESPACE_ID::Any* custom_metrics);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_custom_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_custom_metrics();
  public:
  void unsafe_arena_set_allocated_custom_metrics(
      ::PROTOBUF_NAMESPACE_ID::Any* custom_metrics);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_custom_metrics();

  // uint64 module_id = 1;
  void clear_module_id();
  uint64_t module_id() const;
  void set_module_id(uint64_t value);
  private:
  uint64_t _internal_module_id() const;
  void _internal_set_module_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.PassMetrics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::KeyValueMetric > kv_metrics_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pass_name_;
    ::PROTOBUF_NAMESPACE_ID::Duration* pass_duration_;
    ::PROTOBUF_NAMESPACE_ID::Any* custom_metrics_;
    uint64_t module_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class JobInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.JobInfo) */ {
 public:
  inline JobInfo() : JobInfo(nullptr) {}
  ~JobInfo() override;
  explicit PROTOBUF_CONSTEXPR JobInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JobInfo(const JobInfo& from);
  JobInfo(JobInfo&& from) noexcept
    : JobInfo() {
    *this = ::std::move(from);
  }

  inline JobInfo& operator=(const JobInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline JobInfo& operator=(JobInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JobInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const JobInfo* internal_default_instance() {
    return reinterpret_cast<const JobInfo*>(
               &_JobInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(JobInfo& a, JobInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(JobInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JobInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JobInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JobInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JobInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const JobInfo& from) {
    JobInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JobInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.JobInfo";
  }
  protected:
  explicit JobInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kCellFieldNumber = 2,
    kUserFieldNumber = 3,
    kUidFieldNumber = 4,
    kTaskIdFieldNumber = 5,
    kTaskUidFieldNumber = 6,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string cell = 2;
  bool has_cell() const;
  private:
  bool _internal_has_cell() const;
  public:
  void clear_cell();
  const std::string& cell() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cell(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cell();
  PROTOBUF_NODISCARD std::string* release_cell();
  void set_allocated_cell(std::string* cell);
  private:
  const std::string& _internal_cell() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cell(const std::string& value);
  std::string* _internal_mutable_cell();
  public:

  // optional string user = 3;
  bool has_user() const;
  private:
  bool _internal_has_user() const;
  public:
  void clear_user();
  const std::string& user() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_user(ArgT0&& arg0, ArgT... args);
  std::string* mutable_user();
  PROTOBUF_NODISCARD std::string* release_user();
  void set_allocated_user(std::string* user);
  private:
  const std::string& _internal_user() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_user(const std::string& value);
  std::string* _internal_mutable_user();
  public:

  // optional int64 uid = 4;
  bool has_uid() const;
  private:
  bool _internal_has_uid() const;
  public:
  void clear_uid();
  int64_t uid() const;
  void set_uid(int64_t value);
  private:
  int64_t _internal_uid() const;
  void _internal_set_uid(int64_t value);
  public:

  // optional int64 task_id = 5;
  bool has_task_id() const;
  private:
  bool _internal_has_task_id() const;
  public:
  void clear_task_id();
  int64_t task_id() const;
  void set_task_id(int64_t value);
  private:
  int64_t _internal_task_id() const;
  void _internal_set_task_id(int64_t value);
  public:

  // optional int64 task_uid = 6;
  bool has_task_uid() const;
  private:
  bool _internal_has_task_uid() const;
  public:
  void clear_task_uid();
  int64_t task_uid() const;
  void set_task_uid(int64_t value);
  private:
  int64_t _internal_task_uid() const;
  void _internal_set_task_uid(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.JobInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cell_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr user_;
    int64_t uid_;
    int64_t task_id_;
    int64_t task_uid_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class CompilationLogEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CompilationLogEntry) */ {
 public:
  inline CompilationLogEntry() : CompilationLogEntry(nullptr) {}
  ~CompilationLogEntry() override;
  explicit PROTOBUF_CONSTEXPR CompilationLogEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompilationLogEntry(const CompilationLogEntry& from);
  CompilationLogEntry(CompilationLogEntry&& from) noexcept
    : CompilationLogEntry() {
    *this = ::std::move(from);
  }

  inline CompilationLogEntry& operator=(const CompilationLogEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompilationLogEntry& operator=(CompilationLogEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompilationLogEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompilationLogEntry* internal_default_instance() {
    return reinterpret_cast<const CompilationLogEntry*>(
               &_CompilationLogEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CompilationLogEntry& a, CompilationLogEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(CompilationLogEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompilationLogEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompilationLogEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompilationLogEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompilationLogEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompilationLogEntry& from) {
    CompilationLogEntry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompilationLogEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CompilationLogEntry";
  }
  protected:
  explicit CompilationLogEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CompilationLogEntry_CompilationStage CompilationStage;
  static constexpr CompilationStage UNSPECIFIED =
    CompilationLogEntry_CompilationStage_UNSPECIFIED;
  static constexpr CompilationStage END_TO_END =
    CompilationLogEntry_CompilationStage_END_TO_END;
  static constexpr CompilationStage HLO_PASSES =
    CompilationLogEntry_CompilationStage_HLO_PASSES;
  static constexpr CompilationStage CODE_GENERATION =
    CompilationLogEntry_CompilationStage_CODE_GENERATION;
  static constexpr CompilationStage BACKEND_PASSES =
    CompilationLogEntry_CompilationStage_BACKEND_PASSES;
  static inline bool CompilationStage_IsValid(int value) {
    return CompilationLogEntry_CompilationStage_IsValid(value);
  }
  static constexpr CompilationStage CompilationStage_MIN =
    CompilationLogEntry_CompilationStage_CompilationStage_MIN;
  static constexpr CompilationStage CompilationStage_MAX =
    CompilationLogEntry_CompilationStage_CompilationStage_MAX;
  static constexpr int CompilationStage_ARRAYSIZE =
    CompilationLogEntry_CompilationStage_CompilationStage_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CompilationStage_descriptor() {
    return CompilationLogEntry_CompilationStage_descriptor();
  }
  template<typename T>
  static inline const std::string& CompilationStage_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CompilationStage>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CompilationStage_Name.");
    return CompilationLogEntry_CompilationStage_Name(enum_t_value);
  }
  static inline bool CompilationStage_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      CompilationStage* value) {
    return CompilationLogEntry_CompilationStage_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kPassMetricsFieldNumber = 5,
    kModuleIdsFieldNumber = 6,
    kTimestampFieldNumber = 1,
    kDurationFieldNumber = 3,
    kJobInfoFieldNumber = 7,
    kStageFieldNumber = 2,
    kTaskIndexFieldNumber = 4,
  };
  // repeated .xla.PassMetrics pass_metrics = 5;
  int pass_metrics_size() const;
  private:
  int _internal_pass_metrics_size() const;
  public:
  void clear_pass_metrics();
  ::xla::PassMetrics* mutable_pass_metrics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PassMetrics >*
      mutable_pass_metrics();
  private:
  const ::xla::PassMetrics& _internal_pass_metrics(int index) const;
  ::xla::PassMetrics* _internal_add_pass_metrics();
  public:
  const ::xla::PassMetrics& pass_metrics(int index) const;
  ::xla::PassMetrics* add_pass_metrics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PassMetrics >&
      pass_metrics() const;

  // repeated uint64 module_ids = 6;
  int module_ids_size() const;
  private:
  int _internal_module_ids_size() const;
  public:
  void clear_module_ids();
  private:
  uint64_t _internal_module_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_module_ids() const;
  void _internal_add_module_ids(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_module_ids();
  public:
  uint64_t module_ids(int index) const;
  void set_module_ids(int index, uint64_t value);
  void add_module_ids(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      module_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_module_ids();

  // .google.protobuf.Timestamp timestamp = 1;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& timestamp() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_timestamp();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_timestamp() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_timestamp();
  public:
  void unsafe_arena_set_allocated_timestamp(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_timestamp();

  // .google.protobuf.Duration duration = 3;
  bool has_duration() const;
  private:
  bool _internal_has_duration() const;
  public:
  void clear_duration();
  const ::PROTOBUF_NAMESPACE_ID::Duration& duration() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Duration* release_duration();
  ::PROTOBUF_NAMESPACE_ID::Duration* mutable_duration();
  void set_allocated_duration(::PROTOBUF_NAMESPACE_ID::Duration* duration);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Duration& _internal_duration() const;
  ::PROTOBUF_NAMESPACE_ID::Duration* _internal_mutable_duration();
  public:
  void unsafe_arena_set_allocated_duration(
      ::PROTOBUF_NAMESPACE_ID::Duration* duration);
  ::PROTOBUF_NAMESPACE_ID::Duration* unsafe_arena_release_duration();

  // .xla.JobInfo job_info = 7;
  bool has_job_info() const;
  private:
  bool _internal_has_job_info() const;
  public:
  void clear_job_info();
  const ::xla::JobInfo& job_info() const;
  PROTOBUF_NODISCARD ::xla::JobInfo* release_job_info();
  ::xla::JobInfo* mutable_job_info();
  void set_allocated_job_info(::xla::JobInfo* job_info);
  private:
  const ::xla::JobInfo& _internal_job_info() const;
  ::xla::JobInfo* _internal_mutable_job_info();
  public:
  void unsafe_arena_set_allocated_job_info(
      ::xla::JobInfo* job_info);
  ::xla::JobInfo* unsafe_arena_release_job_info();

  // .xla.CompilationLogEntry.CompilationStage stage = 2;
  void clear_stage();
  ::xla::CompilationLogEntry_CompilationStage stage() const;
  void set_stage(::xla::CompilationLogEntry_CompilationStage value);
  private:
  ::xla::CompilationLogEntry_CompilationStage _internal_stage() const;
  void _internal_set_stage(::xla::CompilationLogEntry_CompilationStage value);
  public:

  // int32 task_index = 4;
  void clear_task_index();
  int32_t task_index() const;
  void set_task_index(int32_t value);
  private:
  int32_t _internal_task_index() const;
  void _internal_set_task_index(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.CompilationLogEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PassMetrics > pass_metrics_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > module_ids_;
    mutable std::atomic<int> _module_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp_;
    ::PROTOBUF_NAMESPACE_ID::Duration* duration_;
    ::xla::JobInfo* job_info_;
    int stage_;
    int32_t task_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmetrics_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// KeyValueMetric

// string key = 1;
inline void KeyValueMetric::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& KeyValueMetric::key() const {
  // @@protoc_insertion_point(field_get:xla.KeyValueMetric.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KeyValueMetric::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.KeyValueMetric.key)
}
inline std::string* KeyValueMetric::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:xla.KeyValueMetric.key)
  return _s;
}
inline const std::string& KeyValueMetric::_internal_key() const {
  return _impl_.key_.Get();
}
inline void KeyValueMetric::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* KeyValueMetric::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* KeyValueMetric::release_key() {
  // @@protoc_insertion_point(field_release:xla.KeyValueMetric.key)
  return _impl_.key_.Release();
}
inline void KeyValueMetric::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.KeyValueMetric.key)
}

// int64 value = 2;
inline void KeyValueMetric::clear_value() {
  _impl_.value_ = int64_t{0};
}
inline int64_t KeyValueMetric::_internal_value() const {
  return _impl_.value_;
}
inline int64_t KeyValueMetric::value() const {
  // @@protoc_insertion_point(field_get:xla.KeyValueMetric.value)
  return _internal_value();
}
inline void KeyValueMetric::_internal_set_value(int64_t value) {
  
  _impl_.value_ = value;
}
inline void KeyValueMetric::set_value(int64_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:xla.KeyValueMetric.value)
}

// -------------------------------------------------------------------

// PassMetrics

// uint64 module_id = 1;
inline void PassMetrics::clear_module_id() {
  _impl_.module_id_ = uint64_t{0u};
}
inline uint64_t PassMetrics::_internal_module_id() const {
  return _impl_.module_id_;
}
inline uint64_t PassMetrics::module_id() const {
  // @@protoc_insertion_point(field_get:xla.PassMetrics.module_id)
  return _internal_module_id();
}
inline void PassMetrics::_internal_set_module_id(uint64_t value) {
  
  _impl_.module_id_ = value;
}
inline void PassMetrics::set_module_id(uint64_t value) {
  _internal_set_module_id(value);
  // @@protoc_insertion_point(field_set:xla.PassMetrics.module_id)
}

// string pass_name = 2;
inline void PassMetrics::clear_pass_name() {
  _impl_.pass_name_.ClearToEmpty();
}
inline const std::string& PassMetrics::pass_name() const {
  // @@protoc_insertion_point(field_get:xla.PassMetrics.pass_name)
  return _internal_pass_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PassMetrics::set_pass_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.pass_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.PassMetrics.pass_name)
}
inline std::string* PassMetrics::mutable_pass_name() {
  std::string* _s = _internal_mutable_pass_name();
  // @@protoc_insertion_point(field_mutable:xla.PassMetrics.pass_name)
  return _s;
}
inline const std::string& PassMetrics::_internal_pass_name() const {
  return _impl_.pass_name_.Get();
}
inline void PassMetrics::_internal_set_pass_name(const std::string& value) {
  
  _impl_.pass_name_.Set(value, GetArenaForAllocation());
}
inline std::string* PassMetrics::_internal_mutable_pass_name() {
  
  return _impl_.pass_name_.Mutable(GetArenaForAllocation());
}
inline std::string* PassMetrics::release_pass_name() {
  // @@protoc_insertion_point(field_release:xla.PassMetrics.pass_name)
  return _impl_.pass_name_.Release();
}
inline void PassMetrics::set_allocated_pass_name(std::string* pass_name) {
  if (pass_name != nullptr) {
    
  } else {
    
  }
  _impl_.pass_name_.SetAllocated(pass_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pass_name_.IsDefault()) {
    _impl_.pass_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.PassMetrics.pass_name)
}

// .google.protobuf.Duration pass_duration = 3;
inline bool PassMetrics::_internal_has_pass_duration() const {
  return this != internal_default_instance() && _impl_.pass_duration_ != nullptr;
}
inline bool PassMetrics::has_pass_duration() const {
  return _internal_has_pass_duration();
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& PassMetrics::_internal_pass_duration() const {
  const ::PROTOBUF_NAMESPACE_ID::Duration* p = _impl_.pass_duration_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Duration&>(
      ::PROTOBUF_NAMESPACE_ID::_Duration_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& PassMetrics::pass_duration() const {
  // @@protoc_insertion_point(field_get:xla.PassMetrics.pass_duration)
  return _internal_pass_duration();
}
inline void PassMetrics::unsafe_arena_set_allocated_pass_duration(
    ::PROTOBUF_NAMESPACE_ID::Duration* pass_duration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pass_duration_);
  }
  _impl_.pass_duration_ = pass_duration;
  if (pass_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.PassMetrics.pass_duration)
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* PassMetrics::release_pass_duration() {
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = _impl_.pass_duration_;
  _impl_.pass_duration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* PassMetrics::unsafe_arena_release_pass_duration() {
  // @@protoc_insertion_point(field_release:xla.PassMetrics.pass_duration)
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = _impl_.pass_duration_;
  _impl_.pass_duration_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* PassMetrics::_internal_mutable_pass_duration() {
  
  if (_impl_.pass_duration_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Duration>(GetArenaForAllocation());
    _impl_.pass_duration_ = p;
  }
  return _impl_.pass_duration_;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* PassMetrics::mutable_pass_duration() {
  ::PROTOBUF_NAMESPACE_ID::Duration* _msg = _internal_mutable_pass_duration();
  // @@protoc_insertion_point(field_mutable:xla.PassMetrics.pass_duration)
  return _msg;
}
inline void PassMetrics::set_allocated_pass_duration(::PROTOBUF_NAMESPACE_ID::Duration* pass_duration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pass_duration_);
  }
  if (pass_duration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pass_duration));
    if (message_arena != submessage_arena) {
      pass_duration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pass_duration, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.pass_duration_ = pass_duration;
  // @@protoc_insertion_point(field_set_allocated:xla.PassMetrics.pass_duration)
}

// .google.protobuf.Any custom_metrics = 4;
inline bool PassMetrics::_internal_has_custom_metrics() const {
  return this != internal_default_instance() && _impl_.custom_metrics_ != nullptr;
}
inline bool PassMetrics::has_custom_metrics() const {
  return _internal_has_custom_metrics();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& PassMetrics::_internal_custom_metrics() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.custom_metrics_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& PassMetrics::custom_metrics() const {
  // @@protoc_insertion_point(field_get:xla.PassMetrics.custom_metrics)
  return _internal_custom_metrics();
}
inline void PassMetrics::unsafe_arena_set_allocated_custom_metrics(
    ::PROTOBUF_NAMESPACE_ID::Any* custom_metrics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.custom_metrics_);
  }
  _impl_.custom_metrics_ = custom_metrics;
  if (custom_metrics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.PassMetrics.custom_metrics)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* PassMetrics::release_custom_metrics() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.custom_metrics_;
  _impl_.custom_metrics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* PassMetrics::unsafe_arena_release_custom_metrics() {
  // @@protoc_insertion_point(field_release:xla.PassMetrics.custom_metrics)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.custom_metrics_;
  _impl_.custom_metrics_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* PassMetrics::_internal_mutable_custom_metrics() {
  
  if (_impl_.custom_metrics_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.custom_metrics_ = p;
  }
  return _impl_.custom_metrics_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* PassMetrics::mutable_custom_metrics() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_custom_metrics();
  // @@protoc_insertion_point(field_mutable:xla.PassMetrics.custom_metrics)
  return _msg;
}
inline void PassMetrics::set_allocated_custom_metrics(::PROTOBUF_NAMESPACE_ID::Any* custom_metrics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.custom_metrics_);
  }
  if (custom_metrics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(custom_metrics));
    if (message_arena != submessage_arena) {
      custom_metrics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, custom_metrics, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.custom_metrics_ = custom_metrics;
  // @@protoc_insertion_point(field_set_allocated:xla.PassMetrics.custom_metrics)
}

// repeated .xla.KeyValueMetric kv_metrics = 5;
inline int PassMetrics::_internal_kv_metrics_size() const {
  return _impl_.kv_metrics_.size();
}
inline int PassMetrics::kv_metrics_size() const {
  return _internal_kv_metrics_size();
}
inline void PassMetrics::clear_kv_metrics() {
  _impl_.kv_metrics_.Clear();
}
inline ::xla::KeyValueMetric* PassMetrics::mutable_kv_metrics(int index) {
  // @@protoc_insertion_point(field_mutable:xla.PassMetrics.kv_metrics)
  return _impl_.kv_metrics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::KeyValueMetric >*
PassMetrics::mutable_kv_metrics() {
  // @@protoc_insertion_point(field_mutable_list:xla.PassMetrics.kv_metrics)
  return &_impl_.kv_metrics_;
}
inline const ::xla::KeyValueMetric& PassMetrics::_internal_kv_metrics(int index) const {
  return _impl_.kv_metrics_.Get(index);
}
inline const ::xla::KeyValueMetric& PassMetrics::kv_metrics(int index) const {
  // @@protoc_insertion_point(field_get:xla.PassMetrics.kv_metrics)
  return _internal_kv_metrics(index);
}
inline ::xla::KeyValueMetric* PassMetrics::_internal_add_kv_metrics() {
  return _impl_.kv_metrics_.Add();
}
inline ::xla::KeyValueMetric* PassMetrics::add_kv_metrics() {
  ::xla::KeyValueMetric* _add = _internal_add_kv_metrics();
  // @@protoc_insertion_point(field_add:xla.PassMetrics.kv_metrics)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::KeyValueMetric >&
PassMetrics::kv_metrics() const {
  // @@protoc_insertion_point(field_list:xla.PassMetrics.kv_metrics)
  return _impl_.kv_metrics_;
}

// -------------------------------------------------------------------

// JobInfo

// optional string name = 1;
inline bool JobInfo::_internal_has_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool JobInfo::has_name() const {
  return _internal_has_name();
}
inline void JobInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& JobInfo::name() const {
  // @@protoc_insertion_point(field_get:xla.JobInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobInfo::set_name(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.JobInfo.name)
}
inline std::string* JobInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:xla.JobInfo.name)
  return _s;
}
inline const std::string& JobInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void JobInfo::_internal_set_name(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* JobInfo::_internal_mutable_name() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* JobInfo::release_name() {
  // @@protoc_insertion_point(field_release:xla.JobInfo.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void JobInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.JobInfo.name)
}

// optional string cell = 2;
inline bool JobInfo::_internal_has_cell() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool JobInfo::has_cell() const {
  return _internal_has_cell();
}
inline void JobInfo::clear_cell() {
  _impl_.cell_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& JobInfo::cell() const {
  // @@protoc_insertion_point(field_get:xla.JobInfo.cell)
  return _internal_cell();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobInfo::set_cell(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.cell_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.JobInfo.cell)
}
inline std::string* JobInfo::mutable_cell() {
  std::string* _s = _internal_mutable_cell();
  // @@protoc_insertion_point(field_mutable:xla.JobInfo.cell)
  return _s;
}
inline const std::string& JobInfo::_internal_cell() const {
  return _impl_.cell_.Get();
}
inline void JobInfo::_internal_set_cell(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.cell_.Set(value, GetArenaForAllocation());
}
inline std::string* JobInfo::_internal_mutable_cell() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.cell_.Mutable(GetArenaForAllocation());
}
inline std::string* JobInfo::release_cell() {
  // @@protoc_insertion_point(field_release:xla.JobInfo.cell)
  if (!_internal_has_cell()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.cell_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.cell_.IsDefault()) {
    _impl_.cell_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void JobInfo::set_allocated_cell(std::string* cell) {
  if (cell != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.cell_.SetAllocated(cell, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.cell_.IsDefault()) {
    _impl_.cell_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.JobInfo.cell)
}

// optional string user = 3;
inline bool JobInfo::_internal_has_user() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool JobInfo::has_user() const {
  return _internal_has_user();
}
inline void JobInfo::clear_user() {
  _impl_.user_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const std::string& JobInfo::user() const {
  // @@protoc_insertion_point(field_get:xla.JobInfo.user)
  return _internal_user();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobInfo::set_user(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000004u;
 _impl_.user_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.JobInfo.user)
}
inline std::string* JobInfo::mutable_user() {
  std::string* _s = _internal_mutable_user();
  // @@protoc_insertion_point(field_mutable:xla.JobInfo.user)
  return _s;
}
inline const std::string& JobInfo::_internal_user() const {
  return _impl_.user_.Get();
}
inline void JobInfo::_internal_set_user(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.user_.Set(value, GetArenaForAllocation());
}
inline std::string* JobInfo::_internal_mutable_user() {
  _impl_._has_bits_[0] |= 0x00000004u;
  return _impl_.user_.Mutable(GetArenaForAllocation());
}
inline std::string* JobInfo::release_user() {
  // @@protoc_insertion_point(field_release:xla.JobInfo.user)
  if (!_internal_has_user()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000004u;
  auto* p = _impl_.user_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.user_.IsDefault()) {
    _impl_.user_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void JobInfo::set_allocated_user(std::string* user) {
  if (user != nullptr) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.user_.SetAllocated(user, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.user_.IsDefault()) {
    _impl_.user_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.JobInfo.user)
}

// optional int64 uid = 4;
inline bool JobInfo::_internal_has_uid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool JobInfo::has_uid() const {
  return _internal_has_uid();
}
inline void JobInfo::clear_uid() {
  _impl_.uid_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline int64_t JobInfo::_internal_uid() const {
  return _impl_.uid_;
}
inline int64_t JobInfo::uid() const {
  // @@protoc_insertion_point(field_get:xla.JobInfo.uid)
  return _internal_uid();
}
inline void JobInfo::_internal_set_uid(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.uid_ = value;
}
inline void JobInfo::set_uid(int64_t value) {
  _internal_set_uid(value);
  // @@protoc_insertion_point(field_set:xla.JobInfo.uid)
}

// optional int64 task_id = 5;
inline bool JobInfo::_internal_has_task_id() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool JobInfo::has_task_id() const {
  return _internal_has_task_id();
}
inline void JobInfo::clear_task_id() {
  _impl_.task_id_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline int64_t JobInfo::_internal_task_id() const {
  return _impl_.task_id_;
}
inline int64_t JobInfo::task_id() const {
  // @@protoc_insertion_point(field_get:xla.JobInfo.task_id)
  return _internal_task_id();
}
inline void JobInfo::_internal_set_task_id(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.task_id_ = value;
}
inline void JobInfo::set_task_id(int64_t value) {
  _internal_set_task_id(value);
  // @@protoc_insertion_point(field_set:xla.JobInfo.task_id)
}

// optional int64 task_uid = 6;
inline bool JobInfo::_internal_has_task_uid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool JobInfo::has_task_uid() const {
  return _internal_has_task_uid();
}
inline void JobInfo::clear_task_uid() {
  _impl_.task_uid_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline int64_t JobInfo::_internal_task_uid() const {
  return _impl_.task_uid_;
}
inline int64_t JobInfo::task_uid() const {
  // @@protoc_insertion_point(field_get:xla.JobInfo.task_uid)
  return _internal_task_uid();
}
inline void JobInfo::_internal_set_task_uid(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.task_uid_ = value;
}
inline void JobInfo::set_task_uid(int64_t value) {
  _internal_set_task_uid(value);
  // @@protoc_insertion_point(field_set:xla.JobInfo.task_uid)
}

// -------------------------------------------------------------------

// CompilationLogEntry

// .google.protobuf.Timestamp timestamp = 1;
inline bool CompilationLogEntry::_internal_has_timestamp() const {
  return this != internal_default_instance() && _impl_.timestamp_ != nullptr;
}
inline bool CompilationLogEntry::has_timestamp() const {
  return _internal_has_timestamp();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& CompilationLogEntry::_internal_timestamp() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = _impl_.timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& CompilationLogEntry::timestamp() const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.timestamp)
  return _internal_timestamp();
}
inline void CompilationLogEntry::unsafe_arena_set_allocated_timestamp(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.timestamp_);
  }
  _impl_.timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.CompilationLogEntry.timestamp)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CompilationLogEntry::release_timestamp() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = _impl_.timestamp_;
  _impl_.timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CompilationLogEntry::unsafe_arena_release_timestamp() {
  // @@protoc_insertion_point(field_release:xla.CompilationLogEntry.timestamp)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = _impl_.timestamp_;
  _impl_.timestamp_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CompilationLogEntry::_internal_mutable_timestamp() {
  
  if (_impl_.timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    _impl_.timestamp_ = p;
  }
  return _impl_.timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CompilationLogEntry::mutable_timestamp() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:xla.CompilationLogEntry.timestamp)
  return _msg;
}
inline void CompilationLogEntry::set_allocated_timestamp(::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.timestamp_);
  }
  if (timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp));
    if (message_arena != submessage_arena) {
      timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:xla.CompilationLogEntry.timestamp)
}

// .xla.CompilationLogEntry.CompilationStage stage = 2;
inline void CompilationLogEntry::clear_stage() {
  _impl_.stage_ = 0;
}
inline ::xla::CompilationLogEntry_CompilationStage CompilationLogEntry::_internal_stage() const {
  return static_cast< ::xla::CompilationLogEntry_CompilationStage >(_impl_.stage_);
}
inline ::xla::CompilationLogEntry_CompilationStage CompilationLogEntry::stage() const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.stage)
  return _internal_stage();
}
inline void CompilationLogEntry::_internal_set_stage(::xla::CompilationLogEntry_CompilationStage value) {
  
  _impl_.stage_ = value;
}
inline void CompilationLogEntry::set_stage(::xla::CompilationLogEntry_CompilationStage value) {
  _internal_set_stage(value);
  // @@protoc_insertion_point(field_set:xla.CompilationLogEntry.stage)
}

// .google.protobuf.Duration duration = 3;
inline bool CompilationLogEntry::_internal_has_duration() const {
  return this != internal_default_instance() && _impl_.duration_ != nullptr;
}
inline bool CompilationLogEntry::has_duration() const {
  return _internal_has_duration();
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& CompilationLogEntry::_internal_duration() const {
  const ::PROTOBUF_NAMESPACE_ID::Duration* p = _impl_.duration_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Duration&>(
      ::PROTOBUF_NAMESPACE_ID::_Duration_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& CompilationLogEntry::duration() const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.duration)
  return _internal_duration();
}
inline void CompilationLogEntry::unsafe_arena_set_allocated_duration(
    ::PROTOBUF_NAMESPACE_ID::Duration* duration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.duration_);
  }
  _impl_.duration_ = duration;
  if (duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.CompilationLogEntry.duration)
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* CompilationLogEntry::release_duration() {
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = _impl_.duration_;
  _impl_.duration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* CompilationLogEntry::unsafe_arena_release_duration() {
  // @@protoc_insertion_point(field_release:xla.CompilationLogEntry.duration)
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = _impl_.duration_;
  _impl_.duration_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* CompilationLogEntry::_internal_mutable_duration() {
  
  if (_impl_.duration_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Duration>(GetArenaForAllocation());
    _impl_.duration_ = p;
  }
  return _impl_.duration_;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* CompilationLogEntry::mutable_duration() {
  ::PROTOBUF_NAMESPACE_ID::Duration* _msg = _internal_mutable_duration();
  // @@protoc_insertion_point(field_mutable:xla.CompilationLogEntry.duration)
  return _msg;
}
inline void CompilationLogEntry::set_allocated_duration(::PROTOBUF_NAMESPACE_ID::Duration* duration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.duration_);
  }
  if (duration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(duration));
    if (message_arena != submessage_arena) {
      duration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, duration, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.duration_ = duration;
  // @@protoc_insertion_point(field_set_allocated:xla.CompilationLogEntry.duration)
}

// int32 task_index = 4;
inline void CompilationLogEntry::clear_task_index() {
  _impl_.task_index_ = 0;
}
inline int32_t CompilationLogEntry::_internal_task_index() const {
  return _impl_.task_index_;
}
inline int32_t CompilationLogEntry::task_index() const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.task_index)
  return _internal_task_index();
}
inline void CompilationLogEntry::_internal_set_task_index(int32_t value) {
  
  _impl_.task_index_ = value;
}
inline void CompilationLogEntry::set_task_index(int32_t value) {
  _internal_set_task_index(value);
  // @@protoc_insertion_point(field_set:xla.CompilationLogEntry.task_index)
}

// repeated .xla.PassMetrics pass_metrics = 5;
inline int CompilationLogEntry::_internal_pass_metrics_size() const {
  return _impl_.pass_metrics_.size();
}
inline int CompilationLogEntry::pass_metrics_size() const {
  return _internal_pass_metrics_size();
}
inline void CompilationLogEntry::clear_pass_metrics() {
  _impl_.pass_metrics_.Clear();
}
inline ::xla::PassMetrics* CompilationLogEntry::mutable_pass_metrics(int index) {
  // @@protoc_insertion_point(field_mutable:xla.CompilationLogEntry.pass_metrics)
  return _impl_.pass_metrics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PassMetrics >*
CompilationLogEntry::mutable_pass_metrics() {
  // @@protoc_insertion_point(field_mutable_list:xla.CompilationLogEntry.pass_metrics)
  return &_impl_.pass_metrics_;
}
inline const ::xla::PassMetrics& CompilationLogEntry::_internal_pass_metrics(int index) const {
  return _impl_.pass_metrics_.Get(index);
}
inline const ::xla::PassMetrics& CompilationLogEntry::pass_metrics(int index) const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.pass_metrics)
  return _internal_pass_metrics(index);
}
inline ::xla::PassMetrics* CompilationLogEntry::_internal_add_pass_metrics() {
  return _impl_.pass_metrics_.Add();
}
inline ::xla::PassMetrics* CompilationLogEntry::add_pass_metrics() {
  ::xla::PassMetrics* _add = _internal_add_pass_metrics();
  // @@protoc_insertion_point(field_add:xla.CompilationLogEntry.pass_metrics)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::PassMetrics >&
CompilationLogEntry::pass_metrics() const {
  // @@protoc_insertion_point(field_list:xla.CompilationLogEntry.pass_metrics)
  return _impl_.pass_metrics_;
}

// repeated uint64 module_ids = 6;
inline int CompilationLogEntry::_internal_module_ids_size() const {
  return _impl_.module_ids_.size();
}
inline int CompilationLogEntry::module_ids_size() const {
  return _internal_module_ids_size();
}
inline void CompilationLogEntry::clear_module_ids() {
  _impl_.module_ids_.Clear();
}
inline uint64_t CompilationLogEntry::_internal_module_ids(int index) const {
  return _impl_.module_ids_.Get(index);
}
inline uint64_t CompilationLogEntry::module_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.module_ids)
  return _internal_module_ids(index);
}
inline void CompilationLogEntry::set_module_ids(int index, uint64_t value) {
  _impl_.module_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.CompilationLogEntry.module_ids)
}
inline void CompilationLogEntry::_internal_add_module_ids(uint64_t value) {
  _impl_.module_ids_.Add(value);
}
inline void CompilationLogEntry::add_module_ids(uint64_t value) {
  _internal_add_module_ids(value);
  // @@protoc_insertion_point(field_add:xla.CompilationLogEntry.module_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
CompilationLogEntry::_internal_module_ids() const {
  return _impl_.module_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
CompilationLogEntry::module_ids() const {
  // @@protoc_insertion_point(field_list:xla.CompilationLogEntry.module_ids)
  return _internal_module_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
CompilationLogEntry::_internal_mutable_module_ids() {
  return &_impl_.module_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
CompilationLogEntry::mutable_module_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.CompilationLogEntry.module_ids)
  return _internal_mutable_module_ids();
}

// .xla.JobInfo job_info = 7;
inline bool CompilationLogEntry::_internal_has_job_info() const {
  return this != internal_default_instance() && _impl_.job_info_ != nullptr;
}
inline bool CompilationLogEntry::has_job_info() const {
  return _internal_has_job_info();
}
inline void CompilationLogEntry::clear_job_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.job_info_ != nullptr) {
    delete _impl_.job_info_;
  }
  _impl_.job_info_ = nullptr;
}
inline const ::xla::JobInfo& CompilationLogEntry::_internal_job_info() const {
  const ::xla::JobInfo* p = _impl_.job_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::JobInfo&>(
      ::xla::_JobInfo_default_instance_);
}
inline const ::xla::JobInfo& CompilationLogEntry::job_info() const {
  // @@protoc_insertion_point(field_get:xla.CompilationLogEntry.job_info)
  return _internal_job_info();
}
inline void CompilationLogEntry::unsafe_arena_set_allocated_job_info(
    ::xla::JobInfo* job_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.job_info_);
  }
  _impl_.job_info_ = job_info;
  if (job_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.CompilationLogEntry.job_info)
}
inline ::xla::JobInfo* CompilationLogEntry::release_job_info() {
  
  ::xla::JobInfo* temp = _impl_.job_info_;
  _impl_.job_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::JobInfo* CompilationLogEntry::unsafe_arena_release_job_info() {
  // @@protoc_insertion_point(field_release:xla.CompilationLogEntry.job_info)
  
  ::xla::JobInfo* temp = _impl_.job_info_;
  _impl_.job_info_ = nullptr;
  return temp;
}
inline ::xla::JobInfo* CompilationLogEntry::_internal_mutable_job_info() {
  
  if (_impl_.job_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::JobInfo>(GetArenaForAllocation());
    _impl_.job_info_ = p;
  }
  return _impl_.job_info_;
}
inline ::xla::JobInfo* CompilationLogEntry::mutable_job_info() {
  ::xla::JobInfo* _msg = _internal_mutable_job_info();
  // @@protoc_insertion_point(field_mutable:xla.CompilationLogEntry.job_info)
  return _msg;
}
inline void CompilationLogEntry::set_allocated_job_info(::xla::JobInfo* job_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.job_info_;
  }
  if (job_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(job_info);
    if (message_arena != submessage_arena) {
      job_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, job_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.job_info_ = job_info;
  // @@protoc_insertion_point(field_set_allocated:xla.CompilationLogEntry.job_info)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::CompilationLogEntry_CompilationStage> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::CompilationLogEntry_CompilationStage>() {
  return ::xla::CompilationLogEntry_CompilationStage_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fmetrics_2eproto
