
/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// This file is autogenerated from a template file. Please make
// modifications to
// `templates/src/objective-c/tsi/grpc_shadow_boringssl.h.template`
// instead. This file can be regenerated from the template by running
// `tools/buildgen/generate_projects.sh`.

#ifndef GRPC_CORE_TSI_GRPC_SHADOW_BORINGSSL_H
#define GRPC_CORE_TSI_GRPC_SHADOW_BORINGSSL_H

#ifdef GRPC_SHADOW_BORINGSSL_SYMBOLS

#define ACCESS_DESCRIPTION_free GRPC_SHADOW_ACCESS_DESCRIPTION_free
#define ACCESS_DESCRIPTION_it GRPC_SHADOW_ACCESS_DESCRIPTION_it
#define ACCESS_DESCRIPTION_new GRPC_SHADOW_ACCESS_DESCRIPTION_new
#define AES_CMAC GRPC_SHADOW_AES_CMAC
#define AES_cbc_encrypt GRPC_SHADOW_AES_cbc_encrypt
#define AES_cfb128_encrypt GRPC_SHADOW_AES_cfb128_encrypt
#define AES_ctr128_encrypt GRPC_SHADOW_AES_ctr128_encrypt
#define AES_decrypt GRPC_SHADOW_AES_decrypt
#define AES_ecb_encrypt GRPC_SHADOW_AES_ecb_encrypt
#define AES_encrypt GRPC_SHADOW_AES_encrypt
#define AES_ofb128_encrypt GRPC_SHADOW_AES_ofb128_encrypt
#define AES_set_decrypt_key GRPC_SHADOW_AES_set_decrypt_key
#define AES_set_encrypt_key GRPC_SHADOW_AES_set_encrypt_key
#define AES_unwrap_key GRPC_SHADOW_AES_unwrap_key
#define AES_unwrap_key_padded GRPC_SHADOW_AES_unwrap_key_padded
#define AES_wrap_key GRPC_SHADOW_AES_wrap_key
#define AES_wrap_key_padded GRPC_SHADOW_AES_wrap_key_padded
#define ASN1_ANY_it GRPC_SHADOW_ASN1_ANY_it
#define ASN1_BIT_STRING_check GRPC_SHADOW_ASN1_BIT_STRING_check
#define ASN1_BIT_STRING_free GRPC_SHADOW_ASN1_BIT_STRING_free
#define ASN1_BIT_STRING_get_bit GRPC_SHADOW_ASN1_BIT_STRING_get_bit
#define ASN1_BIT_STRING_it GRPC_SHADOW_ASN1_BIT_STRING_it
#define ASN1_BIT_STRING_new GRPC_SHADOW_ASN1_BIT_STRING_new
#define ASN1_BIT_STRING_set GRPC_SHADOW_ASN1_BIT_STRING_set
#define ASN1_BIT_STRING_set_bit GRPC_SHADOW_ASN1_BIT_STRING_set_bit
#define ASN1_BMPSTRING_free GRPC_SHADOW_ASN1_BMPSTRING_free
#define ASN1_BMPSTRING_it GRPC_SHADOW_ASN1_BMPSTRING_it
#define ASN1_BMPSTRING_new GRPC_SHADOW_ASN1_BMPSTRING_new
#define ASN1_BOOLEAN_it GRPC_SHADOW_ASN1_BOOLEAN_it
#define ASN1_ENUMERATED_free GRPC_SHADOW_ASN1_ENUMERATED_free
#define ASN1_ENUMERATED_get GRPC_SHADOW_ASN1_ENUMERATED_get
#define ASN1_ENUMERATED_it GRPC_SHADOW_ASN1_ENUMERATED_it
#define ASN1_ENUMERATED_new GRPC_SHADOW_ASN1_ENUMERATED_new
#define ASN1_ENUMERATED_set GRPC_SHADOW_ASN1_ENUMERATED_set
#define ASN1_ENUMERATED_to_BN GRPC_SHADOW_ASN1_ENUMERATED_to_BN
#define ASN1_FBOOLEAN_it GRPC_SHADOW_ASN1_FBOOLEAN_it
#define ASN1_GENERALIZEDTIME_adj GRPC_SHADOW_ASN1_GENERALIZEDTIME_adj
#define ASN1_GENERALIZEDTIME_check GRPC_SHADOW_ASN1_GENERALIZEDTIME_check
#define ASN1_GENERALIZEDTIME_free GRPC_SHADOW_ASN1_GENERALIZEDTIME_free
#define ASN1_GENERALIZEDTIME_it GRPC_SHADOW_ASN1_GENERALIZEDTIME_it
#define ASN1_GENERALIZEDTIME_new GRPC_SHADOW_ASN1_GENERALIZEDTIME_new
#define ASN1_GENERALIZEDTIME_print GRPC_SHADOW_ASN1_GENERALIZEDTIME_print
#define ASN1_GENERALIZEDTIME_set GRPC_SHADOW_ASN1_GENERALIZEDTIME_set
#define ASN1_GENERALIZEDTIME_set_string GRPC_SHADOW_ASN1_GENERALIZEDTIME_set_string
#define ASN1_GENERALSTRING_free GRPC_SHADOW_ASN1_GENERALSTRING_free
#define ASN1_GENERALSTRING_it GRPC_SHADOW_ASN1_GENERALSTRING_it
#define ASN1_GENERALSTRING_new GRPC_SHADOW_ASN1_GENERALSTRING_new
#define ASN1_IA5STRING_free GRPC_SHADOW_ASN1_IA5STRING_free
#define ASN1_IA5STRING_it GRPC_SHADOW_ASN1_IA5STRING_it
#define ASN1_IA5STRING_new GRPC_SHADOW_ASN1_IA5STRING_new
#define ASN1_INTEGER_cmp GRPC_SHADOW_ASN1_INTEGER_cmp
#define ASN1_INTEGER_dup GRPC_SHADOW_ASN1_INTEGER_dup
#define ASN1_INTEGER_free GRPC_SHADOW_ASN1_INTEGER_free
#define ASN1_INTEGER_get GRPC_SHADOW_ASN1_INTEGER_get
#define ASN1_INTEGER_it GRPC_SHADOW_ASN1_INTEGER_it
#define ASN1_INTEGER_new GRPC_SHADOW_ASN1_INTEGER_new
#define ASN1_INTEGER_set GRPC_SHADOW_ASN1_INTEGER_set
#define ASN1_INTEGER_set_uint64 GRPC_SHADOW_ASN1_INTEGER_set_uint64
#define ASN1_INTEGER_to_BN GRPC_SHADOW_ASN1_INTEGER_to_BN
#define ASN1_NULL_free GRPC_SHADOW_ASN1_NULL_free
#define ASN1_NULL_it GRPC_SHADOW_ASN1_NULL_it
#define ASN1_NULL_new GRPC_SHADOW_ASN1_NULL_new
#define ASN1_OBJECT_create GRPC_SHADOW_ASN1_OBJECT_create
#define ASN1_OBJECT_free GRPC_SHADOW_ASN1_OBJECT_free
#define ASN1_OBJECT_it GRPC_SHADOW_ASN1_OBJECT_it
#define ASN1_OBJECT_new GRPC_SHADOW_ASN1_OBJECT_new
#define ASN1_OCTET_STRING_NDEF_it GRPC_SHADOW_ASN1_OCTET_STRING_NDEF_it
#define ASN1_OCTET_STRING_cmp GRPC_SHADOW_ASN1_OCTET_STRING_cmp
#define ASN1_OCTET_STRING_dup GRPC_SHADOW_ASN1_OCTET_STRING_dup
#define ASN1_OCTET_STRING_free GRPC_SHADOW_ASN1_OCTET_STRING_free
#define ASN1_OCTET_STRING_it GRPC_SHADOW_ASN1_OCTET_STRING_it
#define ASN1_OCTET_STRING_new GRPC_SHADOW_ASN1_OCTET_STRING_new
#define ASN1_OCTET_STRING_set GRPC_SHADOW_ASN1_OCTET_STRING_set
#define ASN1_PRINTABLESTRING_free GRPC_SHADOW_ASN1_PRINTABLESTRING_free
#define ASN1_PRINTABLESTRING_it GRPC_SHADOW_ASN1_PRINTABLESTRING_it
#define ASN1_PRINTABLESTRING_new GRPC_SHADOW_ASN1_PRINTABLESTRING_new
#define ASN1_PRINTABLE_free GRPC_SHADOW_ASN1_PRINTABLE_free
#define ASN1_PRINTABLE_it GRPC_SHADOW_ASN1_PRINTABLE_it
#define ASN1_PRINTABLE_new GRPC_SHADOW_ASN1_PRINTABLE_new
#define ASN1_PRINTABLE_type GRPC_SHADOW_ASN1_PRINTABLE_type
#define ASN1_SEQUENCE_ANY_it GRPC_SHADOW_ASN1_SEQUENCE_ANY_it
#define ASN1_SEQUENCE_it GRPC_SHADOW_ASN1_SEQUENCE_it
#define ASN1_SET_ANY_it GRPC_SHADOW_ASN1_SET_ANY_it
#define ASN1_STRING_TABLE_add GRPC_SHADOW_ASN1_STRING_TABLE_add
#define ASN1_STRING_TABLE_cleanup GRPC_SHADOW_ASN1_STRING_TABLE_cleanup
#define ASN1_STRING_TABLE_get GRPC_SHADOW_ASN1_STRING_TABLE_get
#define ASN1_STRING_cmp GRPC_SHADOW_ASN1_STRING_cmp
#define ASN1_STRING_copy GRPC_SHADOW_ASN1_STRING_copy
#define ASN1_STRING_data GRPC_SHADOW_ASN1_STRING_data
#define ASN1_STRING_dup GRPC_SHADOW_ASN1_STRING_dup
#define ASN1_STRING_free GRPC_SHADOW_ASN1_STRING_free
#define ASN1_STRING_get0_data GRPC_SHADOW_ASN1_STRING_get0_data
#define ASN1_STRING_get_default_mask GRPC_SHADOW_ASN1_STRING_get_default_mask
#define ASN1_STRING_length GRPC_SHADOW_ASN1_STRING_length
#define ASN1_STRING_length_set GRPC_SHADOW_ASN1_STRING_length_set
#define ASN1_STRING_new GRPC_SHADOW_ASN1_STRING_new
#define ASN1_STRING_print GRPC_SHADOW_ASN1_STRING_print
#define ASN1_STRING_print_ex GRPC_SHADOW_ASN1_STRING_print_ex
#define ASN1_STRING_print_ex_fp GRPC_SHADOW_ASN1_STRING_print_ex_fp
#define ASN1_STRING_set GRPC_SHADOW_ASN1_STRING_set
#define ASN1_STRING_set0 GRPC_SHADOW_ASN1_STRING_set0
#define ASN1_STRING_set_by_NID GRPC_SHADOW_ASN1_STRING_set_by_NID
#define ASN1_STRING_set_default_mask GRPC_SHADOW_ASN1_STRING_set_default_mask
#define ASN1_STRING_set_default_mask_asc GRPC_SHADOW_ASN1_STRING_set_default_mask_asc
#define ASN1_STRING_to_UTF8 GRPC_SHADOW_ASN1_STRING_to_UTF8
#define ASN1_STRING_type GRPC_SHADOW_ASN1_STRING_type
#define ASN1_STRING_type_new GRPC_SHADOW_ASN1_STRING_type_new
#define ASN1_T61STRING_free GRPC_SHADOW_ASN1_T61STRING_free
#define ASN1_T61STRING_it GRPC_SHADOW_ASN1_T61STRING_it
#define ASN1_T61STRING_new GRPC_SHADOW_ASN1_T61STRING_new
#define ASN1_TBOOLEAN_it GRPC_SHADOW_ASN1_TBOOLEAN_it
#define ASN1_TIME_adj GRPC_SHADOW_ASN1_TIME_adj
#define ASN1_TIME_check GRPC_SHADOW_ASN1_TIME_check
#define ASN1_TIME_diff GRPC_SHADOW_ASN1_TIME_diff
#define ASN1_TIME_free GRPC_SHADOW_ASN1_TIME_free
#define ASN1_TIME_it GRPC_SHADOW_ASN1_TIME_it
#define ASN1_TIME_new GRPC_SHADOW_ASN1_TIME_new
#define ASN1_TIME_print GRPC_SHADOW_ASN1_TIME_print
#define ASN1_TIME_set GRPC_SHADOW_ASN1_TIME_set
#define ASN1_TIME_set_string GRPC_SHADOW_ASN1_TIME_set_string
#define ASN1_TIME_to_generalizedtime GRPC_SHADOW_ASN1_TIME_to_generalizedtime
#define ASN1_TYPE_cmp GRPC_SHADOW_ASN1_TYPE_cmp
#define ASN1_TYPE_free GRPC_SHADOW_ASN1_TYPE_free
#define ASN1_TYPE_get GRPC_SHADOW_ASN1_TYPE_get
#define ASN1_TYPE_new GRPC_SHADOW_ASN1_TYPE_new
#define ASN1_TYPE_set GRPC_SHADOW_ASN1_TYPE_set
#define ASN1_TYPE_set1 GRPC_SHADOW_ASN1_TYPE_set1
#define ASN1_UNIVERSALSTRING_free GRPC_SHADOW_ASN1_UNIVERSALSTRING_free
#define ASN1_UNIVERSALSTRING_it GRPC_SHADOW_ASN1_UNIVERSALSTRING_it
#define ASN1_UNIVERSALSTRING_new GRPC_SHADOW_ASN1_UNIVERSALSTRING_new
#define ASN1_UTCTIME_adj GRPC_SHADOW_ASN1_UTCTIME_adj
#define ASN1_UTCTIME_check GRPC_SHADOW_ASN1_UTCTIME_check
#define ASN1_UTCTIME_cmp_time_t GRPC_SHADOW_ASN1_UTCTIME_cmp_time_t
#define ASN1_UTCTIME_free GRPC_SHADOW_ASN1_UTCTIME_free
#define ASN1_UTCTIME_it GRPC_SHADOW_ASN1_UTCTIME_it
#define ASN1_UTCTIME_new GRPC_SHADOW_ASN1_UTCTIME_new
#define ASN1_UTCTIME_print GRPC_SHADOW_ASN1_UTCTIME_print
#define ASN1_UTCTIME_set GRPC_SHADOW_ASN1_UTCTIME_set
#define ASN1_UTCTIME_set_string GRPC_SHADOW_ASN1_UTCTIME_set_string
#define ASN1_UTF8STRING_free GRPC_SHADOW_ASN1_UTF8STRING_free
#define ASN1_UTF8STRING_it GRPC_SHADOW_ASN1_UTF8STRING_it
#define ASN1_UTF8STRING_new GRPC_SHADOW_ASN1_UTF8STRING_new
#define ASN1_VISIBLESTRING_free GRPC_SHADOW_ASN1_VISIBLESTRING_free
#define ASN1_VISIBLESTRING_it GRPC_SHADOW_ASN1_VISIBLESTRING_it
#define ASN1_VISIBLESTRING_new GRPC_SHADOW_ASN1_VISIBLESTRING_new
#define ASN1_digest GRPC_SHADOW_ASN1_digest
#define ASN1_generate_nconf GRPC_SHADOW_ASN1_generate_nconf
#define ASN1_generate_v3 GRPC_SHADOW_ASN1_generate_v3
#define ASN1_get_object GRPC_SHADOW_ASN1_get_object
#define ASN1_item_d2i GRPC_SHADOW_ASN1_item_d2i
#define ASN1_item_d2i_bio GRPC_SHADOW_ASN1_item_d2i_bio
#define ASN1_item_d2i_fp GRPC_SHADOW_ASN1_item_d2i_fp
#define ASN1_item_digest GRPC_SHADOW_ASN1_item_digest
#define ASN1_item_dup GRPC_SHADOW_ASN1_item_dup
#define ASN1_item_ex_d2i GRPC_SHADOW_ASN1_item_ex_d2i
#define ASN1_item_ex_free GRPC_SHADOW_ASN1_item_ex_free
#define ASN1_item_ex_i2d GRPC_SHADOW_ASN1_item_ex_i2d
#define ASN1_item_ex_new GRPC_SHADOW_ASN1_item_ex_new
#define ASN1_item_free GRPC_SHADOW_ASN1_item_free
#define ASN1_item_i2d GRPC_SHADOW_ASN1_item_i2d
#define ASN1_item_i2d_bio GRPC_SHADOW_ASN1_item_i2d_bio
#define ASN1_item_i2d_fp GRPC_SHADOW_ASN1_item_i2d_fp
#define ASN1_item_ndef_i2d GRPC_SHADOW_ASN1_item_ndef_i2d
#define ASN1_item_new GRPC_SHADOW_ASN1_item_new
#define ASN1_item_pack GRPC_SHADOW_ASN1_item_pack
#define ASN1_item_sign GRPC_SHADOW_ASN1_item_sign
#define ASN1_item_sign_ctx GRPC_SHADOW_ASN1_item_sign_ctx
#define ASN1_item_unpack GRPC_SHADOW_ASN1_item_unpack
#define ASN1_item_verify GRPC_SHADOW_ASN1_item_verify
#define ASN1_mbstring_copy GRPC_SHADOW_ASN1_mbstring_copy
#define ASN1_mbstring_ncopy GRPC_SHADOW_ASN1_mbstring_ncopy
#define ASN1_object_size GRPC_SHADOW_ASN1_object_size
#define ASN1_primitive_free GRPC_SHADOW_ASN1_primitive_free
#define ASN1_primitive_new GRPC_SHADOW_ASN1_primitive_new
#define ASN1_put_eoc GRPC_SHADOW_ASN1_put_eoc
#define ASN1_put_object GRPC_SHADOW_ASN1_put_object
#define ASN1_tag2bit GRPC_SHADOW_ASN1_tag2bit
#define ASN1_tag2str GRPC_SHADOW_ASN1_tag2str
#define ASN1_template_free GRPC_SHADOW_ASN1_template_free
#define ASN1_template_new GRPC_SHADOW_ASN1_template_new
#define AUTHORITY_INFO_ACCESS_free GRPC_SHADOW_AUTHORITY_INFO_ACCESS_free
#define AUTHORITY_INFO_ACCESS_it GRPC_SHADOW_AUTHORITY_INFO_ACCESS_it
#define AUTHORITY_INFO_ACCESS_new GRPC_SHADOW_AUTHORITY_INFO_ACCESS_new
#define AUTHORITY_KEYID_free GRPC_SHADOW_AUTHORITY_KEYID_free
#define AUTHORITY_KEYID_it GRPC_SHADOW_AUTHORITY_KEYID_it
#define AUTHORITY_KEYID_new GRPC_SHADOW_AUTHORITY_KEYID_new
#define BASIC_CONSTRAINTS_free GRPC_SHADOW_BASIC_CONSTRAINTS_free
#define BASIC_CONSTRAINTS_it GRPC_SHADOW_BASIC_CONSTRAINTS_it
#define BASIC_CONSTRAINTS_new GRPC_SHADOW_BASIC_CONSTRAINTS_new
#define BIO_append_filename GRPC_SHADOW_BIO_append_filename
#define BIO_callback_ctrl GRPC_SHADOW_BIO_callback_ctrl
#define BIO_clear_flags GRPC_SHADOW_BIO_clear_flags
#define BIO_clear_retry_flags GRPC_SHADOW_BIO_clear_retry_flags
#define BIO_copy_next_retry GRPC_SHADOW_BIO_copy_next_retry
#define BIO_ctrl GRPC_SHADOW_BIO_ctrl
#define BIO_ctrl_get_read_request GRPC_SHADOW_BIO_ctrl_get_read_request
#define BIO_ctrl_get_write_guarantee GRPC_SHADOW_BIO_ctrl_get_write_guarantee
#define BIO_ctrl_pending GRPC_SHADOW_BIO_ctrl_pending
#define BIO_do_connect GRPC_SHADOW_BIO_do_connect
#define BIO_eof GRPC_SHADOW_BIO_eof
#define BIO_f_ssl GRPC_SHADOW_BIO_f_ssl
#define BIO_find_type GRPC_SHADOW_BIO_find_type
#define BIO_flush GRPC_SHADOW_BIO_flush
#define BIO_free GRPC_SHADOW_BIO_free
#define BIO_free_all GRPC_SHADOW_BIO_free_all
#define BIO_get_data GRPC_SHADOW_BIO_get_data
#define BIO_get_fd GRPC_SHADOW_BIO_get_fd
#define BIO_get_fp GRPC_SHADOW_BIO_get_fp
#define BIO_get_init GRPC_SHADOW_BIO_get_init
#define BIO_get_mem_data GRPC_SHADOW_BIO_get_mem_data
#define BIO_get_mem_ptr GRPC_SHADOW_BIO_get_mem_ptr
#define BIO_get_new_index GRPC_SHADOW_BIO_get_new_index
#define BIO_get_retry_flags GRPC_SHADOW_BIO_get_retry_flags
#define BIO_get_retry_reason GRPC_SHADOW_BIO_get_retry_reason
#define BIO_get_shutdown GRPC_SHADOW_BIO_get_shutdown
#define BIO_gets GRPC_SHADOW_BIO_gets
#define BIO_hexdump GRPC_SHADOW_BIO_hexdump
#define BIO_indent GRPC_SHADOW_BIO_indent
#define BIO_int_ctrl GRPC_SHADOW_BIO_int_ctrl
#define BIO_mem_contents GRPC_SHADOW_BIO_mem_contents
#define BIO_meth_free GRPC_SHADOW_BIO_meth_free
#define BIO_meth_new GRPC_SHADOW_BIO_meth_new
#define BIO_meth_set_create GRPC_SHADOW_BIO_meth_set_create
#define BIO_meth_set_ctrl GRPC_SHADOW_BIO_meth_set_ctrl
#define BIO_meth_set_destroy GRPC_SHADOW_BIO_meth_set_destroy
#define BIO_meth_set_gets GRPC_SHADOW_BIO_meth_set_gets
#define BIO_meth_set_puts GRPC_SHADOW_BIO_meth_set_puts
#define BIO_meth_set_read GRPC_SHADOW_BIO_meth_set_read
#define BIO_meth_set_write GRPC_SHADOW_BIO_meth_set_write
#define BIO_method_type GRPC_SHADOW_BIO_method_type
#define BIO_new GRPC_SHADOW_BIO_new
#define BIO_new_bio_pair GRPC_SHADOW_BIO_new_bio_pair
#define BIO_new_connect GRPC_SHADOW_BIO_new_connect
#define BIO_new_fd GRPC_SHADOW_BIO_new_fd
#define BIO_new_file GRPC_SHADOW_BIO_new_file
#define BIO_new_fp GRPC_SHADOW_BIO_new_fp
#define BIO_new_mem_buf GRPC_SHADOW_BIO_new_mem_buf
#define BIO_new_socket GRPC_SHADOW_BIO_new_socket
#define BIO_next GRPC_SHADOW_BIO_next
#define BIO_number_read GRPC_SHADOW_BIO_number_read
#define BIO_number_written GRPC_SHADOW_BIO_number_written
#define BIO_pending GRPC_SHADOW_BIO_pending
#define BIO_pop GRPC_SHADOW_BIO_pop
#define BIO_printf GRPC_SHADOW_BIO_printf
#define BIO_ptr_ctrl GRPC_SHADOW_BIO_ptr_ctrl
#define BIO_push GRPC_SHADOW_BIO_push
#define BIO_puts GRPC_SHADOW_BIO_puts
#define BIO_read GRPC_SHADOW_BIO_read
#define BIO_read_asn1 GRPC_SHADOW_BIO_read_asn1
#define BIO_read_filename GRPC_SHADOW_BIO_read_filename
#define BIO_reset GRPC_SHADOW_BIO_reset
#define BIO_rw_filename GRPC_SHADOW_BIO_rw_filename
#define BIO_s_connect GRPC_SHADOW_BIO_s_connect
#define BIO_s_fd GRPC_SHADOW_BIO_s_fd
#define BIO_s_file GRPC_SHADOW_BIO_s_file
#define BIO_s_mem GRPC_SHADOW_BIO_s_mem
#define BIO_s_socket GRPC_SHADOW_BIO_s_socket
#define BIO_set_close GRPC_SHADOW_BIO_set_close
#define BIO_set_conn_hostname GRPC_SHADOW_BIO_set_conn_hostname
#define BIO_set_conn_int_port GRPC_SHADOW_BIO_set_conn_int_port
#define BIO_set_conn_port GRPC_SHADOW_BIO_set_conn_port
#define BIO_set_data GRPC_SHADOW_BIO_set_data
#define BIO_set_fd GRPC_SHADOW_BIO_set_fd
#define BIO_set_flags GRPC_SHADOW_BIO_set_flags
#define BIO_set_fp GRPC_SHADOW_BIO_set_fp
#define BIO_set_init GRPC_SHADOW_BIO_set_init
#define BIO_set_mem_buf GRPC_SHADOW_BIO_set_mem_buf
#define BIO_set_mem_eof_return GRPC_SHADOW_BIO_set_mem_eof_return
#define BIO_set_nbio GRPC_SHADOW_BIO_set_nbio
#define BIO_set_retry_read GRPC_SHADOW_BIO_set_retry_read
#define BIO_set_retry_special GRPC_SHADOW_BIO_set_retry_special
#define BIO_set_retry_write GRPC_SHADOW_BIO_set_retry_write
#define BIO_set_shutdown GRPC_SHADOW_BIO_set_shutdown
#define BIO_set_ssl GRPC_SHADOW_BIO_set_ssl
#define BIO_set_write_buffer_size GRPC_SHADOW_BIO_set_write_buffer_size
#define BIO_should_io_special GRPC_SHADOW_BIO_should_io_special
#define BIO_should_read GRPC_SHADOW_BIO_should_read
#define BIO_should_retry GRPC_SHADOW_BIO_should_retry
#define BIO_should_write GRPC_SHADOW_BIO_should_write
#define BIO_shutdown_wr GRPC_SHADOW_BIO_shutdown_wr
#define BIO_snprintf GRPC_SHADOW_BIO_snprintf
#define BIO_test_flags GRPC_SHADOW_BIO_test_flags
#define BIO_up_ref GRPC_SHADOW_BIO_up_ref
#define BIO_vfree GRPC_SHADOW_BIO_vfree
#define BIO_vsnprintf GRPC_SHADOW_BIO_vsnprintf
#define BIO_wpending GRPC_SHADOW_BIO_wpending
#define BIO_write GRPC_SHADOW_BIO_write
#define BIO_write_all GRPC_SHADOW_BIO_write_all
#define BIO_write_filename GRPC_SHADOW_BIO_write_filename
#define BN_BLINDING_convert GRPC_SHADOW_BN_BLINDING_convert
#define BN_BLINDING_free GRPC_SHADOW_BN_BLINDING_free
#define BN_BLINDING_invert GRPC_SHADOW_BN_BLINDING_invert
#define BN_BLINDING_new GRPC_SHADOW_BN_BLINDING_new
#define BN_CTX_end GRPC_SHADOW_BN_CTX_end
#define BN_CTX_free GRPC_SHADOW_BN_CTX_free
#define BN_CTX_get GRPC_SHADOW_BN_CTX_get
#define BN_CTX_new GRPC_SHADOW_BN_CTX_new
#define BN_CTX_start GRPC_SHADOW_BN_CTX_start
#define BN_GENCB_call GRPC_SHADOW_BN_GENCB_call
#define BN_GENCB_set GRPC_SHADOW_BN_GENCB_set
#define BN_MONT_CTX_copy GRPC_SHADOW_BN_MONT_CTX_copy
#define BN_MONT_CTX_free GRPC_SHADOW_BN_MONT_CTX_free
#define BN_MONT_CTX_new GRPC_SHADOW_BN_MONT_CTX_new
#define BN_MONT_CTX_new_consttime GRPC_SHADOW_BN_MONT_CTX_new_consttime
#define BN_MONT_CTX_new_for_modulus GRPC_SHADOW_BN_MONT_CTX_new_for_modulus
#define BN_MONT_CTX_set GRPC_SHADOW_BN_MONT_CTX_set
#define BN_MONT_CTX_set_locked GRPC_SHADOW_BN_MONT_CTX_set_locked
#define BN_abs_is_word GRPC_SHADOW_BN_abs_is_word
#define BN_add GRPC_SHADOW_BN_add
#define BN_add_word GRPC_SHADOW_BN_add_word
#define BN_asc2bn GRPC_SHADOW_BN_asc2bn
#define BN_bin2bn GRPC_SHADOW_BN_bin2bn
#define BN_bn2bin GRPC_SHADOW_BN_bn2bin
#define BN_bn2bin_padded GRPC_SHADOW_BN_bn2bin_padded
#define BN_bn2binpad GRPC_SHADOW_BN_bn2binpad
#define BN_bn2cbb_padded GRPC_SHADOW_BN_bn2cbb_padded
#define BN_bn2dec GRPC_SHADOW_BN_bn2dec
#define BN_bn2hex GRPC_SHADOW_BN_bn2hex
#define BN_bn2le_padded GRPC_SHADOW_BN_bn2le_padded
#define BN_bn2mpi GRPC_SHADOW_BN_bn2mpi
#define BN_clear GRPC_SHADOW_BN_clear
#define BN_clear_bit GRPC_SHADOW_BN_clear_bit
#define BN_clear_free GRPC_SHADOW_BN_clear_free
#define BN_cmp GRPC_SHADOW_BN_cmp
#define BN_cmp_word GRPC_SHADOW_BN_cmp_word
#define BN_copy GRPC_SHADOW_BN_copy
#define BN_count_low_zero_bits GRPC_SHADOW_BN_count_low_zero_bits
#define BN_dec2bn GRPC_SHADOW_BN_dec2bn
#define BN_div GRPC_SHADOW_BN_div
#define BN_div_word GRPC_SHADOW_BN_div_word
#define BN_dup GRPC_SHADOW_BN_dup
#define BN_enhanced_miller_rabin_primality_test GRPC_SHADOW_BN_enhanced_miller_rabin_primality_test
#define BN_equal_consttime GRPC_SHADOW_BN_equal_consttime
#define BN_exp GRPC_SHADOW_BN_exp
#define BN_free GRPC_SHADOW_BN_free
#define BN_from_montgomery GRPC_SHADOW_BN_from_montgomery
#define BN_gcd GRPC_SHADOW_BN_gcd
#define BN_generate_prime_ex GRPC_SHADOW_BN_generate_prime_ex
#define BN_get_rfc3526_prime_1536 GRPC_SHADOW_BN_get_rfc3526_prime_1536
#define BN_get_u64 GRPC_SHADOW_BN_get_u64
#define BN_get_word GRPC_SHADOW_BN_get_word
#define BN_hex2bn GRPC_SHADOW_BN_hex2bn
#define BN_init GRPC_SHADOW_BN_init
#define BN_is_bit_set GRPC_SHADOW_BN_is_bit_set
#define BN_is_negative GRPC_SHADOW_BN_is_negative
#define BN_is_odd GRPC_SHADOW_BN_is_odd
#define BN_is_one GRPC_SHADOW_BN_is_one
#define BN_is_pow2 GRPC_SHADOW_BN_is_pow2
#define BN_is_prime_ex GRPC_SHADOW_BN_is_prime_ex
#define BN_is_prime_fasttest_ex GRPC_SHADOW_BN_is_prime_fasttest_ex
#define BN_is_word GRPC_SHADOW_BN_is_word
#define BN_is_zero GRPC_SHADOW_BN_is_zero
#define BN_le2bn GRPC_SHADOW_BN_le2bn
#define BN_lshift GRPC_SHADOW_BN_lshift
#define BN_lshift1 GRPC_SHADOW_BN_lshift1
#define BN_marshal_asn1 GRPC_SHADOW_BN_marshal_asn1
#define BN_mask_bits GRPC_SHADOW_BN_mask_bits
#define BN_mod_add GRPC_SHADOW_BN_mod_add
#define BN_mod_add_quick GRPC_SHADOW_BN_mod_add_quick
#define BN_mod_exp GRPC_SHADOW_BN_mod_exp
#define BN_mod_exp2_mont GRPC_SHADOW_BN_mod_exp2_mont
#define BN_mod_exp_mont GRPC_SHADOW_BN_mod_exp_mont
#define BN_mod_exp_mont_consttime GRPC_SHADOW_BN_mod_exp_mont_consttime
#define BN_mod_exp_mont_word GRPC_SHADOW_BN_mod_exp_mont_word
#define BN_mod_inverse GRPC_SHADOW_BN_mod_inverse
#define BN_mod_inverse_blinded GRPC_SHADOW_BN_mod_inverse_blinded
#define BN_mod_inverse_odd GRPC_SHADOW_BN_mod_inverse_odd
#define BN_mod_lshift GRPC_SHADOW_BN_mod_lshift
#define BN_mod_lshift1 GRPC_SHADOW_BN_mod_lshift1
#define BN_mod_lshift1_quick GRPC_SHADOW_BN_mod_lshift1_quick
#define BN_mod_lshift_quick GRPC_SHADOW_BN_mod_lshift_quick
#define BN_mod_mul GRPC_SHADOW_BN_mod_mul
#define BN_mod_mul_montgomery GRPC_SHADOW_BN_mod_mul_montgomery
#define BN_mod_pow2 GRPC_SHADOW_BN_mod_pow2
#define BN_mod_sqr GRPC_SHADOW_BN_mod_sqr
#define BN_mod_sqrt GRPC_SHADOW_BN_mod_sqrt
#define BN_mod_sub GRPC_SHADOW_BN_mod_sub
#define BN_mod_sub_quick GRPC_SHADOW_BN_mod_sub_quick
#define BN_mod_word GRPC_SHADOW_BN_mod_word
#define BN_mpi2bn GRPC_SHADOW_BN_mpi2bn
#define BN_mul GRPC_SHADOW_BN_mul
#define BN_mul_word GRPC_SHADOW_BN_mul_word
#define BN_new GRPC_SHADOW_BN_new
#define BN_nnmod GRPC_SHADOW_BN_nnmod
#define BN_nnmod_pow2 GRPC_SHADOW_BN_nnmod_pow2
#define BN_num_bits GRPC_SHADOW_BN_num_bits
#define BN_num_bits_word GRPC_SHADOW_BN_num_bits_word
#define BN_num_bytes GRPC_SHADOW_BN_num_bytes
#define BN_one GRPC_SHADOW_BN_one
#define BN_parse_asn1_unsigned GRPC_SHADOW_BN_parse_asn1_unsigned
#define BN_primality_test GRPC_SHADOW_BN_primality_test
#define BN_print GRPC_SHADOW_BN_print
#define BN_print_fp GRPC_SHADOW_BN_print_fp
#define BN_pseudo_rand GRPC_SHADOW_BN_pseudo_rand
#define BN_pseudo_rand_range GRPC_SHADOW_BN_pseudo_rand_range
#define BN_rand GRPC_SHADOW_BN_rand
#define BN_rand_range GRPC_SHADOW_BN_rand_range
#define BN_rand_range_ex GRPC_SHADOW_BN_rand_range_ex
#define BN_rshift GRPC_SHADOW_BN_rshift
#define BN_rshift1 GRPC_SHADOW_BN_rshift1
#define BN_set_bit GRPC_SHADOW_BN_set_bit
#define BN_set_negative GRPC_SHADOW_BN_set_negative
#define BN_set_u64 GRPC_SHADOW_BN_set_u64
#define BN_set_word GRPC_SHADOW_BN_set_word
#define BN_sqr GRPC_SHADOW_BN_sqr
#define BN_sqrt GRPC_SHADOW_BN_sqrt
#define BN_sub GRPC_SHADOW_BN_sub
#define BN_sub_word GRPC_SHADOW_BN_sub_word
#define BN_to_ASN1_ENUMERATED GRPC_SHADOW_BN_to_ASN1_ENUMERATED
#define BN_to_ASN1_INTEGER GRPC_SHADOW_BN_to_ASN1_INTEGER
#define BN_to_montgomery GRPC_SHADOW_BN_to_montgomery
#define BN_uadd GRPC_SHADOW_BN_uadd
#define BN_ucmp GRPC_SHADOW_BN_ucmp
#define BN_usub GRPC_SHADOW_BN_usub
#define BN_value_one GRPC_SHADOW_BN_value_one
#define BN_zero GRPC_SHADOW_BN_zero
#define BORINGSSL_function_hit GRPC_SHADOW_BORINGSSL_function_hit
#define BORINGSSL_self_test GRPC_SHADOW_BORINGSSL_self_test
#define BUF_MEM_append GRPC_SHADOW_BUF_MEM_append
#define BUF_MEM_free GRPC_SHADOW_BUF_MEM_free
#define BUF_MEM_grow GRPC_SHADOW_BUF_MEM_grow
#define BUF_MEM_grow_clean GRPC_SHADOW_BUF_MEM_grow_clean
#define BUF_MEM_new GRPC_SHADOW_BUF_MEM_new
#define BUF_MEM_reserve GRPC_SHADOW_BUF_MEM_reserve
#define BUF_memdup GRPC_SHADOW_BUF_memdup
#define BUF_strdup GRPC_SHADOW_BUF_strdup
#define BUF_strlcat GRPC_SHADOW_BUF_strlcat
#define BUF_strlcpy GRPC_SHADOW_BUF_strlcpy
#define BUF_strndup GRPC_SHADOW_BUF_strndup
#define BUF_strnlen GRPC_SHADOW_BUF_strnlen
#define CBB_add_asn1 GRPC_SHADOW_CBB_add_asn1
#define CBB_add_asn1_bool GRPC_SHADOW_CBB_add_asn1_bool
#define CBB_add_asn1_octet_string GRPC_SHADOW_CBB_add_asn1_octet_string
#define CBB_add_asn1_oid_from_text GRPC_SHADOW_CBB_add_asn1_oid_from_text
#define CBB_add_asn1_uint64 GRPC_SHADOW_CBB_add_asn1_uint64
#define CBB_add_bytes GRPC_SHADOW_CBB_add_bytes
#define CBB_add_space GRPC_SHADOW_CBB_add_space
#define CBB_add_u16 GRPC_SHADOW_CBB_add_u16
#define CBB_add_u16_length_prefixed GRPC_SHADOW_CBB_add_u16_length_prefixed
#define CBB_add_u24 GRPC_SHADOW_CBB_add_u24
#define CBB_add_u24_length_prefixed GRPC_SHADOW_CBB_add_u24_length_prefixed
#define CBB_add_u32 GRPC_SHADOW_CBB_add_u32
#define CBB_add_u64 GRPC_SHADOW_CBB_add_u64
#define CBB_add_u8 GRPC_SHADOW_CBB_add_u8
#define CBB_add_u8_length_prefixed GRPC_SHADOW_CBB_add_u8_length_prefixed
#define CBB_cleanup GRPC_SHADOW_CBB_cleanup
#define CBB_data GRPC_SHADOW_CBB_data
#define CBB_did_write GRPC_SHADOW_CBB_did_write
#define CBB_discard_child GRPC_SHADOW_CBB_discard_child
#define CBB_finish GRPC_SHADOW_CBB_finish
#define CBB_finish_i2d GRPC_SHADOW_CBB_finish_i2d
#define CBB_flush GRPC_SHADOW_CBB_flush
#define CBB_flush_asn1_set_of GRPC_SHADOW_CBB_flush_asn1_set_of
#define CBB_init GRPC_SHADOW_CBB_init
#define CBB_init_fixed GRPC_SHADOW_CBB_init_fixed
#define CBB_len GRPC_SHADOW_CBB_len
#define CBB_reserve GRPC_SHADOW_CBB_reserve
#define CBB_zero GRPC_SHADOW_CBB_zero
#define CBS_asn1_ber_to_der GRPC_SHADOW_CBS_asn1_ber_to_der
#define CBS_asn1_bitstring_has_bit GRPC_SHADOW_CBS_asn1_bitstring_has_bit
#define CBS_asn1_oid_to_text GRPC_SHADOW_CBS_asn1_oid_to_text
#define CBS_contains_zero_byte GRPC_SHADOW_CBS_contains_zero_byte
#define CBS_copy_bytes GRPC_SHADOW_CBS_copy_bytes
#define CBS_data GRPC_SHADOW_CBS_data
#define CBS_get_any_asn1 GRPC_SHADOW_CBS_get_any_asn1
#define CBS_get_any_asn1_element GRPC_SHADOW_CBS_get_any_asn1_element
#define CBS_get_any_ber_asn1_element GRPC_SHADOW_CBS_get_any_ber_asn1_element
#define CBS_get_asn1 GRPC_SHADOW_CBS_get_asn1
#define CBS_get_asn1_bool GRPC_SHADOW_CBS_get_asn1_bool
#define CBS_get_asn1_element GRPC_SHADOW_CBS_get_asn1_element
#define CBS_get_asn1_implicit_string GRPC_SHADOW_CBS_get_asn1_implicit_string
#define CBS_get_asn1_uint64 GRPC_SHADOW_CBS_get_asn1_uint64
#define CBS_get_bytes GRPC_SHADOW_CBS_get_bytes
#define CBS_get_last_u8 GRPC_SHADOW_CBS_get_last_u8
#define CBS_get_optional_asn1 GRPC_SHADOW_CBS_get_optional_asn1
#define CBS_get_optional_asn1_bool GRPC_SHADOW_CBS_get_optional_asn1_bool
#define CBS_get_optional_asn1_octet_string GRPC_SHADOW_CBS_get_optional_asn1_octet_string
#define CBS_get_optional_asn1_uint64 GRPC_SHADOW_CBS_get_optional_asn1_uint64
#define CBS_get_u16 GRPC_SHADOW_CBS_get_u16
#define CBS_get_u16_length_prefixed GRPC_SHADOW_CBS_get_u16_length_prefixed
#define CBS_get_u24 GRPC_SHADOW_CBS_get_u24
#define CBS_get_u24_length_prefixed GRPC_SHADOW_CBS_get_u24_length_prefixed
#define CBS_get_u32 GRPC_SHADOW_CBS_get_u32
#define CBS_get_u64 GRPC_SHADOW_CBS_get_u64
#define CBS_get_u8 GRPC_SHADOW_CBS_get_u8
#define CBS_get_u8_length_prefixed GRPC_SHADOW_CBS_get_u8_length_prefixed
#define CBS_init GRPC_SHADOW_CBS_init
#define CBS_is_valid_asn1_bitstring GRPC_SHADOW_CBS_is_valid_asn1_bitstring
#define CBS_len GRPC_SHADOW_CBS_len
#define CBS_mem_equal GRPC_SHADOW_CBS_mem_equal
#define CBS_peek_asn1_tag GRPC_SHADOW_CBS_peek_asn1_tag
#define CBS_skip GRPC_SHADOW_CBS_skip
#define CBS_stow GRPC_SHADOW_CBS_stow
#define CBS_strdup GRPC_SHADOW_CBS_strdup
#define CERTIFICATEPOLICIES_free GRPC_SHADOW_CERTIFICATEPOLICIES_free
#define CERTIFICATEPOLICIES_it GRPC_SHADOW_CERTIFICATEPOLICIES_it
#define CERTIFICATEPOLICIES_new GRPC_SHADOW_CERTIFICATEPOLICIES_new
#define CMAC_CTX_copy GRPC_SHADOW_CMAC_CTX_copy
#define CMAC_CTX_free GRPC_SHADOW_CMAC_CTX_free
#define CMAC_CTX_new GRPC_SHADOW_CMAC_CTX_new
#define CMAC_Final GRPC_SHADOW_CMAC_Final
#define CMAC_Init GRPC_SHADOW_CMAC_Init
#define CMAC_Reset GRPC_SHADOW_CMAC_Reset
#define CMAC_Update GRPC_SHADOW_CMAC_Update
#define CONF_VALUE_new GRPC_SHADOW_CONF_VALUE_new
#define CONF_modules_free GRPC_SHADOW_CONF_modules_free
#define CONF_modules_load_file GRPC_SHADOW_CONF_modules_load_file
#define CONF_parse_list GRPC_SHADOW_CONF_parse_list
#define CRL_DIST_POINTS_free GRPC_SHADOW_CRL_DIST_POINTS_free
#define CRL_DIST_POINTS_it GRPC_SHADOW_CRL_DIST_POINTS_it
#define CRL_DIST_POINTS_new GRPC_SHADOW_CRL_DIST_POINTS_new
#define CRYPTO_BUFFER_POOL_free GRPC_SHADOW_CRYPTO_BUFFER_POOL_free
#define CRYPTO_BUFFER_POOL_new GRPC_SHADOW_CRYPTO_BUFFER_POOL_new
#define CRYPTO_BUFFER_alloc GRPC_SHADOW_CRYPTO_BUFFER_alloc
#define CRYPTO_BUFFER_data GRPC_SHADOW_CRYPTO_BUFFER_data
#define CRYPTO_BUFFER_free GRPC_SHADOW_CRYPTO_BUFFER_free
#define CRYPTO_BUFFER_init_CBS GRPC_SHADOW_CRYPTO_BUFFER_init_CBS
#define CRYPTO_BUFFER_len GRPC_SHADOW_CRYPTO_BUFFER_len
#define CRYPTO_BUFFER_new GRPC_SHADOW_CRYPTO_BUFFER_new
#define CRYPTO_BUFFER_new_from_CBS GRPC_SHADOW_CRYPTO_BUFFER_new_from_CBS
#define CRYPTO_BUFFER_up_ref GRPC_SHADOW_CRYPTO_BUFFER_up_ref
#define CRYPTO_MUTEX_cleanup GRPC_SHADOW_CRYPTO_MUTEX_cleanup
#define CRYPTO_MUTEX_init GRPC_SHADOW_CRYPTO_MUTEX_init
#define CRYPTO_MUTEX_lock_read GRPC_SHADOW_CRYPTO_MUTEX_lock_read
#define CRYPTO_MUTEX_lock_write GRPC_SHADOW_CRYPTO_MUTEX_lock_write
#define CRYPTO_MUTEX_unlock_read GRPC_SHADOW_CRYPTO_MUTEX_unlock_read
#define CRYPTO_MUTEX_unlock_write GRPC_SHADOW_CRYPTO_MUTEX_unlock_write
#define CRYPTO_POLYVAL_finish GRPC_SHADOW_CRYPTO_POLYVAL_finish
#define CRYPTO_POLYVAL_init GRPC_SHADOW_CRYPTO_POLYVAL_init
#define CRYPTO_POLYVAL_update_blocks GRPC_SHADOW_CRYPTO_POLYVAL_update_blocks
#define CRYPTO_STATIC_MUTEX_lock_read GRPC_SHADOW_CRYPTO_STATIC_MUTEX_lock_read
#define CRYPTO_STATIC_MUTEX_lock_write GRPC_SHADOW_CRYPTO_STATIC_MUTEX_lock_write
#define CRYPTO_STATIC_MUTEX_unlock_read GRPC_SHADOW_CRYPTO_STATIC_MUTEX_unlock_read
#define CRYPTO_STATIC_MUTEX_unlock_write GRPC_SHADOW_CRYPTO_STATIC_MUTEX_unlock_write
#define CRYPTO_THREADID_current GRPC_SHADOW_CRYPTO_THREADID_current
#define CRYPTO_THREADID_set_callback GRPC_SHADOW_CRYPTO_THREADID_set_callback
#define CRYPTO_THREADID_set_numeric GRPC_SHADOW_CRYPTO_THREADID_set_numeric
#define CRYPTO_THREADID_set_pointer GRPC_SHADOW_CRYPTO_THREADID_set_pointer
#define CRYPTO_cbc128_decrypt GRPC_SHADOW_CRYPTO_cbc128_decrypt
#define CRYPTO_cbc128_encrypt GRPC_SHADOW_CRYPTO_cbc128_encrypt
#define CRYPTO_cfb128_1_encrypt GRPC_SHADOW_CRYPTO_cfb128_1_encrypt
#define CRYPTO_cfb128_8_encrypt GRPC_SHADOW_CRYPTO_cfb128_8_encrypt
#define CRYPTO_cfb128_encrypt GRPC_SHADOW_CRYPTO_cfb128_encrypt
#define CRYPTO_chacha_20 GRPC_SHADOW_CRYPTO_chacha_20
#define CRYPTO_cleanup_all_ex_data GRPC_SHADOW_CRYPTO_cleanup_all_ex_data
#define CRYPTO_ctr128_encrypt GRPC_SHADOW_CRYPTO_ctr128_encrypt
#define CRYPTO_ctr128_encrypt_ctr32 GRPC_SHADOW_CRYPTO_ctr128_encrypt_ctr32
#define CRYPTO_free_ex_data GRPC_SHADOW_CRYPTO_free_ex_data
#define CRYPTO_gcm128_aad GRPC_SHADOW_CRYPTO_gcm128_aad
#define CRYPTO_gcm128_decrypt GRPC_SHADOW_CRYPTO_gcm128_decrypt
#define CRYPTO_gcm128_decrypt_ctr32 GRPC_SHADOW_CRYPTO_gcm128_decrypt_ctr32
#define CRYPTO_gcm128_encrypt GRPC_SHADOW_CRYPTO_gcm128_encrypt
#define CRYPTO_gcm128_encrypt_ctr32 GRPC_SHADOW_CRYPTO_gcm128_encrypt_ctr32
#define CRYPTO_gcm128_finish GRPC_SHADOW_CRYPTO_gcm128_finish
#define CRYPTO_gcm128_init_key GRPC_SHADOW_CRYPTO_gcm128_init_key
#define CRYPTO_gcm128_setiv GRPC_SHADOW_CRYPTO_gcm128_setiv
#define CRYPTO_gcm128_tag GRPC_SHADOW_CRYPTO_gcm128_tag
#define CRYPTO_get_dynlock_create_callback GRPC_SHADOW_CRYPTO_get_dynlock_create_callback
#define CRYPTO_get_dynlock_destroy_callback GRPC_SHADOW_CRYPTO_get_dynlock_destroy_callback
#define CRYPTO_get_dynlock_lock_callback GRPC_SHADOW_CRYPTO_get_dynlock_lock_callback
#define CRYPTO_get_ex_data GRPC_SHADOW_CRYPTO_get_ex_data
#define CRYPTO_get_ex_new_index GRPC_SHADOW_CRYPTO_get_ex_new_index
#define CRYPTO_get_lock_name GRPC_SHADOW_CRYPTO_get_lock_name
#define CRYPTO_get_locking_callback GRPC_SHADOW_CRYPTO_get_locking_callback
#define CRYPTO_get_thread_local GRPC_SHADOW_CRYPTO_get_thread_local
#define CRYPTO_ghash_init GRPC_SHADOW_CRYPTO_ghash_init
#define CRYPTO_has_asm GRPC_SHADOW_CRYPTO_has_asm
#define CRYPTO_hchacha20 GRPC_SHADOW_CRYPTO_hchacha20
#define CRYPTO_is_confidential_build GRPC_SHADOW_CRYPTO_is_confidential_build
#define CRYPTO_library_init GRPC_SHADOW_CRYPTO_library_init
#define CRYPTO_malloc_init GRPC_SHADOW_CRYPTO_malloc_init
#define CRYPTO_memcmp GRPC_SHADOW_CRYPTO_memcmp
#define CRYPTO_new_ex_data GRPC_SHADOW_CRYPTO_new_ex_data
#define CRYPTO_num_locks GRPC_SHADOW_CRYPTO_num_locks
#define CRYPTO_ofb128_encrypt GRPC_SHADOW_CRYPTO_ofb128_encrypt
#define CRYPTO_once GRPC_SHADOW_CRYPTO_once
#define CRYPTO_poly1305_finish GRPC_SHADOW_CRYPTO_poly1305_finish
#define CRYPTO_poly1305_init GRPC_SHADOW_CRYPTO_poly1305_init
#define CRYPTO_poly1305_update GRPC_SHADOW_CRYPTO_poly1305_update
#define CRYPTO_rdrand GRPC_SHADOW_CRYPTO_rdrand
#define CRYPTO_rdrand_multiple8_buf GRPC_SHADOW_CRYPTO_rdrand_multiple8_buf
#define CRYPTO_refcount_dec_and_test_zero GRPC_SHADOW_CRYPTO_refcount_dec_and_test_zero
#define CRYPTO_refcount_inc GRPC_SHADOW_CRYPTO_refcount_inc
#define CRYPTO_set_add_lock_callback GRPC_SHADOW_CRYPTO_set_add_lock_callback
#define CRYPTO_set_dynlock_create_callback GRPC_SHADOW_CRYPTO_set_dynlock_create_callback
#define CRYPTO_set_dynlock_destroy_callback GRPC_SHADOW_CRYPTO_set_dynlock_destroy_callback
#define CRYPTO_set_dynlock_lock_callback GRPC_SHADOW_CRYPTO_set_dynlock_lock_callback
#define CRYPTO_set_ex_data GRPC_SHADOW_CRYPTO_set_ex_data
#define CRYPTO_set_id_callback GRPC_SHADOW_CRYPTO_set_id_callback
#define CRYPTO_set_locking_callback GRPC_SHADOW_CRYPTO_set_locking_callback
#define CRYPTO_set_thread_local GRPC_SHADOW_CRYPTO_set_thread_local
#define CRYPTO_sysrand GRPC_SHADOW_CRYPTO_sysrand
#define CRYPTO_tls1_prf GRPC_SHADOW_CRYPTO_tls1_prf
#define CTR_DRBG_clear GRPC_SHADOW_CTR_DRBG_clear
#define CTR_DRBG_generate GRPC_SHADOW_CTR_DRBG_generate
#define CTR_DRBG_init GRPC_SHADOW_CTR_DRBG_init
#define CTR_DRBG_reseed GRPC_SHADOW_CTR_DRBG_reseed
#define ChaCha20_ctr32 GRPC_SHADOW_ChaCha20_ctr32
#define DES_decrypt3 GRPC_SHADOW_DES_decrypt3
#define DES_ecb3_encrypt GRPC_SHADOW_DES_ecb3_encrypt
#define DES_ecb_encrypt GRPC_SHADOW_DES_ecb_encrypt
#define DES_ede2_cbc_encrypt GRPC_SHADOW_DES_ede2_cbc_encrypt
#define DES_ede3_cbc_encrypt GRPC_SHADOW_DES_ede3_cbc_encrypt
#define DES_encrypt3 GRPC_SHADOW_DES_encrypt3
#define DES_ncbc_encrypt GRPC_SHADOW_DES_ncbc_encrypt
#define DES_set_key GRPC_SHADOW_DES_set_key
#define DES_set_key_unchecked GRPC_SHADOW_DES_set_key_unchecked
#define DES_set_odd_parity GRPC_SHADOW_DES_set_odd_parity
#define DH_check GRPC_SHADOW_DH_check
#define DH_check_pub_key GRPC_SHADOW_DH_check_pub_key
#define DH_compute_key GRPC_SHADOW_DH_compute_key
#define DH_free GRPC_SHADOW_DH_free
#define DH_generate_key GRPC_SHADOW_DH_generate_key
#define DH_generate_parameters_ex GRPC_SHADOW_DH_generate_parameters_ex
#define DH_get0_key GRPC_SHADOW_DH_get0_key
#define DH_get0_pqg GRPC_SHADOW_DH_get0_pqg
#define DH_get_ex_data GRPC_SHADOW_DH_get_ex_data
#define DH_get_ex_new_index GRPC_SHADOW_DH_get_ex_new_index
#define DH_marshal_parameters GRPC_SHADOW_DH_marshal_parameters
#define DH_new GRPC_SHADOW_DH_new
#define DH_num_bits GRPC_SHADOW_DH_num_bits
#define DH_parse_parameters GRPC_SHADOW_DH_parse_parameters
#define DH_set0_key GRPC_SHADOW_DH_set0_key
#define DH_set0_pqg GRPC_SHADOW_DH_set0_pqg
#define DH_set_ex_data GRPC_SHADOW_DH_set_ex_data
#define DH_size GRPC_SHADOW_DH_size
#define DH_up_ref GRPC_SHADOW_DH_up_ref
#define DHparams_dup GRPC_SHADOW_DHparams_dup
#define DIRECTORYSTRING_free GRPC_SHADOW_DIRECTORYSTRING_free
#define DIRECTORYSTRING_it GRPC_SHADOW_DIRECTORYSTRING_it
#define DIRECTORYSTRING_new GRPC_SHADOW_DIRECTORYSTRING_new
#define DISPLAYTEXT_free GRPC_SHADOW_DISPLAYTEXT_free
#define DISPLAYTEXT_it GRPC_SHADOW_DISPLAYTEXT_it
#define DISPLAYTEXT_new GRPC_SHADOW_DISPLAYTEXT_new
#define DIST_POINT_NAME_free GRPC_SHADOW_DIST_POINT_NAME_free
#define DIST_POINT_NAME_it GRPC_SHADOW_DIST_POINT_NAME_it
#define DIST_POINT_NAME_new GRPC_SHADOW_DIST_POINT_NAME_new
#define DIST_POINT_free GRPC_SHADOW_DIST_POINT_free
#define DIST_POINT_it GRPC_SHADOW_DIST_POINT_it
#define DIST_POINT_new GRPC_SHADOW_DIST_POINT_new
#define DIST_POINT_set_dpname GRPC_SHADOW_DIST_POINT_set_dpname
#define DSA_SIG_free GRPC_SHADOW_DSA_SIG_free
#define DSA_SIG_marshal GRPC_SHADOW_DSA_SIG_marshal
#define DSA_SIG_new GRPC_SHADOW_DSA_SIG_new
#define DSA_SIG_parse GRPC_SHADOW_DSA_SIG_parse
#define DSA_check_signature GRPC_SHADOW_DSA_check_signature
#define DSA_do_check_signature GRPC_SHADOW_DSA_do_check_signature
#define DSA_do_sign GRPC_SHADOW_DSA_do_sign
#define DSA_do_verify GRPC_SHADOW_DSA_do_verify
#define DSA_dup_DH GRPC_SHADOW_DSA_dup_DH
#define DSA_free GRPC_SHADOW_DSA_free
#define DSA_generate_key GRPC_SHADOW_DSA_generate_key
#define DSA_generate_parameters_ex GRPC_SHADOW_DSA_generate_parameters_ex
#define DSA_get0_key GRPC_SHADOW_DSA_get0_key
#define DSA_get0_pqg GRPC_SHADOW_DSA_get0_pqg
#define DSA_get_ex_data GRPC_SHADOW_DSA_get_ex_data
#define DSA_get_ex_new_index GRPC_SHADOW_DSA_get_ex_new_index
#define DSA_marshal_parameters GRPC_SHADOW_DSA_marshal_parameters
#define DSA_marshal_private_key GRPC_SHADOW_DSA_marshal_private_key
#define DSA_marshal_public_key GRPC_SHADOW_DSA_marshal_public_key
#define DSA_new GRPC_SHADOW_DSA_new
#define DSA_parse_parameters GRPC_SHADOW_DSA_parse_parameters
#define DSA_parse_private_key GRPC_SHADOW_DSA_parse_private_key
#define DSA_parse_public_key GRPC_SHADOW_DSA_parse_public_key
#define DSA_set0_key GRPC_SHADOW_DSA_set0_key
#define DSA_set0_pqg GRPC_SHADOW_DSA_set0_pqg
#define DSA_set_ex_data GRPC_SHADOW_DSA_set_ex_data
#define DSA_sign GRPC_SHADOW_DSA_sign
#define DSA_size GRPC_SHADOW_DSA_size
#define DSA_up_ref GRPC_SHADOW_DSA_up_ref
#define DSA_verify GRPC_SHADOW_DSA_verify
#define DSAparams_dup GRPC_SHADOW_DSAparams_dup
#define DTLS_client_method GRPC_SHADOW_DTLS_client_method
#define DTLS_method GRPC_SHADOW_DTLS_method
#define DTLS_server_method GRPC_SHADOW_DTLS_server_method
#define DTLS_with_buffers_method GRPC_SHADOW_DTLS_with_buffers_method
#define DTLSv1_2_client_method GRPC_SHADOW_DTLSv1_2_client_method
#define DTLSv1_2_method GRPC_SHADOW_DTLSv1_2_method
#define DTLSv1_2_server_method GRPC_SHADOW_DTLSv1_2_server_method
#define DTLSv1_client_method GRPC_SHADOW_DTLSv1_client_method
#define DTLSv1_get_timeout GRPC_SHADOW_DTLSv1_get_timeout
#define DTLSv1_handle_timeout GRPC_SHADOW_DTLSv1_handle_timeout
#define DTLSv1_method GRPC_SHADOW_DTLSv1_method
#define DTLSv1_server_method GRPC_SHADOW_DTLSv1_server_method
#define DTLSv1_set_initial_timeout_duration GRPC_SHADOW_DTLSv1_set_initial_timeout_duration
#define ECDH_compute_key GRPC_SHADOW_ECDH_compute_key
#define ECDH_compute_key_fips GRPC_SHADOW_ECDH_compute_key_fips
#define ECDSA_SIG_free GRPC_SHADOW_ECDSA_SIG_free
#define ECDSA_SIG_from_bytes GRPC_SHADOW_ECDSA_SIG_from_bytes
#define ECDSA_SIG_get0 GRPC_SHADOW_ECDSA_SIG_get0
#define ECDSA_SIG_marshal GRPC_SHADOW_ECDSA_SIG_marshal
#define ECDSA_SIG_max_len GRPC_SHADOW_ECDSA_SIG_max_len
#define ECDSA_SIG_new GRPC_SHADOW_ECDSA_SIG_new
#define ECDSA_SIG_parse GRPC_SHADOW_ECDSA_SIG_parse
#define ECDSA_SIG_set0 GRPC_SHADOW_ECDSA_SIG_set0
#define ECDSA_SIG_to_bytes GRPC_SHADOW_ECDSA_SIG_to_bytes
#define ECDSA_do_sign GRPC_SHADOW_ECDSA_do_sign
#define ECDSA_do_verify GRPC_SHADOW_ECDSA_do_verify
#define ECDSA_sign GRPC_SHADOW_ECDSA_sign
#define ECDSA_size GRPC_SHADOW_ECDSA_size
#define ECDSA_verify GRPC_SHADOW_ECDSA_verify
#define EC_GFp_mont_method GRPC_SHADOW_EC_GFp_mont_method
#define EC_GFp_nistp224_method GRPC_SHADOW_EC_GFp_nistp224_method
#define EC_GFp_nistp256_method GRPC_SHADOW_EC_GFp_nistp256_method
#define EC_GFp_nistz256_method GRPC_SHADOW_EC_GFp_nistz256_method
#define EC_GROUP_cmp GRPC_SHADOW_EC_GROUP_cmp
#define EC_GROUP_dup GRPC_SHADOW_EC_GROUP_dup
#define EC_GROUP_free GRPC_SHADOW_EC_GROUP_free
#define EC_GROUP_get0_generator GRPC_SHADOW_EC_GROUP_get0_generator
#define EC_GROUP_get0_order GRPC_SHADOW_EC_GROUP_get0_order
#define EC_GROUP_get_cofactor GRPC_SHADOW_EC_GROUP_get_cofactor
#define EC_GROUP_get_curve_GFp GRPC_SHADOW_EC_GROUP_get_curve_GFp
#define EC_GROUP_get_curve_name GRPC_SHADOW_EC_GROUP_get_curve_name
#define EC_GROUP_get_degree GRPC_SHADOW_EC_GROUP_get_degree
#define EC_GROUP_get_order GRPC_SHADOW_EC_GROUP_get_order
#define EC_GROUP_method_of GRPC_SHADOW_EC_GROUP_method_of
#define EC_GROUP_new_by_curve_name GRPC_SHADOW_EC_GROUP_new_by_curve_name
#define EC_GROUP_new_curve_GFp GRPC_SHADOW_EC_GROUP_new_curve_GFp
#define EC_GROUP_order_bits GRPC_SHADOW_EC_GROUP_order_bits
#define EC_GROUP_set_asn1_flag GRPC_SHADOW_EC_GROUP_set_asn1_flag
#define EC_GROUP_set_generator GRPC_SHADOW_EC_GROUP_set_generator
#define EC_GROUP_set_point_conversion_form GRPC_SHADOW_EC_GROUP_set_point_conversion_form
#define EC_KEY_check_fips GRPC_SHADOW_EC_KEY_check_fips
#define EC_KEY_check_key GRPC_SHADOW_EC_KEY_check_key
#define EC_KEY_derive_from_secret GRPC_SHADOW_EC_KEY_derive_from_secret
#define EC_KEY_dup GRPC_SHADOW_EC_KEY_dup
#define EC_KEY_free GRPC_SHADOW_EC_KEY_free
#define EC_KEY_generate_key GRPC_SHADOW_EC_KEY_generate_key
#define EC_KEY_generate_key_fips GRPC_SHADOW_EC_KEY_generate_key_fips
#define EC_KEY_get0_group GRPC_SHADOW_EC_KEY_get0_group
#define EC_KEY_get0_private_key GRPC_SHADOW_EC_KEY_get0_private_key
#define EC_KEY_get0_public_key GRPC_SHADOW_EC_KEY_get0_public_key
#define EC_KEY_get_conv_form GRPC_SHADOW_EC_KEY_get_conv_form
#define EC_KEY_get_enc_flags GRPC_SHADOW_EC_KEY_get_enc_flags
#define EC_KEY_get_ex_data GRPC_SHADOW_EC_KEY_get_ex_data
#define EC_KEY_get_ex_new_index GRPC_SHADOW_EC_KEY_get_ex_new_index
#define EC_KEY_is_opaque GRPC_SHADOW_EC_KEY_is_opaque
#define EC_KEY_key2buf GRPC_SHADOW_EC_KEY_key2buf
#define EC_KEY_marshal_curve_name GRPC_SHADOW_EC_KEY_marshal_curve_name
#define EC_KEY_marshal_private_key GRPC_SHADOW_EC_KEY_marshal_private_key
#define EC_KEY_new GRPC_SHADOW_EC_KEY_new
#define EC_KEY_new_by_curve_name GRPC_SHADOW_EC_KEY_new_by_curve_name
#define EC_KEY_new_method GRPC_SHADOW_EC_KEY_new_method
#define EC_KEY_parse_curve_name GRPC_SHADOW_EC_KEY_parse_curve_name
#define EC_KEY_parse_parameters GRPC_SHADOW_EC_KEY_parse_parameters
#define EC_KEY_parse_private_key GRPC_SHADOW_EC_KEY_parse_private_key
#define EC_KEY_set_asn1_flag GRPC_SHADOW_EC_KEY_set_asn1_flag
#define EC_KEY_set_conv_form GRPC_SHADOW_EC_KEY_set_conv_form
#define EC_KEY_set_enc_flags GRPC_SHADOW_EC_KEY_set_enc_flags
#define EC_KEY_set_ex_data GRPC_SHADOW_EC_KEY_set_ex_data
#define EC_KEY_set_group GRPC_SHADOW_EC_KEY_set_group
#define EC_KEY_set_private_key GRPC_SHADOW_EC_KEY_set_private_key
#define EC_KEY_set_public_key GRPC_SHADOW_EC_KEY_set_public_key
#define EC_KEY_set_public_key_affine_coordinates GRPC_SHADOW_EC_KEY_set_public_key_affine_coordinates
#define EC_KEY_up_ref GRPC_SHADOW_EC_KEY_up_ref
#define EC_METHOD_get_field_type GRPC_SHADOW_EC_METHOD_get_field_type
#define EC_POINT_add GRPC_SHADOW_EC_POINT_add
#define EC_POINT_clear_free GRPC_SHADOW_EC_POINT_clear_free
#define EC_POINT_cmp GRPC_SHADOW_EC_POINT_cmp
#define EC_POINT_copy GRPC_SHADOW_EC_POINT_copy
#define EC_POINT_dbl GRPC_SHADOW_EC_POINT_dbl
#define EC_POINT_dup GRPC_SHADOW_EC_POINT_dup
#define EC_POINT_free GRPC_SHADOW_EC_POINT_free
#define EC_POINT_get_affine_coordinates_GFp GRPC_SHADOW_EC_POINT_get_affine_coordinates_GFp
#define EC_POINT_invert GRPC_SHADOW_EC_POINT_invert
#define EC_POINT_is_at_infinity GRPC_SHADOW_EC_POINT_is_at_infinity
#define EC_POINT_is_on_curve GRPC_SHADOW_EC_POINT_is_on_curve
#define EC_POINT_mul GRPC_SHADOW_EC_POINT_mul
#define EC_POINT_new GRPC_SHADOW_EC_POINT_new
#define EC_POINT_oct2point GRPC_SHADOW_EC_POINT_oct2point
#define EC_POINT_point2cbb GRPC_SHADOW_EC_POINT_point2cbb
#define EC_POINT_point2oct GRPC_SHADOW_EC_POINT_point2oct
#define EC_POINT_set_affine_coordinates_GFp GRPC_SHADOW_EC_POINT_set_affine_coordinates_GFp
#define EC_POINT_set_compressed_coordinates_GFp GRPC_SHADOW_EC_POINT_set_compressed_coordinates_GFp
#define EC_POINT_set_to_infinity GRPC_SHADOW_EC_POINT_set_to_infinity
#define EC_curve_nid2nist GRPC_SHADOW_EC_curve_nid2nist
#define EC_curve_nist2nid GRPC_SHADOW_EC_curve_nist2nid
#define EC_get_builtin_curves GRPC_SHADOW_EC_get_builtin_curves
#define ED25519_keypair GRPC_SHADOW_ED25519_keypair
#define ED25519_keypair_from_seed GRPC_SHADOW_ED25519_keypair_from_seed
#define ED25519_sign GRPC_SHADOW_ED25519_sign
#define ED25519_verify GRPC_SHADOW_ED25519_verify
#define EDIPARTYNAME_free GRPC_SHADOW_EDIPARTYNAME_free
#define EDIPARTYNAME_it GRPC_SHADOW_EDIPARTYNAME_it
#define EDIPARTYNAME_new GRPC_SHADOW_EDIPARTYNAME_new
#define ENGINE_free GRPC_SHADOW_ENGINE_free
#define ENGINE_get_ECDSA_method GRPC_SHADOW_ENGINE_get_ECDSA_method
#define ENGINE_get_RSA_method GRPC_SHADOW_ENGINE_get_RSA_method
#define ENGINE_load_builtin_engines GRPC_SHADOW_ENGINE_load_builtin_engines
#define ENGINE_new GRPC_SHADOW_ENGINE_new
#define ENGINE_register_all_complete GRPC_SHADOW_ENGINE_register_all_complete
#define ENGINE_set_ECDSA_method GRPC_SHADOW_ENGINE_set_ECDSA_method
#define ENGINE_set_RSA_method GRPC_SHADOW_ENGINE_set_RSA_method
#define ERR_SAVE_STATE_free GRPC_SHADOW_ERR_SAVE_STATE_free
#define ERR_add_error_data GRPC_SHADOW_ERR_add_error_data
#define ERR_add_error_dataf GRPC_SHADOW_ERR_add_error_dataf
#define ERR_clear_error GRPC_SHADOW_ERR_clear_error
#define ERR_clear_system_error GRPC_SHADOW_ERR_clear_system_error
#define ERR_error_string GRPC_SHADOW_ERR_error_string
#define ERR_error_string_n GRPC_SHADOW_ERR_error_string_n
#define ERR_free_strings GRPC_SHADOW_ERR_free_strings
#define ERR_func_error_string GRPC_SHADOW_ERR_func_error_string
#define ERR_get_error GRPC_SHADOW_ERR_get_error
#define ERR_get_error_line GRPC_SHADOW_ERR_get_error_line
#define ERR_get_error_line_data GRPC_SHADOW_ERR_get_error_line_data
#define ERR_get_next_error_library GRPC_SHADOW_ERR_get_next_error_library
#define ERR_lib_error_string GRPC_SHADOW_ERR_lib_error_string
#define ERR_load_BIO_strings GRPC_SHADOW_ERR_load_BIO_strings
#define ERR_load_ERR_strings GRPC_SHADOW_ERR_load_ERR_strings
#define ERR_load_RAND_strings GRPC_SHADOW_ERR_load_RAND_strings
#define ERR_load_SSL_strings GRPC_SHADOW_ERR_load_SSL_strings
#define ERR_load_crypto_strings GRPC_SHADOW_ERR_load_crypto_strings
#define ERR_peek_error GRPC_SHADOW_ERR_peek_error
#define ERR_peek_error_line GRPC_SHADOW_ERR_peek_error_line
#define ERR_peek_error_line_data GRPC_SHADOW_ERR_peek_error_line_data
#define ERR_peek_last_error GRPC_SHADOW_ERR_peek_last_error
#define ERR_peek_last_error_line GRPC_SHADOW_ERR_peek_last_error_line
#define ERR_peek_last_error_line_data GRPC_SHADOW_ERR_peek_last_error_line_data
#define ERR_pop_to_mark GRPC_SHADOW_ERR_pop_to_mark
#define ERR_print_errors GRPC_SHADOW_ERR_print_errors
#define ERR_print_errors_cb GRPC_SHADOW_ERR_print_errors_cb
#define ERR_print_errors_fp GRPC_SHADOW_ERR_print_errors_fp
#define ERR_put_error GRPC_SHADOW_ERR_put_error
#define ERR_reason_error_string GRPC_SHADOW_ERR_reason_error_string
#define ERR_remove_state GRPC_SHADOW_ERR_remove_state
#define ERR_remove_thread_state GRPC_SHADOW_ERR_remove_thread_state
#define ERR_restore_state GRPC_SHADOW_ERR_restore_state
#define ERR_save_state GRPC_SHADOW_ERR_save_state
#define ERR_set_mark GRPC_SHADOW_ERR_set_mark
#define EVP_AEAD_CTX_aead GRPC_SHADOW_EVP_AEAD_CTX_aead
#define EVP_AEAD_CTX_cleanup GRPC_SHADOW_EVP_AEAD_CTX_cleanup
#define EVP_AEAD_CTX_free GRPC_SHADOW_EVP_AEAD_CTX_free
#define EVP_AEAD_CTX_get_iv GRPC_SHADOW_EVP_AEAD_CTX_get_iv
#define EVP_AEAD_CTX_init GRPC_SHADOW_EVP_AEAD_CTX_init
#define EVP_AEAD_CTX_init_with_direction GRPC_SHADOW_EVP_AEAD_CTX_init_with_direction
#define EVP_AEAD_CTX_new GRPC_SHADOW_EVP_AEAD_CTX_new
#define EVP_AEAD_CTX_open GRPC_SHADOW_EVP_AEAD_CTX_open
#define EVP_AEAD_CTX_open_gather GRPC_SHADOW_EVP_AEAD_CTX_open_gather
#define EVP_AEAD_CTX_seal GRPC_SHADOW_EVP_AEAD_CTX_seal
#define EVP_AEAD_CTX_seal_scatter GRPC_SHADOW_EVP_AEAD_CTX_seal_scatter
#define EVP_AEAD_CTX_tag_len GRPC_SHADOW_EVP_AEAD_CTX_tag_len
#define EVP_AEAD_CTX_zero GRPC_SHADOW_EVP_AEAD_CTX_zero
#define EVP_AEAD_key_length GRPC_SHADOW_EVP_AEAD_key_length
#define EVP_AEAD_max_overhead GRPC_SHADOW_EVP_AEAD_max_overhead
#define EVP_AEAD_max_tag_len GRPC_SHADOW_EVP_AEAD_max_tag_len
#define EVP_AEAD_nonce_length GRPC_SHADOW_EVP_AEAD_nonce_length
#define EVP_BytesToKey GRPC_SHADOW_EVP_BytesToKey
#define EVP_CIPHER_CTX_block_size GRPC_SHADOW_EVP_CIPHER_CTX_block_size
#define EVP_CIPHER_CTX_cipher GRPC_SHADOW_EVP_CIPHER_CTX_cipher
#define EVP_CIPHER_CTX_cleanup GRPC_SHADOW_EVP_CIPHER_CTX_cleanup
#define EVP_CIPHER_CTX_copy GRPC_SHADOW_EVP_CIPHER_CTX_copy
#define EVP_CIPHER_CTX_ctrl GRPC_SHADOW_EVP_CIPHER_CTX_ctrl
#define EVP_CIPHER_CTX_encrypting GRPC_SHADOW_EVP_CIPHER_CTX_encrypting
#define EVP_CIPHER_CTX_flags GRPC_SHADOW_EVP_CIPHER_CTX_flags
#define EVP_CIPHER_CTX_free GRPC_SHADOW_EVP_CIPHER_CTX_free
#define EVP_CIPHER_CTX_get_app_data GRPC_SHADOW_EVP_CIPHER_CTX_get_app_data
#define EVP_CIPHER_CTX_init GRPC_SHADOW_EVP_CIPHER_CTX_init
#define EVP_CIPHER_CTX_iv_length GRPC_SHADOW_EVP_CIPHER_CTX_iv_length
#define EVP_CIPHER_CTX_key_length GRPC_SHADOW_EVP_CIPHER_CTX_key_length
#define EVP_CIPHER_CTX_mode GRPC_SHADOW_EVP_CIPHER_CTX_mode
#define EVP_CIPHER_CTX_new GRPC_SHADOW_EVP_CIPHER_CTX_new
#define EVP_CIPHER_CTX_nid GRPC_SHADOW_EVP_CIPHER_CTX_nid
#define EVP_CIPHER_CTX_reset GRPC_SHADOW_EVP_CIPHER_CTX_reset
#define EVP_CIPHER_CTX_set_app_data GRPC_SHADOW_EVP_CIPHER_CTX_set_app_data
#define EVP_CIPHER_CTX_set_flags GRPC_SHADOW_EVP_CIPHER_CTX_set_flags
#define EVP_CIPHER_CTX_set_key_length GRPC_SHADOW_EVP_CIPHER_CTX_set_key_length
#define EVP_CIPHER_CTX_set_padding GRPC_SHADOW_EVP_CIPHER_CTX_set_padding
#define EVP_CIPHER_block_size GRPC_SHADOW_EVP_CIPHER_block_size
#define EVP_CIPHER_flags GRPC_SHADOW_EVP_CIPHER_flags
#define EVP_CIPHER_iv_length GRPC_SHADOW_EVP_CIPHER_iv_length
#define EVP_CIPHER_key_length GRPC_SHADOW_EVP_CIPHER_key_length
#define EVP_CIPHER_mode GRPC_SHADOW_EVP_CIPHER_mode
#define EVP_CIPHER_nid GRPC_SHADOW_EVP_CIPHER_nid
#define EVP_Cipher GRPC_SHADOW_EVP_Cipher
#define EVP_CipherFinal_ex GRPC_SHADOW_EVP_CipherFinal_ex
#define EVP_CipherInit GRPC_SHADOW_EVP_CipherInit
#define EVP_CipherInit_ex GRPC_SHADOW_EVP_CipherInit_ex
#define EVP_CipherUpdate GRPC_SHADOW_EVP_CipherUpdate
#define EVP_DecodeBase64 GRPC_SHADOW_EVP_DecodeBase64
#define EVP_DecodeBlock GRPC_SHADOW_EVP_DecodeBlock
#define EVP_DecodeFinal GRPC_SHADOW_EVP_DecodeFinal
#define EVP_DecodeInit GRPC_SHADOW_EVP_DecodeInit
#define EVP_DecodeUpdate GRPC_SHADOW_EVP_DecodeUpdate
#define EVP_DecodedLength GRPC_SHADOW_EVP_DecodedLength
#define EVP_DecryptFinal_ex GRPC_SHADOW_EVP_DecryptFinal_ex
#define EVP_DecryptInit GRPC_SHADOW_EVP_DecryptInit
#define EVP_DecryptInit_ex GRPC_SHADOW_EVP_DecryptInit_ex
#define EVP_DecryptUpdate GRPC_SHADOW_EVP_DecryptUpdate
#define EVP_Digest GRPC_SHADOW_EVP_Digest
#define EVP_DigestFinal GRPC_SHADOW_EVP_DigestFinal
#define EVP_DigestFinalXOF GRPC_SHADOW_EVP_DigestFinalXOF
#define EVP_DigestFinal_ex GRPC_SHADOW_EVP_DigestFinal_ex
#define EVP_DigestInit GRPC_SHADOW_EVP_DigestInit
#define EVP_DigestInit_ex GRPC_SHADOW_EVP_DigestInit_ex
#define EVP_DigestSign GRPC_SHADOW_EVP_DigestSign
#define EVP_DigestSignFinal GRPC_SHADOW_EVP_DigestSignFinal
#define EVP_DigestSignInit GRPC_SHADOW_EVP_DigestSignInit
#define EVP_DigestSignUpdate GRPC_SHADOW_EVP_DigestSignUpdate
#define EVP_DigestUpdate GRPC_SHADOW_EVP_DigestUpdate
#define EVP_DigestVerify GRPC_SHADOW_EVP_DigestVerify
#define EVP_DigestVerifyFinal GRPC_SHADOW_EVP_DigestVerifyFinal
#define EVP_DigestVerifyInit GRPC_SHADOW_EVP_DigestVerifyInit
#define EVP_DigestVerifyUpdate GRPC_SHADOW_EVP_DigestVerifyUpdate
#define EVP_EncodeBlock GRPC_SHADOW_EVP_EncodeBlock
#define EVP_EncodeFinal GRPC_SHADOW_EVP_EncodeFinal
#define EVP_EncodeInit GRPC_SHADOW_EVP_EncodeInit
#define EVP_EncodeUpdate GRPC_SHADOW_EVP_EncodeUpdate
#define EVP_EncodedLength GRPC_SHADOW_EVP_EncodedLength
#define EVP_EncryptFinal_ex GRPC_SHADOW_EVP_EncryptFinal_ex
#define EVP_EncryptInit GRPC_SHADOW_EVP_EncryptInit
#define EVP_EncryptInit_ex GRPC_SHADOW_EVP_EncryptInit_ex
#define EVP_EncryptUpdate GRPC_SHADOW_EVP_EncryptUpdate
#define EVP_MD_CTX_block_size GRPC_SHADOW_EVP_MD_CTX_block_size
#define EVP_MD_CTX_cleanup GRPC_SHADOW_EVP_MD_CTX_cleanup
#define EVP_MD_CTX_copy GRPC_SHADOW_EVP_MD_CTX_copy
#define EVP_MD_CTX_copy_ex GRPC_SHADOW_EVP_MD_CTX_copy_ex
#define EVP_MD_CTX_create GRPC_SHADOW_EVP_MD_CTX_create
#define EVP_MD_CTX_destroy GRPC_SHADOW_EVP_MD_CTX_destroy
#define EVP_MD_CTX_free GRPC_SHADOW_EVP_MD_CTX_free
#define EVP_MD_CTX_init GRPC_SHADOW_EVP_MD_CTX_init
#define EVP_MD_CTX_md GRPC_SHADOW_EVP_MD_CTX_md
#define EVP_MD_CTX_new GRPC_SHADOW_EVP_MD_CTX_new
#define EVP_MD_CTX_reset GRPC_SHADOW_EVP_MD_CTX_reset
#define EVP_MD_CTX_size GRPC_SHADOW_EVP_MD_CTX_size
#define EVP_MD_CTX_type GRPC_SHADOW_EVP_MD_CTX_type
#define EVP_MD_block_size GRPC_SHADOW_EVP_MD_block_size
#define EVP_MD_flags GRPC_SHADOW_EVP_MD_flags
#define EVP_MD_meth_get_flags GRPC_SHADOW_EVP_MD_meth_get_flags
#define EVP_MD_size GRPC_SHADOW_EVP_MD_size
#define EVP_MD_type GRPC_SHADOW_EVP_MD_type
#define EVP_PBE_scrypt GRPC_SHADOW_EVP_PBE_scrypt
#define EVP_PKCS82PKEY GRPC_SHADOW_EVP_PKCS82PKEY
#define EVP_PKEY2PKCS8 GRPC_SHADOW_EVP_PKEY2PKCS8
#define EVP_PKEY_CTX_ctrl GRPC_SHADOW_EVP_PKEY_CTX_ctrl
#define EVP_PKEY_CTX_dup GRPC_SHADOW_EVP_PKEY_CTX_dup
#define EVP_PKEY_CTX_free GRPC_SHADOW_EVP_PKEY_CTX_free
#define EVP_PKEY_CTX_get0_pkey GRPC_SHADOW_EVP_PKEY_CTX_get0_pkey
#define EVP_PKEY_CTX_get0_rsa_oaep_label GRPC_SHADOW_EVP_PKEY_CTX_get0_rsa_oaep_label
#define EVP_PKEY_CTX_get_rsa_mgf1_md GRPC_SHADOW_EVP_PKEY_CTX_get_rsa_mgf1_md
#define EVP_PKEY_CTX_get_rsa_oaep_md GRPC_SHADOW_EVP_PKEY_CTX_get_rsa_oaep_md
#define EVP_PKEY_CTX_get_rsa_padding GRPC_SHADOW_EVP_PKEY_CTX_get_rsa_padding
#define EVP_PKEY_CTX_get_rsa_pss_saltlen GRPC_SHADOW_EVP_PKEY_CTX_get_rsa_pss_saltlen
#define EVP_PKEY_CTX_get_signature_md GRPC_SHADOW_EVP_PKEY_CTX_get_signature_md
#define EVP_PKEY_CTX_new GRPC_SHADOW_EVP_PKEY_CTX_new
#define EVP_PKEY_CTX_new_id GRPC_SHADOW_EVP_PKEY_CTX_new_id
#define EVP_PKEY_CTX_set0_rsa_oaep_label GRPC_SHADOW_EVP_PKEY_CTX_set0_rsa_oaep_label
#define EVP_PKEY_CTX_set_ec_param_enc GRPC_SHADOW_EVP_PKEY_CTX_set_ec_param_enc
#define EVP_PKEY_CTX_set_ec_paramgen_curve_nid GRPC_SHADOW_EVP_PKEY_CTX_set_ec_paramgen_curve_nid
#define EVP_PKEY_CTX_set_rsa_keygen_bits GRPC_SHADOW_EVP_PKEY_CTX_set_rsa_keygen_bits
#define EVP_PKEY_CTX_set_rsa_keygen_pubexp GRPC_SHADOW_EVP_PKEY_CTX_set_rsa_keygen_pubexp
#define EVP_PKEY_CTX_set_rsa_mgf1_md GRPC_SHADOW_EVP_PKEY_CTX_set_rsa_mgf1_md
#define EVP_PKEY_CTX_set_rsa_oaep_md GRPC_SHADOW_EVP_PKEY_CTX_set_rsa_oaep_md
#define EVP_PKEY_CTX_set_rsa_padding GRPC_SHADOW_EVP_PKEY_CTX_set_rsa_padding
#define EVP_PKEY_CTX_set_rsa_pss_saltlen GRPC_SHADOW_EVP_PKEY_CTX_set_rsa_pss_saltlen
#define EVP_PKEY_CTX_set_signature_md GRPC_SHADOW_EVP_PKEY_CTX_set_signature_md
#define EVP_PKEY_assign GRPC_SHADOW_EVP_PKEY_assign
#define EVP_PKEY_assign_DSA GRPC_SHADOW_EVP_PKEY_assign_DSA
#define EVP_PKEY_assign_EC_KEY GRPC_SHADOW_EVP_PKEY_assign_EC_KEY
#define EVP_PKEY_assign_RSA GRPC_SHADOW_EVP_PKEY_assign_RSA
#define EVP_PKEY_base_id GRPC_SHADOW_EVP_PKEY_base_id
#define EVP_PKEY_bits GRPC_SHADOW_EVP_PKEY_bits
#define EVP_PKEY_cmp GRPC_SHADOW_EVP_PKEY_cmp
#define EVP_PKEY_cmp_parameters GRPC_SHADOW_EVP_PKEY_cmp_parameters
#define EVP_PKEY_copy_parameters GRPC_SHADOW_EVP_PKEY_copy_parameters
#define EVP_PKEY_decrypt GRPC_SHADOW_EVP_PKEY_decrypt
#define EVP_PKEY_decrypt_init GRPC_SHADOW_EVP_PKEY_decrypt_init
#define EVP_PKEY_derive GRPC_SHADOW_EVP_PKEY_derive
#define EVP_PKEY_derive_init GRPC_SHADOW_EVP_PKEY_derive_init
#define EVP_PKEY_derive_set_peer GRPC_SHADOW_EVP_PKEY_derive_set_peer
#define EVP_PKEY_encrypt GRPC_SHADOW_EVP_PKEY_encrypt
#define EVP_PKEY_encrypt_init GRPC_SHADOW_EVP_PKEY_encrypt_init
#define EVP_PKEY_free GRPC_SHADOW_EVP_PKEY_free
#define EVP_PKEY_get0_DH GRPC_SHADOW_EVP_PKEY_get0_DH
#define EVP_PKEY_get0_DSA GRPC_SHADOW_EVP_PKEY_get0_DSA
#define EVP_PKEY_get0_EC_KEY GRPC_SHADOW_EVP_PKEY_get0_EC_KEY
#define EVP_PKEY_get0_RSA GRPC_SHADOW_EVP_PKEY_get0_RSA
#define EVP_PKEY_get1_DH GRPC_SHADOW_EVP_PKEY_get1_DH
#define EVP_PKEY_get1_DSA GRPC_SHADOW_EVP_PKEY_get1_DSA
#define EVP_PKEY_get1_EC_KEY GRPC_SHADOW_EVP_PKEY_get1_EC_KEY
#define EVP_PKEY_get1_RSA GRPC_SHADOW_EVP_PKEY_get1_RSA
#define EVP_PKEY_get1_tls_encodedpoint GRPC_SHADOW_EVP_PKEY_get1_tls_encodedpoint
#define EVP_PKEY_get_raw_private_key GRPC_SHADOW_EVP_PKEY_get_raw_private_key
#define EVP_PKEY_get_raw_public_key GRPC_SHADOW_EVP_PKEY_get_raw_public_key
#define EVP_PKEY_id GRPC_SHADOW_EVP_PKEY_id
#define EVP_PKEY_is_opaque GRPC_SHADOW_EVP_PKEY_is_opaque
#define EVP_PKEY_keygen GRPC_SHADOW_EVP_PKEY_keygen
#define EVP_PKEY_keygen_init GRPC_SHADOW_EVP_PKEY_keygen_init
#define EVP_PKEY_missing_parameters GRPC_SHADOW_EVP_PKEY_missing_parameters
#define EVP_PKEY_new GRPC_SHADOW_EVP_PKEY_new
#define EVP_PKEY_new_raw_private_key GRPC_SHADOW_EVP_PKEY_new_raw_private_key
#define EVP_PKEY_new_raw_public_key GRPC_SHADOW_EVP_PKEY_new_raw_public_key
#define EVP_PKEY_paramgen GRPC_SHADOW_EVP_PKEY_paramgen
#define EVP_PKEY_paramgen_init GRPC_SHADOW_EVP_PKEY_paramgen_init
#define EVP_PKEY_print_params GRPC_SHADOW_EVP_PKEY_print_params
#define EVP_PKEY_print_private GRPC_SHADOW_EVP_PKEY_print_private
#define EVP_PKEY_print_public GRPC_SHADOW_EVP_PKEY_print_public
#define EVP_PKEY_set1_DSA GRPC_SHADOW_EVP_PKEY_set1_DSA
#define EVP_PKEY_set1_EC_KEY GRPC_SHADOW_EVP_PKEY_set1_EC_KEY
#define EVP_PKEY_set1_RSA GRPC_SHADOW_EVP_PKEY_set1_RSA
#define EVP_PKEY_set1_tls_encodedpoint GRPC_SHADOW_EVP_PKEY_set1_tls_encodedpoint
#define EVP_PKEY_set_type GRPC_SHADOW_EVP_PKEY_set_type
#define EVP_PKEY_sign GRPC_SHADOW_EVP_PKEY_sign
#define EVP_PKEY_sign_init GRPC_SHADOW_EVP_PKEY_sign_init
#define EVP_PKEY_size GRPC_SHADOW_EVP_PKEY_size
#define EVP_PKEY_type GRPC_SHADOW_EVP_PKEY_type
#define EVP_PKEY_up_ref GRPC_SHADOW_EVP_PKEY_up_ref
#define EVP_PKEY_verify GRPC_SHADOW_EVP_PKEY_verify
#define EVP_PKEY_verify_init GRPC_SHADOW_EVP_PKEY_verify_init
#define EVP_PKEY_verify_recover GRPC_SHADOW_EVP_PKEY_verify_recover
#define EVP_PKEY_verify_recover_init GRPC_SHADOW_EVP_PKEY_verify_recover_init
#define EVP_SignFinal GRPC_SHADOW_EVP_SignFinal
#define EVP_SignInit GRPC_SHADOW_EVP_SignInit
#define EVP_SignInit_ex GRPC_SHADOW_EVP_SignInit_ex
#define EVP_SignUpdate GRPC_SHADOW_EVP_SignUpdate
#define EVP_VerifyFinal GRPC_SHADOW_EVP_VerifyFinal
#define EVP_VerifyInit GRPC_SHADOW_EVP_VerifyInit
#define EVP_VerifyInit_ex GRPC_SHADOW_EVP_VerifyInit_ex
#define EVP_VerifyUpdate GRPC_SHADOW_EVP_VerifyUpdate
#define EVP_add_cipher_alias GRPC_SHADOW_EVP_add_cipher_alias
#define EVP_add_digest GRPC_SHADOW_EVP_add_digest
#define EVP_aead_aes_128_cbc_sha1_tls GRPC_SHADOW_EVP_aead_aes_128_cbc_sha1_tls
#define EVP_aead_aes_128_cbc_sha1_tls_implicit_iv GRPC_SHADOW_EVP_aead_aes_128_cbc_sha1_tls_implicit_iv
#define EVP_aead_aes_128_cbc_sha256_tls GRPC_SHADOW_EVP_aead_aes_128_cbc_sha256_tls
#define EVP_aead_aes_128_ccm_bluetooth GRPC_SHADOW_EVP_aead_aes_128_ccm_bluetooth
#define EVP_aead_aes_128_ccm_bluetooth_8 GRPC_SHADOW_EVP_aead_aes_128_ccm_bluetooth_8
#define EVP_aead_aes_128_ctr_hmac_sha256 GRPC_SHADOW_EVP_aead_aes_128_ctr_hmac_sha256
#define EVP_aead_aes_128_gcm GRPC_SHADOW_EVP_aead_aes_128_gcm
#define EVP_aead_aes_128_gcm_siv GRPC_SHADOW_EVP_aead_aes_128_gcm_siv
#define EVP_aead_aes_128_gcm_tls12 GRPC_SHADOW_EVP_aead_aes_128_gcm_tls12
#define EVP_aead_aes_128_gcm_tls13 GRPC_SHADOW_EVP_aead_aes_128_gcm_tls13
#define EVP_aead_aes_192_gcm GRPC_SHADOW_EVP_aead_aes_192_gcm
#define EVP_aead_aes_256_cbc_sha1_tls GRPC_SHADOW_EVP_aead_aes_256_cbc_sha1_tls
#define EVP_aead_aes_256_cbc_sha1_tls_implicit_iv GRPC_SHADOW_EVP_aead_aes_256_cbc_sha1_tls_implicit_iv
#define EVP_aead_aes_256_cbc_sha256_tls GRPC_SHADOW_EVP_aead_aes_256_cbc_sha256_tls
#define EVP_aead_aes_256_cbc_sha384_tls GRPC_SHADOW_EVP_aead_aes_256_cbc_sha384_tls
#define EVP_aead_aes_256_ctr_hmac_sha256 GRPC_SHADOW_EVP_aead_aes_256_ctr_hmac_sha256
#define EVP_aead_aes_256_gcm GRPC_SHADOW_EVP_aead_aes_256_gcm
#define EVP_aead_aes_256_gcm_siv GRPC_SHADOW_EVP_aead_aes_256_gcm_siv
#define EVP_aead_aes_256_gcm_tls12 GRPC_SHADOW_EVP_aead_aes_256_gcm_tls12
#define EVP_aead_aes_256_gcm_tls13 GRPC_SHADOW_EVP_aead_aes_256_gcm_tls13
#define EVP_aead_chacha20_poly1305 GRPC_SHADOW_EVP_aead_chacha20_poly1305
#define EVP_aead_des_ede3_cbc_sha1_tls GRPC_SHADOW_EVP_aead_des_ede3_cbc_sha1_tls
#define EVP_aead_des_ede3_cbc_sha1_tls_implicit_iv GRPC_SHADOW_EVP_aead_des_ede3_cbc_sha1_tls_implicit_iv
#define EVP_aead_null_sha1_tls GRPC_SHADOW_EVP_aead_null_sha1_tls
#define EVP_aead_xchacha20_poly1305 GRPC_SHADOW_EVP_aead_xchacha20_poly1305
#define EVP_aes_128_cbc GRPC_SHADOW_EVP_aes_128_cbc
#define EVP_aes_128_ctr GRPC_SHADOW_EVP_aes_128_ctr
#define EVP_aes_128_ecb GRPC_SHADOW_EVP_aes_128_ecb
#define EVP_aes_128_gcm GRPC_SHADOW_EVP_aes_128_gcm
#define EVP_aes_128_ofb GRPC_SHADOW_EVP_aes_128_ofb
#define EVP_aes_192_cbc GRPC_SHADOW_EVP_aes_192_cbc
#define EVP_aes_192_ctr GRPC_SHADOW_EVP_aes_192_ctr
#define EVP_aes_192_ecb GRPC_SHADOW_EVP_aes_192_ecb
#define EVP_aes_192_gcm GRPC_SHADOW_EVP_aes_192_gcm
#define EVP_aes_192_ofb GRPC_SHADOW_EVP_aes_192_ofb
#define EVP_aes_256_cbc GRPC_SHADOW_EVP_aes_256_cbc
#define EVP_aes_256_ctr GRPC_SHADOW_EVP_aes_256_ctr
#define EVP_aes_256_ecb GRPC_SHADOW_EVP_aes_256_ecb
#define EVP_aes_256_gcm GRPC_SHADOW_EVP_aes_256_gcm
#define EVP_aes_256_ofb GRPC_SHADOW_EVP_aes_256_ofb
#define EVP_cleanup GRPC_SHADOW_EVP_cleanup
#define EVP_des_cbc GRPC_SHADOW_EVP_des_cbc
#define EVP_des_ecb GRPC_SHADOW_EVP_des_ecb
#define EVP_des_ede GRPC_SHADOW_EVP_des_ede
#define EVP_des_ede3 GRPC_SHADOW_EVP_des_ede3
#define EVP_des_ede3_cbc GRPC_SHADOW_EVP_des_ede3_cbc
#define EVP_des_ede3_ecb GRPC_SHADOW_EVP_des_ede3_ecb
#define EVP_des_ede_cbc GRPC_SHADOW_EVP_des_ede_cbc
#define EVP_enc_null GRPC_SHADOW_EVP_enc_null
#define EVP_get_cipherbyname GRPC_SHADOW_EVP_get_cipherbyname
#define EVP_get_cipherbynid GRPC_SHADOW_EVP_get_cipherbynid
#define EVP_get_digestbyname GRPC_SHADOW_EVP_get_digestbyname
#define EVP_get_digestbynid GRPC_SHADOW_EVP_get_digestbynid
#define EVP_get_digestbyobj GRPC_SHADOW_EVP_get_digestbyobj
#define EVP_has_aes_hardware GRPC_SHADOW_EVP_has_aes_hardware
#define EVP_marshal_digest_algorithm GRPC_SHADOW_EVP_marshal_digest_algorithm
#define EVP_marshal_private_key GRPC_SHADOW_EVP_marshal_private_key
#define EVP_marshal_public_key GRPC_SHADOW_EVP_marshal_public_key
#define EVP_md4 GRPC_SHADOW_EVP_md4
#define EVP_md5 GRPC_SHADOW_EVP_md5
#define EVP_md5_sha1 GRPC_SHADOW_EVP_md5_sha1
#define EVP_parse_digest_algorithm GRPC_SHADOW_EVP_parse_digest_algorithm
#define EVP_parse_private_key GRPC_SHADOW_EVP_parse_private_key
#define EVP_parse_public_key GRPC_SHADOW_EVP_parse_public_key
#define EVP_rc2_40_cbc GRPC_SHADOW_EVP_rc2_40_cbc
#define EVP_rc2_cbc GRPC_SHADOW_EVP_rc2_cbc
#define EVP_rc4 GRPC_SHADOW_EVP_rc4
#define EVP_sha1 GRPC_SHADOW_EVP_sha1
#define EVP_sha224 GRPC_SHADOW_EVP_sha224
#define EVP_sha256 GRPC_SHADOW_EVP_sha256
#define EVP_sha384 GRPC_SHADOW_EVP_sha384
#define EVP_sha512 GRPC_SHADOW_EVP_sha512
#define EVP_tls_cbc_copy_mac GRPC_SHADOW_EVP_tls_cbc_copy_mac
#define EVP_tls_cbc_digest_record GRPC_SHADOW_EVP_tls_cbc_digest_record
#define EVP_tls_cbc_record_digest_supported GRPC_SHADOW_EVP_tls_cbc_record_digest_supported
#define EVP_tls_cbc_remove_padding GRPC_SHADOW_EVP_tls_cbc_remove_padding
#define EXTENDED_KEY_USAGE_free GRPC_SHADOW_EXTENDED_KEY_USAGE_free
#define EXTENDED_KEY_USAGE_it GRPC_SHADOW_EXTENDED_KEY_USAGE_it
#define EXTENDED_KEY_USAGE_new GRPC_SHADOW_EXTENDED_KEY_USAGE_new
#define FIPS_mode GRPC_SHADOW_FIPS_mode
#define FIPS_mode_set GRPC_SHADOW_FIPS_mode_set
#define GENERAL_NAMES_free GRPC_SHADOW_GENERAL_NAMES_free
#define GENERAL_NAMES_it GRPC_SHADOW_GENERAL_NAMES_it
#define GENERAL_NAMES_new GRPC_SHADOW_GENERAL_NAMES_new
#define GENERAL_NAME_cmp GRPC_SHADOW_GENERAL_NAME_cmp
#define GENERAL_NAME_dup GRPC_SHADOW_GENERAL_NAME_dup
#define GENERAL_NAME_free GRPC_SHADOW_GENERAL_NAME_free
#define GENERAL_NAME_get0_otherName GRPC_SHADOW_GENERAL_NAME_get0_otherName
#define GENERAL_NAME_get0_value GRPC_SHADOW_GENERAL_NAME_get0_value
#define GENERAL_NAME_it GRPC_SHADOW_GENERAL_NAME_it
#define GENERAL_NAME_new GRPC_SHADOW_GENERAL_NAME_new
#define GENERAL_NAME_print GRPC_SHADOW_GENERAL_NAME_print
#define GENERAL_NAME_set0_othername GRPC_SHADOW_GENERAL_NAME_set0_othername
#define GENERAL_NAME_set0_value GRPC_SHADOW_GENERAL_NAME_set0_value
#define GENERAL_SUBTREE_free GRPC_SHADOW_GENERAL_SUBTREE_free
#define GENERAL_SUBTREE_it GRPC_SHADOW_GENERAL_SUBTREE_it
#define GENERAL_SUBTREE_new GRPC_SHADOW_GENERAL_SUBTREE_new
#define HKDF GRPC_SHADOW_HKDF
#define HKDF_expand GRPC_SHADOW_HKDF_expand
#define HKDF_extract GRPC_SHADOW_HKDF_extract
#define HMAC GRPC_SHADOW_HMAC
#define HMAC_CTX_cleanup GRPC_SHADOW_HMAC_CTX_cleanup
#define HMAC_CTX_copy GRPC_SHADOW_HMAC_CTX_copy
#define HMAC_CTX_copy_ex GRPC_SHADOW_HMAC_CTX_copy_ex
#define HMAC_CTX_free GRPC_SHADOW_HMAC_CTX_free
#define HMAC_CTX_init GRPC_SHADOW_HMAC_CTX_init
#define HMAC_CTX_new GRPC_SHADOW_HMAC_CTX_new
#define HMAC_CTX_reset GRPC_SHADOW_HMAC_CTX_reset
#define HMAC_Final GRPC_SHADOW_HMAC_Final
#define HMAC_Init GRPC_SHADOW_HMAC_Init
#define HMAC_Init_ex GRPC_SHADOW_HMAC_Init_ex
#define HMAC_Update GRPC_SHADOW_HMAC_Update
#define HMAC_size GRPC_SHADOW_HMAC_size
#define HRSS_decap GRPC_SHADOW_HRSS_decap
#define HRSS_encap GRPC_SHADOW_HRSS_encap
#define HRSS_generate_key GRPC_SHADOW_HRSS_generate_key
#define HRSS_marshal_public_key GRPC_SHADOW_HRSS_marshal_public_key
#define HRSS_parse_public_key GRPC_SHADOW_HRSS_parse_public_key
#define HRSS_poly2_rotr_consttime GRPC_SHADOW_HRSS_poly2_rotr_consttime
#define HRSS_poly3_invert GRPC_SHADOW_HRSS_poly3_invert
#define HRSS_poly3_mul GRPC_SHADOW_HRSS_poly3_mul
#define ISSUING_DIST_POINT_free GRPC_SHADOW_ISSUING_DIST_POINT_free
#define ISSUING_DIST_POINT_it GRPC_SHADOW_ISSUING_DIST_POINT_it
#define ISSUING_DIST_POINT_new GRPC_SHADOW_ISSUING_DIST_POINT_new
#define MD4 GRPC_SHADOW_MD4
#define MD4_Final GRPC_SHADOW_MD4_Final
#define MD4_Init GRPC_SHADOW_MD4_Init
#define MD4_Transform GRPC_SHADOW_MD4_Transform
#define MD4_Update GRPC_SHADOW_MD4_Update
#define MD5 GRPC_SHADOW_MD5
#define MD5_Final GRPC_SHADOW_MD5_Final
#define MD5_Init GRPC_SHADOW_MD5_Init
#define MD5_Transform GRPC_SHADOW_MD5_Transform
#define MD5_Update GRPC_SHADOW_MD5_Update
#define METHOD_ref GRPC_SHADOW_METHOD_ref
#define METHOD_unref GRPC_SHADOW_METHOD_unref
#define NAME_CONSTRAINTS_check GRPC_SHADOW_NAME_CONSTRAINTS_check
#define NAME_CONSTRAINTS_free GRPC_SHADOW_NAME_CONSTRAINTS_free
#define NAME_CONSTRAINTS_it GRPC_SHADOW_NAME_CONSTRAINTS_it
#define NAME_CONSTRAINTS_new GRPC_SHADOW_NAME_CONSTRAINTS_new
#define NCONF_free GRPC_SHADOW_NCONF_free
#define NCONF_get_section GRPC_SHADOW_NCONF_get_section
#define NCONF_get_string GRPC_SHADOW_NCONF_get_string
#define NCONF_load GRPC_SHADOW_NCONF_load
#define NCONF_load_bio GRPC_SHADOW_NCONF_load_bio
#define NCONF_new GRPC_SHADOW_NCONF_new
#define NETSCAPE_SPKAC_free GRPC_SHADOW_NETSCAPE_SPKAC_free
#define NETSCAPE_SPKAC_it GRPC_SHADOW_NETSCAPE_SPKAC_it
#define NETSCAPE_SPKAC_new GRPC_SHADOW_NETSCAPE_SPKAC_new
#define NETSCAPE_SPKI_b64_decode GRPC_SHADOW_NETSCAPE_SPKI_b64_decode
#define NETSCAPE_SPKI_b64_encode GRPC_SHADOW_NETSCAPE_SPKI_b64_encode
#define NETSCAPE_SPKI_free GRPC_SHADOW_NETSCAPE_SPKI_free
#define NETSCAPE_SPKI_get_pubkey GRPC_SHADOW_NETSCAPE_SPKI_get_pubkey
#define NETSCAPE_SPKI_it GRPC_SHADOW_NETSCAPE_SPKI_it
#define NETSCAPE_SPKI_new GRPC_SHADOW_NETSCAPE_SPKI_new
#define NETSCAPE_SPKI_set_pubkey GRPC_SHADOW_NETSCAPE_SPKI_set_pubkey
#define NETSCAPE_SPKI_sign GRPC_SHADOW_NETSCAPE_SPKI_sign
#define NETSCAPE_SPKI_verify GRPC_SHADOW_NETSCAPE_SPKI_verify
#define NOTICEREF_free GRPC_SHADOW_NOTICEREF_free
#define NOTICEREF_it GRPC_SHADOW_NOTICEREF_it
#define NOTICEREF_new GRPC_SHADOW_NOTICEREF_new
#define OBJ_cbs2nid GRPC_SHADOW_OBJ_cbs2nid
#define OBJ_cleanup GRPC_SHADOW_OBJ_cleanup
#define OBJ_cmp GRPC_SHADOW_OBJ_cmp
#define OBJ_create GRPC_SHADOW_OBJ_create
#define OBJ_dup GRPC_SHADOW_OBJ_dup
#define OBJ_find_sigid_algs GRPC_SHADOW_OBJ_find_sigid_algs
#define OBJ_find_sigid_by_algs GRPC_SHADOW_OBJ_find_sigid_by_algs
#define OBJ_get0_data GRPC_SHADOW_OBJ_get0_data
#define OBJ_length GRPC_SHADOW_OBJ_length
#define OBJ_ln2nid GRPC_SHADOW_OBJ_ln2nid
#define OBJ_nid2cbb GRPC_SHADOW_OBJ_nid2cbb
#define OBJ_nid2ln GRPC_SHADOW_OBJ_nid2ln
#define OBJ_nid2obj GRPC_SHADOW_OBJ_nid2obj
#define OBJ_nid2sn GRPC_SHADOW_OBJ_nid2sn
#define OBJ_obj2nid GRPC_SHADOW_OBJ_obj2nid
#define OBJ_obj2txt GRPC_SHADOW_OBJ_obj2txt
#define OBJ_sn2nid GRPC_SHADOW_OBJ_sn2nid
#define OBJ_txt2nid GRPC_SHADOW_OBJ_txt2nid
#define OBJ_txt2obj GRPC_SHADOW_OBJ_txt2obj
#define OPENSSL_add_all_algorithms_conf GRPC_SHADOW_OPENSSL_add_all_algorithms_conf
#define OPENSSL_built_in_curves GRPC_SHADOW_OPENSSL_built_in_curves
#define OPENSSL_cleanse GRPC_SHADOW_OPENSSL_cleanse
#define OPENSSL_cleanup GRPC_SHADOW_OPENSSL_cleanup
#define OPENSSL_clear_free GRPC_SHADOW_OPENSSL_clear_free
#define OPENSSL_config GRPC_SHADOW_OPENSSL_config
#define OPENSSL_cpuid_setup GRPC_SHADOW_OPENSSL_cpuid_setup
#define OPENSSL_free GRPC_SHADOW_OPENSSL_free
#define OPENSSL_gmtime GRPC_SHADOW_OPENSSL_gmtime
#define OPENSSL_gmtime_adj GRPC_SHADOW_OPENSSL_gmtime_adj
#define OPENSSL_gmtime_diff GRPC_SHADOW_OPENSSL_gmtime_diff
#define OPENSSL_hash32 GRPC_SHADOW_OPENSSL_hash32
#define OPENSSL_ia32cap_P GRPC_SHADOW_OPENSSL_ia32cap_P
#define OPENSSL_init_crypto GRPC_SHADOW_OPENSSL_init_crypto
#define OPENSSL_init_ssl GRPC_SHADOW_OPENSSL_init_ssl
#define OPENSSL_load_builtin_modules GRPC_SHADOW_OPENSSL_load_builtin_modules
#define OPENSSL_malloc GRPC_SHADOW_OPENSSL_malloc
#define OPENSSL_malloc_init GRPC_SHADOW_OPENSSL_malloc_init
#define OPENSSL_no_config GRPC_SHADOW_OPENSSL_no_config
#define OPENSSL_realloc GRPC_SHADOW_OPENSSL_realloc
#define OPENSSL_strcasecmp GRPC_SHADOW_OPENSSL_strcasecmp
#define OPENSSL_strdup GRPC_SHADOW_OPENSSL_strdup
#define OPENSSL_strncasecmp GRPC_SHADOW_OPENSSL_strncasecmp
#define OPENSSL_strnlen GRPC_SHADOW_OPENSSL_strnlen
#define OPENSSL_tolower GRPC_SHADOW_OPENSSL_tolower
#define OTHERNAME_cmp GRPC_SHADOW_OTHERNAME_cmp
#define OTHERNAME_free GRPC_SHADOW_OTHERNAME_free
#define OTHERNAME_it GRPC_SHADOW_OTHERNAME_it
#define OTHERNAME_new GRPC_SHADOW_OTHERNAME_new
#define OpenSSL_add_all_algorithms GRPC_SHADOW_OpenSSL_add_all_algorithms
#define OpenSSL_add_all_ciphers GRPC_SHADOW_OpenSSL_add_all_ciphers
#define OpenSSL_add_all_digests GRPC_SHADOW_OpenSSL_add_all_digests
#define OpenSSL_version GRPC_SHADOW_OpenSSL_version
#define OpenSSL_version_num GRPC_SHADOW_OpenSSL_version_num
#define PEM_ASN1_read GRPC_SHADOW_PEM_ASN1_read
#define PEM_ASN1_read_bio GRPC_SHADOW_PEM_ASN1_read_bio
#define PEM_ASN1_write GRPC_SHADOW_PEM_ASN1_write
#define PEM_ASN1_write_bio GRPC_SHADOW_PEM_ASN1_write_bio
#define PEM_X509_INFO_read GRPC_SHADOW_PEM_X509_INFO_read
#define PEM_X509_INFO_read_bio GRPC_SHADOW_PEM_X509_INFO_read_bio
#define PEM_X509_INFO_write_bio GRPC_SHADOW_PEM_X509_INFO_write_bio
#define PEM_bytes_read_bio GRPC_SHADOW_PEM_bytes_read_bio
#define PEM_def_callback GRPC_SHADOW_PEM_def_callback
#define PEM_dek_info GRPC_SHADOW_PEM_dek_info
#define PEM_do_header GRPC_SHADOW_PEM_do_header
#define PEM_get_EVP_CIPHER_INFO GRPC_SHADOW_PEM_get_EVP_CIPHER_INFO
#define PEM_proc_type GRPC_SHADOW_PEM_proc_type
#define PEM_read GRPC_SHADOW_PEM_read
#define PEM_read_DHparams GRPC_SHADOW_PEM_read_DHparams
#define PEM_read_DSAPrivateKey GRPC_SHADOW_PEM_read_DSAPrivateKey
#define PEM_read_DSA_PUBKEY GRPC_SHADOW_PEM_read_DSA_PUBKEY
#define PEM_read_DSAparams GRPC_SHADOW_PEM_read_DSAparams
#define PEM_read_ECPrivateKey GRPC_SHADOW_PEM_read_ECPrivateKey
#define PEM_read_EC_PUBKEY GRPC_SHADOW_PEM_read_EC_PUBKEY
#define PEM_read_PKCS7 GRPC_SHADOW_PEM_read_PKCS7
#define PEM_read_PKCS8 GRPC_SHADOW_PEM_read_PKCS8
#define PEM_read_PKCS8_PRIV_KEY_INFO GRPC_SHADOW_PEM_read_PKCS8_PRIV_KEY_INFO
#define PEM_read_PUBKEY GRPC_SHADOW_PEM_read_PUBKEY
#define PEM_read_PrivateKey GRPC_SHADOW_PEM_read_PrivateKey
#define PEM_read_RSAPrivateKey GRPC_SHADOW_PEM_read_RSAPrivateKey
#define PEM_read_RSAPublicKey GRPC_SHADOW_PEM_read_RSAPublicKey
#define PEM_read_RSA_PUBKEY GRPC_SHADOW_PEM_read_RSA_PUBKEY
#define PEM_read_SSL_SESSION GRPC_SHADOW_PEM_read_SSL_SESSION
#define PEM_read_X509 GRPC_SHADOW_PEM_read_X509
#define PEM_read_X509_AUX GRPC_SHADOW_PEM_read_X509_AUX
#define PEM_read_X509_CRL GRPC_SHADOW_PEM_read_X509_CRL
#define PEM_read_X509_REQ GRPC_SHADOW_PEM_read_X509_REQ
#define PEM_read_bio GRPC_SHADOW_PEM_read_bio
#define PEM_read_bio_DHparams GRPC_SHADOW_PEM_read_bio_DHparams
#define PEM_read_bio_DSAPrivateKey GRPC_SHADOW_PEM_read_bio_DSAPrivateKey
#define PEM_read_bio_DSA_PUBKEY GRPC_SHADOW_PEM_read_bio_DSA_PUBKEY
#define PEM_read_bio_DSAparams GRPC_SHADOW_PEM_read_bio_DSAparams
#define PEM_read_bio_ECPrivateKey GRPC_SHADOW_PEM_read_bio_ECPrivateKey
#define PEM_read_bio_EC_PUBKEY GRPC_SHADOW_PEM_read_bio_EC_PUBKEY
#define PEM_read_bio_PKCS7 GRPC_SHADOW_PEM_read_bio_PKCS7
#define PEM_read_bio_PKCS8 GRPC_SHADOW_PEM_read_bio_PKCS8
#define PEM_read_bio_PKCS8_PRIV_KEY_INFO GRPC_SHADOW_PEM_read_bio_PKCS8_PRIV_KEY_INFO
#define PEM_read_bio_PUBKEY GRPC_SHADOW_PEM_read_bio_PUBKEY
#define PEM_read_bio_PrivateKey GRPC_SHADOW_PEM_read_bio_PrivateKey
#define PEM_read_bio_RSAPrivateKey GRPC_SHADOW_PEM_read_bio_RSAPrivateKey
#define PEM_read_bio_RSAPublicKey GRPC_SHADOW_PEM_read_bio_RSAPublicKey
#define PEM_read_bio_RSA_PUBKEY GRPC_SHADOW_PEM_read_bio_RSA_PUBKEY
#define PEM_read_bio_SSL_SESSION GRPC_SHADOW_PEM_read_bio_SSL_SESSION
#define PEM_read_bio_X509 GRPC_SHADOW_PEM_read_bio_X509
#define PEM_read_bio_X509_AUX GRPC_SHADOW_PEM_read_bio_X509_AUX
#define PEM_read_bio_X509_CRL GRPC_SHADOW_PEM_read_bio_X509_CRL
#define PEM_read_bio_X509_REQ GRPC_SHADOW_PEM_read_bio_X509_REQ
#define PEM_write GRPC_SHADOW_PEM_write
#define PEM_write_DHparams GRPC_SHADOW_PEM_write_DHparams
#define PEM_write_DSAPrivateKey GRPC_SHADOW_PEM_write_DSAPrivateKey
#define PEM_write_DSA_PUBKEY GRPC_SHADOW_PEM_write_DSA_PUBKEY
#define PEM_write_DSAparams GRPC_SHADOW_PEM_write_DSAparams
#define PEM_write_ECPrivateKey GRPC_SHADOW_PEM_write_ECPrivateKey
#define PEM_write_EC_PUBKEY GRPC_SHADOW_PEM_write_EC_PUBKEY
#define PEM_write_PKCS7 GRPC_SHADOW_PEM_write_PKCS7
#define PEM_write_PKCS8 GRPC_SHADOW_PEM_write_PKCS8
#define PEM_write_PKCS8PrivateKey GRPC_SHADOW_PEM_write_PKCS8PrivateKey
#define PEM_write_PKCS8PrivateKey_nid GRPC_SHADOW_PEM_write_PKCS8PrivateKey_nid
#define PEM_write_PKCS8_PRIV_KEY_INFO GRPC_SHADOW_PEM_write_PKCS8_PRIV_KEY_INFO
#define PEM_write_PUBKEY GRPC_SHADOW_PEM_write_PUBKEY
#define PEM_write_PrivateKey GRPC_SHADOW_PEM_write_PrivateKey
#define PEM_write_RSAPrivateKey GRPC_SHADOW_PEM_write_RSAPrivateKey
#define PEM_write_RSAPublicKey GRPC_SHADOW_PEM_write_RSAPublicKey
#define PEM_write_RSA_PUBKEY GRPC_SHADOW_PEM_write_RSA_PUBKEY
#define PEM_write_SSL_SESSION GRPC_SHADOW_PEM_write_SSL_SESSION
#define PEM_write_X509 GRPC_SHADOW_PEM_write_X509
#define PEM_write_X509_AUX GRPC_SHADOW_PEM_write_X509_AUX
#define PEM_write_X509_CRL GRPC_SHADOW_PEM_write_X509_CRL
#define PEM_write_X509_REQ GRPC_SHADOW_PEM_write_X509_REQ
#define PEM_write_X509_REQ_NEW GRPC_SHADOW_PEM_write_X509_REQ_NEW
#define PEM_write_bio GRPC_SHADOW_PEM_write_bio
#define PEM_write_bio_DHparams GRPC_SHADOW_PEM_write_bio_DHparams
#define PEM_write_bio_DSAPrivateKey GRPC_SHADOW_PEM_write_bio_DSAPrivateKey
#define PEM_write_bio_DSA_PUBKEY GRPC_SHADOW_PEM_write_bio_DSA_PUBKEY
#define PEM_write_bio_DSAparams GRPC_SHADOW_PEM_write_bio_DSAparams
#define PEM_write_bio_ECPrivateKey GRPC_SHADOW_PEM_write_bio_ECPrivateKey
#define PEM_write_bio_EC_PUBKEY GRPC_SHADOW_PEM_write_bio_EC_PUBKEY
#define PEM_write_bio_PKCS7 GRPC_SHADOW_PEM_write_bio_PKCS7
#define PEM_write_bio_PKCS8 GRPC_SHADOW_PEM_write_bio_PKCS8
#define PEM_write_bio_PKCS8PrivateKey GRPC_SHADOW_PEM_write_bio_PKCS8PrivateKey
#define PEM_write_bio_PKCS8PrivateKey_nid GRPC_SHADOW_PEM_write_bio_PKCS8PrivateKey_nid
#define PEM_write_bio_PKCS8_PRIV_KEY_INFO GRPC_SHADOW_PEM_write_bio_PKCS8_PRIV_KEY_INFO
#define PEM_write_bio_PUBKEY GRPC_SHADOW_PEM_write_bio_PUBKEY
#define PEM_write_bio_PrivateKey GRPC_SHADOW_PEM_write_bio_PrivateKey
#define PEM_write_bio_RSAPrivateKey GRPC_SHADOW_PEM_write_bio_RSAPrivateKey
#define PEM_write_bio_RSAPublicKey GRPC_SHADOW_PEM_write_bio_RSAPublicKey
#define PEM_write_bio_RSA_PUBKEY GRPC_SHADOW_PEM_write_bio_RSA_PUBKEY
#define PEM_write_bio_SSL_SESSION GRPC_SHADOW_PEM_write_bio_SSL_SESSION
#define PEM_write_bio_X509 GRPC_SHADOW_PEM_write_bio_X509
#define PEM_write_bio_X509_AUX GRPC_SHADOW_PEM_write_bio_X509_AUX
#define PEM_write_bio_X509_CRL GRPC_SHADOW_PEM_write_bio_X509_CRL
#define PEM_write_bio_X509_REQ GRPC_SHADOW_PEM_write_bio_X509_REQ
#define PEM_write_bio_X509_REQ_NEW GRPC_SHADOW_PEM_write_bio_X509_REQ_NEW
#define PKCS12_PBE_add GRPC_SHADOW_PKCS12_PBE_add
#define PKCS12_create GRPC_SHADOW_PKCS12_create
#define PKCS12_free GRPC_SHADOW_PKCS12_free
#define PKCS12_get_key_and_certs GRPC_SHADOW_PKCS12_get_key_and_certs
#define PKCS12_parse GRPC_SHADOW_PKCS12_parse
#define PKCS12_verify_mac GRPC_SHADOW_PKCS12_verify_mac
#define PKCS5_PBKDF2_HMAC GRPC_SHADOW_PKCS5_PBKDF2_HMAC
#define PKCS5_PBKDF2_HMAC_SHA1 GRPC_SHADOW_PKCS5_PBKDF2_HMAC_SHA1
#define PKCS5_pbe2_decrypt_init GRPC_SHADOW_PKCS5_pbe2_decrypt_init
#define PKCS5_pbe2_encrypt_init GRPC_SHADOW_PKCS5_pbe2_encrypt_init
#define PKCS7_bundle_CRLs GRPC_SHADOW_PKCS7_bundle_CRLs
#define PKCS7_bundle_certificates GRPC_SHADOW_PKCS7_bundle_certificates
#define PKCS7_free GRPC_SHADOW_PKCS7_free
#define PKCS7_get_CRLs GRPC_SHADOW_PKCS7_get_CRLs
#define PKCS7_get_PEM_CRLs GRPC_SHADOW_PKCS7_get_PEM_CRLs
#define PKCS7_get_PEM_certificates GRPC_SHADOW_PKCS7_get_PEM_certificates
#define PKCS7_get_certificates GRPC_SHADOW_PKCS7_get_certificates
#define PKCS7_get_raw_certificates GRPC_SHADOW_PKCS7_get_raw_certificates
#define PKCS7_sign GRPC_SHADOW_PKCS7_sign
#define PKCS7_type_is_data GRPC_SHADOW_PKCS7_type_is_data
#define PKCS7_type_is_digest GRPC_SHADOW_PKCS7_type_is_digest
#define PKCS7_type_is_encrypted GRPC_SHADOW_PKCS7_type_is_encrypted
#define PKCS7_type_is_enveloped GRPC_SHADOW_PKCS7_type_is_enveloped
#define PKCS7_type_is_signed GRPC_SHADOW_PKCS7_type_is_signed
#define PKCS7_type_is_signedAndEnveloped GRPC_SHADOW_PKCS7_type_is_signedAndEnveloped
#define PKCS8_PRIV_KEY_INFO_free GRPC_SHADOW_PKCS8_PRIV_KEY_INFO_free
#define PKCS8_PRIV_KEY_INFO_it GRPC_SHADOW_PKCS8_PRIV_KEY_INFO_it
#define PKCS8_PRIV_KEY_INFO_new GRPC_SHADOW_PKCS8_PRIV_KEY_INFO_new
#define PKCS8_decrypt GRPC_SHADOW_PKCS8_decrypt
#define PKCS8_encrypt GRPC_SHADOW_PKCS8_encrypt
#define PKCS8_marshal_encrypted_private_key GRPC_SHADOW_PKCS8_marshal_encrypted_private_key
#define PKCS8_parse_encrypted_private_key GRPC_SHADOW_PKCS8_parse_encrypted_private_key
#define PKCS8_pkey_get0 GRPC_SHADOW_PKCS8_pkey_get0
#define PKCS8_pkey_set0 GRPC_SHADOW_PKCS8_pkey_set0
#define PKEY_USAGE_PERIOD_free GRPC_SHADOW_PKEY_USAGE_PERIOD_free
#define PKEY_USAGE_PERIOD_it GRPC_SHADOW_PKEY_USAGE_PERIOD_it
#define PKEY_USAGE_PERIOD_new GRPC_SHADOW_PKEY_USAGE_PERIOD_new
#define POLICYINFO_free GRPC_SHADOW_POLICYINFO_free
#define POLICYINFO_it GRPC_SHADOW_POLICYINFO_it
#define POLICYINFO_new GRPC_SHADOW_POLICYINFO_new
#define POLICYQUALINFO_free GRPC_SHADOW_POLICYQUALINFO_free
#define POLICYQUALINFO_it GRPC_SHADOW_POLICYQUALINFO_it
#define POLICYQUALINFO_new GRPC_SHADOW_POLICYQUALINFO_new
#define POLICY_CONSTRAINTS_free GRPC_SHADOW_POLICY_CONSTRAINTS_free
#define POLICY_CONSTRAINTS_it GRPC_SHADOW_POLICY_CONSTRAINTS_it
#define POLICY_CONSTRAINTS_new GRPC_SHADOW_POLICY_CONSTRAINTS_new
#define POLICY_MAPPINGS_it GRPC_SHADOW_POLICY_MAPPINGS_it
#define POLICY_MAPPING_free GRPC_SHADOW_POLICY_MAPPING_free
#define POLICY_MAPPING_it GRPC_SHADOW_POLICY_MAPPING_it
#define POLICY_MAPPING_new GRPC_SHADOW_POLICY_MAPPING_new
#define PROXY_CERT_INFO_EXTENSION_free GRPC_SHADOW_PROXY_CERT_INFO_EXTENSION_free
#define PROXY_CERT_INFO_EXTENSION_it GRPC_SHADOW_PROXY_CERT_INFO_EXTENSION_it
#define PROXY_CERT_INFO_EXTENSION_new GRPC_SHADOW_PROXY_CERT_INFO_EXTENSION_new
#define PROXY_POLICY_free GRPC_SHADOW_PROXY_POLICY_free
#define PROXY_POLICY_it GRPC_SHADOW_PROXY_POLICY_it
#define PROXY_POLICY_new GRPC_SHADOW_PROXY_POLICY_new
#define RAND_SSLeay GRPC_SHADOW_RAND_SSLeay
#define RAND_add GRPC_SHADOW_RAND_add
#define RAND_bytes GRPC_SHADOW_RAND_bytes
#define RAND_bytes_with_additional_data GRPC_SHADOW_RAND_bytes_with_additional_data
#define RAND_cleanup GRPC_SHADOW_RAND_cleanup
#define RAND_egd GRPC_SHADOW_RAND_egd
#define RAND_enable_fork_unsafe_buffering GRPC_SHADOW_RAND_enable_fork_unsafe_buffering
#define RAND_file_name GRPC_SHADOW_RAND_file_name
#define RAND_get_rand_method GRPC_SHADOW_RAND_get_rand_method
#define RAND_load_file GRPC_SHADOW_RAND_load_file
#define RAND_poll GRPC_SHADOW_RAND_poll
#define RAND_pseudo_bytes GRPC_SHADOW_RAND_pseudo_bytes
#define RAND_seed GRPC_SHADOW_RAND_seed
#define RAND_set_rand_method GRPC_SHADOW_RAND_set_rand_method
#define RAND_set_urandom_fd GRPC_SHADOW_RAND_set_urandom_fd
#define RAND_status GRPC_SHADOW_RAND_status
#define RC4 GRPC_SHADOW_RC4
#define RC4_set_key GRPC_SHADOW_RC4_set_key
#define RSAPrivateKey_dup GRPC_SHADOW_RSAPrivateKey_dup
#define RSAPublicKey_dup GRPC_SHADOW_RSAPublicKey_dup
#define RSAZ_1024_mod_exp_avx2 GRPC_SHADOW_RSAZ_1024_mod_exp_avx2
#define RSA_PSS_PARAMS_free GRPC_SHADOW_RSA_PSS_PARAMS_free
#define RSA_PSS_PARAMS_it GRPC_SHADOW_RSA_PSS_PARAMS_it
#define RSA_PSS_PARAMS_new GRPC_SHADOW_RSA_PSS_PARAMS_new
#define RSA_add_pkcs1_prefix GRPC_SHADOW_RSA_add_pkcs1_prefix
#define RSA_bits GRPC_SHADOW_RSA_bits
#define RSA_blinding_on GRPC_SHADOW_RSA_blinding_on
#define RSA_check_fips GRPC_SHADOW_RSA_check_fips
#define RSA_check_key GRPC_SHADOW_RSA_check_key
#define RSA_decrypt GRPC_SHADOW_RSA_decrypt
#define RSA_default_method GRPC_SHADOW_RSA_default_method
#define RSA_encrypt GRPC_SHADOW_RSA_encrypt
#define RSA_flags GRPC_SHADOW_RSA_flags
#define RSA_free GRPC_SHADOW_RSA_free
#define RSA_generate_key_ex GRPC_SHADOW_RSA_generate_key_ex
#define RSA_generate_key_fips GRPC_SHADOW_RSA_generate_key_fips
#define RSA_get0_crt_params GRPC_SHADOW_RSA_get0_crt_params
#define RSA_get0_factors GRPC_SHADOW_RSA_get0_factors
#define RSA_get0_key GRPC_SHADOW_RSA_get0_key
#define RSA_get_ex_data GRPC_SHADOW_RSA_get_ex_data
#define RSA_get_ex_new_index GRPC_SHADOW_RSA_get_ex_new_index
#define RSA_is_opaque GRPC_SHADOW_RSA_is_opaque
#define RSA_marshal_private_key GRPC_SHADOW_RSA_marshal_private_key
#define RSA_marshal_public_key GRPC_SHADOW_RSA_marshal_public_key
#define RSA_new GRPC_SHADOW_RSA_new
#define RSA_new_method GRPC_SHADOW_RSA_new_method
#define RSA_padding_add_PKCS1_OAEP_mgf1 GRPC_SHADOW_RSA_padding_add_PKCS1_OAEP_mgf1
#define RSA_padding_add_PKCS1_PSS_mgf1 GRPC_SHADOW_RSA_padding_add_PKCS1_PSS_mgf1
#define RSA_padding_add_PKCS1_type_1 GRPC_SHADOW_RSA_padding_add_PKCS1_type_1
#define RSA_padding_add_PKCS1_type_2 GRPC_SHADOW_RSA_padding_add_PKCS1_type_2
#define RSA_padding_add_none GRPC_SHADOW_RSA_padding_add_none
#define RSA_padding_check_PKCS1_OAEP_mgf1 GRPC_SHADOW_RSA_padding_check_PKCS1_OAEP_mgf1
#define RSA_padding_check_PKCS1_type_1 GRPC_SHADOW_RSA_padding_check_PKCS1_type_1
#define RSA_padding_check_PKCS1_type_2 GRPC_SHADOW_RSA_padding_check_PKCS1_type_2
#define RSA_parse_private_key GRPC_SHADOW_RSA_parse_private_key
#define RSA_parse_public_key GRPC_SHADOW_RSA_parse_public_key
#define RSA_print GRPC_SHADOW_RSA_print
#define RSA_private_decrypt GRPC_SHADOW_RSA_private_decrypt
#define RSA_private_encrypt GRPC_SHADOW_RSA_private_encrypt
#define RSA_private_key_from_bytes GRPC_SHADOW_RSA_private_key_from_bytes
#define RSA_private_key_to_bytes GRPC_SHADOW_RSA_private_key_to_bytes
#define RSA_private_transform GRPC_SHADOW_RSA_private_transform
#define RSA_public_decrypt GRPC_SHADOW_RSA_public_decrypt
#define RSA_public_encrypt GRPC_SHADOW_RSA_public_encrypt
#define RSA_public_key_from_bytes GRPC_SHADOW_RSA_public_key_from_bytes
#define RSA_public_key_to_bytes GRPC_SHADOW_RSA_public_key_to_bytes
#define RSA_set0_crt_params GRPC_SHADOW_RSA_set0_crt_params
#define RSA_set0_factors GRPC_SHADOW_RSA_set0_factors
#define RSA_set0_key GRPC_SHADOW_RSA_set0_key
#define RSA_set_ex_data GRPC_SHADOW_RSA_set_ex_data
#define RSA_sign GRPC_SHADOW_RSA_sign
#define RSA_sign_pss_mgf1 GRPC_SHADOW_RSA_sign_pss_mgf1
#define RSA_sign_raw GRPC_SHADOW_RSA_sign_raw
#define RSA_size GRPC_SHADOW_RSA_size
#define RSA_up_ref GRPC_SHADOW_RSA_up_ref
#define RSA_verify GRPC_SHADOW_RSA_verify
#define RSA_verify_PKCS1_PSS_mgf1 GRPC_SHADOW_RSA_verify_PKCS1_PSS_mgf1
#define RSA_verify_pss_mgf1 GRPC_SHADOW_RSA_verify_pss_mgf1
#define RSA_verify_raw GRPC_SHADOW_RSA_verify_raw
#define SHA1 GRPC_SHADOW_SHA1
#define SHA1_Final GRPC_SHADOW_SHA1_Final
#define SHA1_Init GRPC_SHADOW_SHA1_Init
#define SHA1_Transform GRPC_SHADOW_SHA1_Transform
#define SHA1_Update GRPC_SHADOW_SHA1_Update
#define SHA224 GRPC_SHADOW_SHA224
#define SHA224_Final GRPC_SHADOW_SHA224_Final
#define SHA224_Init GRPC_SHADOW_SHA224_Init
#define SHA224_Update GRPC_SHADOW_SHA224_Update
#define SHA256 GRPC_SHADOW_SHA256
#define SHA256_Final GRPC_SHADOW_SHA256_Final
#define SHA256_Init GRPC_SHADOW_SHA256_Init
#define SHA256_Transform GRPC_SHADOW_SHA256_Transform
#define SHA256_TransformBlocks GRPC_SHADOW_SHA256_TransformBlocks
#define SHA256_Update GRPC_SHADOW_SHA256_Update
#define SHA384 GRPC_SHADOW_SHA384
#define SHA384_Final GRPC_SHADOW_SHA384_Final
#define SHA384_Init GRPC_SHADOW_SHA384_Init
#define SHA384_Update GRPC_SHADOW_SHA384_Update
#define SHA512 GRPC_SHADOW_SHA512
#define SHA512_Final GRPC_SHADOW_SHA512_Final
#define SHA512_Init GRPC_SHADOW_SHA512_Init
#define SHA512_Transform GRPC_SHADOW_SHA512_Transform
#define SHA512_Update GRPC_SHADOW_SHA512_Update
#define SIPHASH_24 GRPC_SHADOW_SIPHASH_24
#define SPAKE2_CTX_free GRPC_SHADOW_SPAKE2_CTX_free
#define SPAKE2_CTX_new GRPC_SHADOW_SPAKE2_CTX_new
#define SPAKE2_generate_msg GRPC_SHADOW_SPAKE2_generate_msg
#define SPAKE2_process_msg GRPC_SHADOW_SPAKE2_process_msg
#define SSL_CIPHER_description GRPC_SHADOW_SSL_CIPHER_description
#define SSL_CIPHER_get_auth_nid GRPC_SHADOW_SSL_CIPHER_get_auth_nid
#define SSL_CIPHER_get_bits GRPC_SHADOW_SSL_CIPHER_get_bits
#define SSL_CIPHER_get_cipher_nid GRPC_SHADOW_SSL_CIPHER_get_cipher_nid
#define SSL_CIPHER_get_digest_nid GRPC_SHADOW_SSL_CIPHER_get_digest_nid
#define SSL_CIPHER_get_id GRPC_SHADOW_SSL_CIPHER_get_id
#define SSL_CIPHER_get_kx_name GRPC_SHADOW_SSL_CIPHER_get_kx_name
#define SSL_CIPHER_get_kx_nid GRPC_SHADOW_SSL_CIPHER_get_kx_nid
#define SSL_CIPHER_get_max_version GRPC_SHADOW_SSL_CIPHER_get_max_version
#define SSL_CIPHER_get_min_version GRPC_SHADOW_SSL_CIPHER_get_min_version
#define SSL_CIPHER_get_name GRPC_SHADOW_SSL_CIPHER_get_name
#define SSL_CIPHER_get_prf_nid GRPC_SHADOW_SSL_CIPHER_get_prf_nid
#define SSL_CIPHER_get_rfc_name GRPC_SHADOW_SSL_CIPHER_get_rfc_name
#define SSL_CIPHER_get_value GRPC_SHADOW_SSL_CIPHER_get_value
#define SSL_CIPHER_get_version GRPC_SHADOW_SSL_CIPHER_get_version
#define SSL_CIPHER_is_aead GRPC_SHADOW_SSL_CIPHER_is_aead
#define SSL_CIPHER_is_block_cipher GRPC_SHADOW_SSL_CIPHER_is_block_cipher
#define SSL_CIPHER_standard_name GRPC_SHADOW_SSL_CIPHER_standard_name
#define SSL_COMP_add_compression_method GRPC_SHADOW_SSL_COMP_add_compression_method
#define SSL_COMP_free_compression_methods GRPC_SHADOW_SSL_COMP_free_compression_methods
#define SSL_COMP_get0_name GRPC_SHADOW_SSL_COMP_get0_name
#define SSL_COMP_get_compression_methods GRPC_SHADOW_SSL_COMP_get_compression_methods
#define SSL_COMP_get_id GRPC_SHADOW_SSL_COMP_get_id
#define SSL_COMP_get_name GRPC_SHADOW_SSL_COMP_get_name
#define SSL_CTX_add0_chain_cert GRPC_SHADOW_SSL_CTX_add0_chain_cert
#define SSL_CTX_add1_chain_cert GRPC_SHADOW_SSL_CTX_add1_chain_cert
#define SSL_CTX_add_cert_compression_alg GRPC_SHADOW_SSL_CTX_add_cert_compression_alg
#define SSL_CTX_add_client_CA GRPC_SHADOW_SSL_CTX_add_client_CA
#define SSL_CTX_add_extra_chain_cert GRPC_SHADOW_SSL_CTX_add_extra_chain_cert
#define SSL_CTX_add_session GRPC_SHADOW_SSL_CTX_add_session
#define SSL_CTX_check_private_key GRPC_SHADOW_SSL_CTX_check_private_key
#define SSL_CTX_cipher_in_group GRPC_SHADOW_SSL_CTX_cipher_in_group
#define SSL_CTX_clear_chain_certs GRPC_SHADOW_SSL_CTX_clear_chain_certs
#define SSL_CTX_clear_extra_chain_certs GRPC_SHADOW_SSL_CTX_clear_extra_chain_certs
#define SSL_CTX_clear_mode GRPC_SHADOW_SSL_CTX_clear_mode
#define SSL_CTX_clear_options GRPC_SHADOW_SSL_CTX_clear_options
#define SSL_CTX_enable_ocsp_stapling GRPC_SHADOW_SSL_CTX_enable_ocsp_stapling
#define SSL_CTX_enable_pq_experiment_signal GRPC_SHADOW_SSL_CTX_enable_pq_experiment_signal
#define SSL_CTX_enable_signed_cert_timestamps GRPC_SHADOW_SSL_CTX_enable_signed_cert_timestamps
#define SSL_CTX_enable_tls_channel_id GRPC_SHADOW_SSL_CTX_enable_tls_channel_id
#define SSL_CTX_flush_sessions GRPC_SHADOW_SSL_CTX_flush_sessions
#define SSL_CTX_free GRPC_SHADOW_SSL_CTX_free
#define SSL_CTX_get0_certificate GRPC_SHADOW_SSL_CTX_get0_certificate
#define SSL_CTX_get0_chain_certs GRPC_SHADOW_SSL_CTX_get0_chain_certs
#define SSL_CTX_get0_param GRPC_SHADOW_SSL_CTX_get0_param
#define SSL_CTX_get0_privatekey GRPC_SHADOW_SSL_CTX_get0_privatekey
#define SSL_CTX_get_cert_store GRPC_SHADOW_SSL_CTX_get_cert_store
#define SSL_CTX_get_channel_id_cb GRPC_SHADOW_SSL_CTX_get_channel_id_cb
#define SSL_CTX_get_ciphers GRPC_SHADOW_SSL_CTX_get_ciphers
#define SSL_CTX_get_client_CA_list GRPC_SHADOW_SSL_CTX_get_client_CA_list
#define SSL_CTX_get_default_passwd_cb GRPC_SHADOW_SSL_CTX_get_default_passwd_cb
#define SSL_CTX_get_default_passwd_cb_userdata GRPC_SHADOW_SSL_CTX_get_default_passwd_cb_userdata
#define SSL_CTX_get_ex_data GRPC_SHADOW_SSL_CTX_get_ex_data
#define SSL_CTX_get_ex_new_index GRPC_SHADOW_SSL_CTX_get_ex_new_index
#define SSL_CTX_get_extra_chain_certs GRPC_SHADOW_SSL_CTX_get_extra_chain_certs
#define SSL_CTX_get_info_callback GRPC_SHADOW_SSL_CTX_get_info_callback
#define SSL_CTX_get_keylog_callback GRPC_SHADOW_SSL_CTX_get_keylog_callback
#define SSL_CTX_get_max_cert_list GRPC_SHADOW_SSL_CTX_get_max_cert_list
#define SSL_CTX_get_max_proto_version GRPC_SHADOW_SSL_CTX_get_max_proto_version
#define SSL_CTX_get_min_proto_version GRPC_SHADOW_SSL_CTX_get_min_proto_version
#define SSL_CTX_get_mode GRPC_SHADOW_SSL_CTX_get_mode
#define SSL_CTX_get_options GRPC_SHADOW_SSL_CTX_get_options
#define SSL_CTX_get_quiet_shutdown GRPC_SHADOW_SSL_CTX_get_quiet_shutdown
#define SSL_CTX_get_read_ahead GRPC_SHADOW_SSL_CTX_get_read_ahead
#define SSL_CTX_get_session_cache_mode GRPC_SHADOW_SSL_CTX_get_session_cache_mode
#define SSL_CTX_get_timeout GRPC_SHADOW_SSL_CTX_get_timeout
#define SSL_CTX_get_tlsext_ticket_keys GRPC_SHADOW_SSL_CTX_get_tlsext_ticket_keys
#define SSL_CTX_get_verify_callback GRPC_SHADOW_SSL_CTX_get_verify_callback
#define SSL_CTX_get_verify_depth GRPC_SHADOW_SSL_CTX_get_verify_depth
#define SSL_CTX_get_verify_mode GRPC_SHADOW_SSL_CTX_get_verify_mode
#define SSL_CTX_load_verify_locations GRPC_SHADOW_SSL_CTX_load_verify_locations
#define SSL_CTX_need_tmp_RSA GRPC_SHADOW_SSL_CTX_need_tmp_RSA
#define SSL_CTX_new GRPC_SHADOW_SSL_CTX_new
#define SSL_CTX_remove_session GRPC_SHADOW_SSL_CTX_remove_session
#define SSL_CTX_sess_accept GRPC_SHADOW_SSL_CTX_sess_accept
#define SSL_CTX_sess_accept_good GRPC_SHADOW_SSL_CTX_sess_accept_good
#define SSL_CTX_sess_accept_renegotiate GRPC_SHADOW_SSL_CTX_sess_accept_renegotiate
#define SSL_CTX_sess_cache_full GRPC_SHADOW_SSL_CTX_sess_cache_full
#define SSL_CTX_sess_cb_hits GRPC_SHADOW_SSL_CTX_sess_cb_hits
#define SSL_CTX_sess_connect GRPC_SHADOW_SSL_CTX_sess_connect
#define SSL_CTX_sess_connect_good GRPC_SHADOW_SSL_CTX_sess_connect_good
#define SSL_CTX_sess_connect_renegotiate GRPC_SHADOW_SSL_CTX_sess_connect_renegotiate
#define SSL_CTX_sess_get_cache_size GRPC_SHADOW_SSL_CTX_sess_get_cache_size
#define SSL_CTX_sess_get_get_cb GRPC_SHADOW_SSL_CTX_sess_get_get_cb
#define SSL_CTX_sess_get_new_cb GRPC_SHADOW_SSL_CTX_sess_get_new_cb
#define SSL_CTX_sess_get_remove_cb GRPC_SHADOW_SSL_CTX_sess_get_remove_cb
#define SSL_CTX_sess_hits GRPC_SHADOW_SSL_CTX_sess_hits
#define SSL_CTX_sess_misses GRPC_SHADOW_SSL_CTX_sess_misses
#define SSL_CTX_sess_number GRPC_SHADOW_SSL_CTX_sess_number
#define SSL_CTX_sess_set_cache_size GRPC_SHADOW_SSL_CTX_sess_set_cache_size
#define SSL_CTX_sess_set_get_cb GRPC_SHADOW_SSL_CTX_sess_set_get_cb
#define SSL_CTX_sess_set_new_cb GRPC_SHADOW_SSL_CTX_sess_set_new_cb
#define SSL_CTX_sess_set_remove_cb GRPC_SHADOW_SSL_CTX_sess_set_remove_cb
#define SSL_CTX_sess_timeouts GRPC_SHADOW_SSL_CTX_sess_timeouts
#define SSL_CTX_set0_buffer_pool GRPC_SHADOW_SSL_CTX_set0_buffer_pool
#define SSL_CTX_set0_chain GRPC_SHADOW_SSL_CTX_set0_chain
#define SSL_CTX_set0_client_CAs GRPC_SHADOW_SSL_CTX_set0_client_CAs
#define SSL_CTX_set0_verify_cert_store GRPC_SHADOW_SSL_CTX_set0_verify_cert_store
#define SSL_CTX_set1_chain GRPC_SHADOW_SSL_CTX_set1_chain
#define SSL_CTX_set1_curves GRPC_SHADOW_SSL_CTX_set1_curves
#define SSL_CTX_set1_curves_list GRPC_SHADOW_SSL_CTX_set1_curves_list
#define SSL_CTX_set1_param GRPC_SHADOW_SSL_CTX_set1_param
#define SSL_CTX_set1_sigalgs GRPC_SHADOW_SSL_CTX_set1_sigalgs
#define SSL_CTX_set1_sigalgs_list GRPC_SHADOW_SSL_CTX_set1_sigalgs_list
#define SSL_CTX_set1_tls_channel_id GRPC_SHADOW_SSL_CTX_set1_tls_channel_id
#define SSL_CTX_set1_verify_cert_store GRPC_SHADOW_SSL_CTX_set1_verify_cert_store
#define SSL_CTX_set_allow_unknown_alpn_protos GRPC_SHADOW_SSL_CTX_set_allow_unknown_alpn_protos
#define SSL_CTX_set_alpn_protos GRPC_SHADOW_SSL_CTX_set_alpn_protos
#define SSL_CTX_set_alpn_select_cb GRPC_SHADOW_SSL_CTX_set_alpn_select_cb
#define SSL_CTX_set_cert_cb GRPC_SHADOW_SSL_CTX_set_cert_cb
#define SSL_CTX_set_cert_store GRPC_SHADOW_SSL_CTX_set_cert_store
#define SSL_CTX_set_cert_verify_callback GRPC_SHADOW_SSL_CTX_set_cert_verify_callback
#define SSL_CTX_set_chain_and_key GRPC_SHADOW_SSL_CTX_set_chain_and_key
#define SSL_CTX_set_channel_id_cb GRPC_SHADOW_SSL_CTX_set_channel_id_cb
#define SSL_CTX_set_cipher_list GRPC_SHADOW_SSL_CTX_set_cipher_list
#define SSL_CTX_set_client_CA_list GRPC_SHADOW_SSL_CTX_set_client_CA_list
#define SSL_CTX_set_client_cert_cb GRPC_SHADOW_SSL_CTX_set_client_cert_cb
#define SSL_CTX_set_current_time_cb GRPC_SHADOW_SSL_CTX_set_current_time_cb
#define SSL_CTX_set_custom_verify GRPC_SHADOW_SSL_CTX_set_custom_verify
#define SSL_CTX_set_default_passwd_cb GRPC_SHADOW_SSL_CTX_set_default_passwd_cb
#define SSL_CTX_set_default_passwd_cb_userdata GRPC_SHADOW_SSL_CTX_set_default_passwd_cb_userdata
#define SSL_CTX_set_default_verify_paths GRPC_SHADOW_SSL_CTX_set_default_verify_paths
#define SSL_CTX_set_dos_protection_cb GRPC_SHADOW_SSL_CTX_set_dos_protection_cb
#define SSL_CTX_set_early_data_enabled GRPC_SHADOW_SSL_CTX_set_early_data_enabled
#define SSL_CTX_set_ed25519_enabled GRPC_SHADOW_SSL_CTX_set_ed25519_enabled
#define SSL_CTX_set_ex_data GRPC_SHADOW_SSL_CTX_set_ex_data
#define SSL_CTX_set_false_start_allowed_without_alpn GRPC_SHADOW_SSL_CTX_set_false_start_allowed_without_alpn
#define SSL_CTX_set_grease_enabled GRPC_SHADOW_SSL_CTX_set_grease_enabled
#define SSL_CTX_set_ignore_tls13_downgrade GRPC_SHADOW_SSL_CTX_set_ignore_tls13_downgrade
#define SSL_CTX_set_info_callback GRPC_SHADOW_SSL_CTX_set_info_callback
#define SSL_CTX_set_keylog_callback GRPC_SHADOW_SSL_CTX_set_keylog_callback
#define SSL_CTX_set_max_cert_list GRPC_SHADOW_SSL_CTX_set_max_cert_list
#define SSL_CTX_set_max_proto_version GRPC_SHADOW_SSL_CTX_set_max_proto_version
#define SSL_CTX_set_max_send_fragment GRPC_SHADOW_SSL_CTX_set_max_send_fragment
#define SSL_CTX_set_min_proto_version GRPC_SHADOW_SSL_CTX_set_min_proto_version
#define SSL_CTX_set_mode GRPC_SHADOW_SSL_CTX_set_mode
#define SSL_CTX_set_msg_callback GRPC_SHADOW_SSL_CTX_set_msg_callback
#define SSL_CTX_set_msg_callback_arg GRPC_SHADOW_SSL_CTX_set_msg_callback_arg
#define SSL_CTX_set_next_proto_select_cb GRPC_SHADOW_SSL_CTX_set_next_proto_select_cb
#define SSL_CTX_set_next_protos_advertised_cb GRPC_SHADOW_SSL_CTX_set_next_protos_advertised_cb
#define SSL_CTX_set_ocsp_response GRPC_SHADOW_SSL_CTX_set_ocsp_response
#define SSL_CTX_set_options GRPC_SHADOW_SSL_CTX_set_options
#define SSL_CTX_set_private_key_method GRPC_SHADOW_SSL_CTX_set_private_key_method
#define SSL_CTX_set_psk_client_callback GRPC_SHADOW_SSL_CTX_set_psk_client_callback
#define SSL_CTX_set_psk_server_callback GRPC_SHADOW_SSL_CTX_set_psk_server_callback
#define SSL_CTX_set_purpose GRPC_SHADOW_SSL_CTX_set_purpose
#define SSL_CTX_set_quic_method GRPC_SHADOW_SSL_CTX_set_quic_method
#define SSL_CTX_set_quiet_shutdown GRPC_SHADOW_SSL_CTX_set_quiet_shutdown
#define SSL_CTX_set_read_ahead GRPC_SHADOW_SSL_CTX_set_read_ahead
#define SSL_CTX_set_retain_only_sha256_of_client_certs GRPC_SHADOW_SSL_CTX_set_retain_only_sha256_of_client_certs
#define SSL_CTX_set_reverify_on_resume GRPC_SHADOW_SSL_CTX_set_reverify_on_resume
#define SSL_CTX_set_rsa_pss_rsae_certs_enabled GRPC_SHADOW_SSL_CTX_set_rsa_pss_rsae_certs_enabled
#define SSL_CTX_set_select_certificate_cb GRPC_SHADOW_SSL_CTX_set_select_certificate_cb
#define SSL_CTX_set_session_cache_mode GRPC_SHADOW_SSL_CTX_set_session_cache_mode
#define SSL_CTX_set_session_id_context GRPC_SHADOW_SSL_CTX_set_session_id_context
#define SSL_CTX_set_session_psk_dhe_timeout GRPC_SHADOW_SSL_CTX_set_session_psk_dhe_timeout
#define SSL_CTX_set_signed_cert_timestamp_list GRPC_SHADOW_SSL_CTX_set_signed_cert_timestamp_list
#define SSL_CTX_set_signing_algorithm_prefs GRPC_SHADOW_SSL_CTX_set_signing_algorithm_prefs
#define SSL_CTX_set_srtp_profiles GRPC_SHADOW_SSL_CTX_set_srtp_profiles
#define SSL_CTX_set_strict_cipher_list GRPC_SHADOW_SSL_CTX_set_strict_cipher_list
#define SSL_CTX_set_ticket_aead_method GRPC_SHADOW_SSL_CTX_set_ticket_aead_method
#define SSL_CTX_set_timeout GRPC_SHADOW_SSL_CTX_set_timeout
#define SSL_CTX_set_tls_channel_id_enabled GRPC_SHADOW_SSL_CTX_set_tls_channel_id_enabled
#define SSL_CTX_set_tlsext_servername_arg GRPC_SHADOW_SSL_CTX_set_tlsext_servername_arg
#define SSL_CTX_set_tlsext_servername_callback GRPC_SHADOW_SSL_CTX_set_tlsext_servername_callback
#define SSL_CTX_set_tlsext_status_arg GRPC_SHADOW_SSL_CTX_set_tlsext_status_arg
#define SSL_CTX_set_tlsext_status_cb GRPC_SHADOW_SSL_CTX_set_tlsext_status_cb
#define SSL_CTX_set_tlsext_ticket_key_cb GRPC_SHADOW_SSL_CTX_set_tlsext_ticket_key_cb
#define SSL_CTX_set_tlsext_ticket_keys GRPC_SHADOW_SSL_CTX_set_tlsext_ticket_keys
#define SSL_CTX_set_tlsext_use_srtp GRPC_SHADOW_SSL_CTX_set_tlsext_use_srtp
#define SSL_CTX_set_tmp_dh GRPC_SHADOW_SSL_CTX_set_tmp_dh
#define SSL_CTX_set_tmp_dh_callback GRPC_SHADOW_SSL_CTX_set_tmp_dh_callback
#define SSL_CTX_set_tmp_ecdh GRPC_SHADOW_SSL_CTX_set_tmp_ecdh
#define SSL_CTX_set_tmp_rsa GRPC_SHADOW_SSL_CTX_set_tmp_rsa
#define SSL_CTX_set_tmp_rsa_callback GRPC_SHADOW_SSL_CTX_set_tmp_rsa_callback
#define SSL_CTX_set_trust GRPC_SHADOW_SSL_CTX_set_trust
#define SSL_CTX_set_verify GRPC_SHADOW_SSL_CTX_set_verify
#define SSL_CTX_set_verify_algorithm_prefs GRPC_SHADOW_SSL_CTX_set_verify_algorithm_prefs
#define SSL_CTX_set_verify_depth GRPC_SHADOW_SSL_CTX_set_verify_depth
#define SSL_CTX_up_ref GRPC_SHADOW_SSL_CTX_up_ref
#define SSL_CTX_use_PrivateKey GRPC_SHADOW_SSL_CTX_use_PrivateKey
#define SSL_CTX_use_PrivateKey_ASN1 GRPC_SHADOW_SSL_CTX_use_PrivateKey_ASN1
#define SSL_CTX_use_PrivateKey_file GRPC_SHADOW_SSL_CTX_use_PrivateKey_file
#define SSL_CTX_use_RSAPrivateKey GRPC_SHADOW_SSL_CTX_use_RSAPrivateKey
#define SSL_CTX_use_RSAPrivateKey_ASN1 GRPC_SHADOW_SSL_CTX_use_RSAPrivateKey_ASN1
#define SSL_CTX_use_RSAPrivateKey_file GRPC_SHADOW_SSL_CTX_use_RSAPrivateKey_file
#define SSL_CTX_use_certificate GRPC_SHADOW_SSL_CTX_use_certificate
#define SSL_CTX_use_certificate_ASN1 GRPC_SHADOW_SSL_CTX_use_certificate_ASN1
#define SSL_CTX_use_certificate_chain_file GRPC_SHADOW_SSL_CTX_use_certificate_chain_file
#define SSL_CTX_use_certificate_file GRPC_SHADOW_SSL_CTX_use_certificate_file
#define SSL_CTX_use_psk_identity_hint GRPC_SHADOW_SSL_CTX_use_psk_identity_hint
#define SSL_SESSION_early_data_capable GRPC_SHADOW_SSL_SESSION_early_data_capable
#define SSL_SESSION_free GRPC_SHADOW_SSL_SESSION_free
#define SSL_SESSION_from_bytes GRPC_SHADOW_SSL_SESSION_from_bytes
#define SSL_SESSION_get0_cipher GRPC_SHADOW_SSL_SESSION_get0_cipher
#define SSL_SESSION_get0_id_context GRPC_SHADOW_SSL_SESSION_get0_id_context
#define SSL_SESSION_get0_ocsp_response GRPC_SHADOW_SSL_SESSION_get0_ocsp_response
#define SSL_SESSION_get0_peer GRPC_SHADOW_SSL_SESSION_get0_peer
#define SSL_SESSION_get0_peer_certificates GRPC_SHADOW_SSL_SESSION_get0_peer_certificates
#define SSL_SESSION_get0_peer_sha256 GRPC_SHADOW_SSL_SESSION_get0_peer_sha256
#define SSL_SESSION_get0_signed_cert_timestamp_list GRPC_SHADOW_SSL_SESSION_get0_signed_cert_timestamp_list
#define SSL_SESSION_get0_ticket GRPC_SHADOW_SSL_SESSION_get0_ticket
#define SSL_SESSION_get_ex_data GRPC_SHADOW_SSL_SESSION_get_ex_data
#define SSL_SESSION_get_ex_new_index GRPC_SHADOW_SSL_SESSION_get_ex_new_index
#define SSL_SESSION_get_id GRPC_SHADOW_SSL_SESSION_get_id
#define SSL_SESSION_get_master_key GRPC_SHADOW_SSL_SESSION_get_master_key
#define SSL_SESSION_get_protocol_version GRPC_SHADOW_SSL_SESSION_get_protocol_version
#define SSL_SESSION_get_ticket_lifetime_hint GRPC_SHADOW_SSL_SESSION_get_ticket_lifetime_hint
#define SSL_SESSION_get_time GRPC_SHADOW_SSL_SESSION_get_time
#define SSL_SESSION_get_timeout GRPC_SHADOW_SSL_SESSION_get_timeout
#define SSL_SESSION_get_version GRPC_SHADOW_SSL_SESSION_get_version
#define SSL_SESSION_has_peer_sha256 GRPC_SHADOW_SSL_SESSION_has_peer_sha256
#define SSL_SESSION_has_ticket GRPC_SHADOW_SSL_SESSION_has_ticket
#define SSL_SESSION_is_resumable GRPC_SHADOW_SSL_SESSION_is_resumable
#define SSL_SESSION_new GRPC_SHADOW_SSL_SESSION_new
#define SSL_SESSION_set1_id GRPC_SHADOW_SSL_SESSION_set1_id
#define SSL_SESSION_set1_id_context GRPC_SHADOW_SSL_SESSION_set1_id_context
#define SSL_SESSION_set_ex_data GRPC_SHADOW_SSL_SESSION_set_ex_data
#define SSL_SESSION_set_protocol_version GRPC_SHADOW_SSL_SESSION_set_protocol_version
#define SSL_SESSION_set_ticket GRPC_SHADOW_SSL_SESSION_set_ticket
#define SSL_SESSION_set_time GRPC_SHADOW_SSL_SESSION_set_time
#define SSL_SESSION_set_timeout GRPC_SHADOW_SSL_SESSION_set_timeout
#define SSL_SESSION_should_be_single_use GRPC_SHADOW_SSL_SESSION_should_be_single_use
#define SSL_SESSION_to_bytes GRPC_SHADOW_SSL_SESSION_to_bytes
#define SSL_SESSION_to_bytes_for_ticket GRPC_SHADOW_SSL_SESSION_to_bytes_for_ticket
#define SSL_SESSION_up_ref GRPC_SHADOW_SSL_SESSION_up_ref
#define SSL_accept GRPC_SHADOW_SSL_accept
#define SSL_add0_chain_cert GRPC_SHADOW_SSL_add0_chain_cert
#define SSL_add1_chain_cert GRPC_SHADOW_SSL_add1_chain_cert
#define SSL_add_client_CA GRPC_SHADOW_SSL_add_client_CA
#define SSL_add_file_cert_subjects_to_stack GRPC_SHADOW_SSL_add_file_cert_subjects_to_stack
#define SSL_alert_desc_string GRPC_SHADOW_SSL_alert_desc_string
#define SSL_alert_desc_string_long GRPC_SHADOW_SSL_alert_desc_string_long
#define SSL_alert_from_verify_result GRPC_SHADOW_SSL_alert_from_verify_result
#define SSL_alert_type_string GRPC_SHADOW_SSL_alert_type_string
#define SSL_alert_type_string_long GRPC_SHADOW_SSL_alert_type_string_long
#define SSL_cache_hit GRPC_SHADOW_SSL_cache_hit
#define SSL_certs_clear GRPC_SHADOW_SSL_certs_clear
#define SSL_check_private_key GRPC_SHADOW_SSL_check_private_key
#define SSL_clear GRPC_SHADOW_SSL_clear
#define SSL_clear_chain_certs GRPC_SHADOW_SSL_clear_chain_certs
#define SSL_clear_mode GRPC_SHADOW_SSL_clear_mode
#define SSL_clear_options GRPC_SHADOW_SSL_clear_options
#define SSL_connect GRPC_SHADOW_SSL_connect
#define SSL_cutthrough_complete GRPC_SHADOW_SSL_cutthrough_complete
#define SSL_delegated_credential_used GRPC_SHADOW_SSL_delegated_credential_used
#define SSL_do_handshake GRPC_SHADOW_SSL_do_handshake
#define SSL_dup_CA_list GRPC_SHADOW_SSL_dup_CA_list
#define SSL_early_callback_ctx_extension_get GRPC_SHADOW_SSL_early_callback_ctx_extension_get
#define SSL_early_data_accepted GRPC_SHADOW_SSL_early_data_accepted
#define SSL_enable_ocsp_stapling GRPC_SHADOW_SSL_enable_ocsp_stapling
#define SSL_enable_signed_cert_timestamps GRPC_SHADOW_SSL_enable_signed_cert_timestamps
#define SSL_enable_tls_channel_id GRPC_SHADOW_SSL_enable_tls_channel_id
#define SSL_error_description GRPC_SHADOW_SSL_error_description
#define SSL_export_keying_material GRPC_SHADOW_SSL_export_keying_material
#define SSL_free GRPC_SHADOW_SSL_free
#define SSL_generate_key_block GRPC_SHADOW_SSL_generate_key_block
#define SSL_get0_alpn_selected GRPC_SHADOW_SSL_get0_alpn_selected
#define SSL_get0_certificate_types GRPC_SHADOW_SSL_get0_certificate_types
#define SSL_get0_chain_certs GRPC_SHADOW_SSL_get0_chain_certs
#define SSL_get0_next_proto_negotiated GRPC_SHADOW_SSL_get0_next_proto_negotiated
#define SSL_get0_ocsp_response GRPC_SHADOW_SSL_get0_ocsp_response
#define SSL_get0_param GRPC_SHADOW_SSL_get0_param
#define SSL_get0_peer_certificates GRPC_SHADOW_SSL_get0_peer_certificates
#define SSL_get0_peer_verify_algorithms GRPC_SHADOW_SSL_get0_peer_verify_algorithms
#define SSL_get0_server_requested_CAs GRPC_SHADOW_SSL_get0_server_requested_CAs
#define SSL_get0_session_id_context GRPC_SHADOW_SSL_get0_session_id_context
#define SSL_get0_signed_cert_timestamp_list GRPC_SHADOW_SSL_get0_signed_cert_timestamp_list
#define SSL_get1_session GRPC_SHADOW_SSL_get1_session
#define SSL_get_SSL_CTX GRPC_SHADOW_SSL_get_SSL_CTX
#define SSL_get_certificate GRPC_SHADOW_SSL_get_certificate
#define SSL_get_cipher_by_value GRPC_SHADOW_SSL_get_cipher_by_value
#define SSL_get_cipher_list GRPC_SHADOW_SSL_get_cipher_list
#define SSL_get_ciphers GRPC_SHADOW_SSL_get_ciphers
#define SSL_get_client_CA_list GRPC_SHADOW_SSL_get_client_CA_list
#define SSL_get_client_random GRPC_SHADOW_SSL_get_client_random
#define SSL_get_current_cipher GRPC_SHADOW_SSL_get_current_cipher
#define SSL_get_current_compression GRPC_SHADOW_SSL_get_current_compression
#define SSL_get_current_expansion GRPC_SHADOW_SSL_get_current_expansion
#define SSL_get_curve_id GRPC_SHADOW_SSL_get_curve_id
#define SSL_get_curve_name GRPC_SHADOW_SSL_get_curve_name
#define SSL_get_default_timeout GRPC_SHADOW_SSL_get_default_timeout
#define SSL_get_early_data_reason GRPC_SHADOW_SSL_get_early_data_reason
#define SSL_get_error GRPC_SHADOW_SSL_get_error
#define SSL_get_ex_data GRPC_SHADOW_SSL_get_ex_data
#define SSL_get_ex_data_X509_STORE_CTX_idx GRPC_SHADOW_SSL_get_ex_data_X509_STORE_CTX_idx
#define SSL_get_ex_new_index GRPC_SHADOW_SSL_get_ex_new_index
#define SSL_get_extms_support GRPC_SHADOW_SSL_get_extms_support
#define SSL_get_fd GRPC_SHADOW_SSL_get_fd
#define SSL_get_finished GRPC_SHADOW_SSL_get_finished
#define SSL_get_info_callback GRPC_SHADOW_SSL_get_info_callback
#define SSL_get_ivs GRPC_SHADOW_SSL_get_ivs
#define SSL_get_key_block_len GRPC_SHADOW_SSL_get_key_block_len
#define SSL_get_max_cert_list GRPC_SHADOW_SSL_get_max_cert_list
#define SSL_get_max_proto_version GRPC_SHADOW_SSL_get_max_proto_version
#define SSL_get_min_proto_version GRPC_SHADOW_SSL_get_min_proto_version
#define SSL_get_mode GRPC_SHADOW_SSL_get_mode
#define SSL_get_negotiated_token_binding_param GRPC_SHADOW_SSL_get_negotiated_token_binding_param
#define SSL_get_options GRPC_SHADOW_SSL_get_options
#define SSL_get_peer_cert_chain GRPC_SHADOW_SSL_get_peer_cert_chain
#define SSL_get_peer_certificate GRPC_SHADOW_SSL_get_peer_certificate
#define SSL_get_peer_finished GRPC_SHADOW_SSL_get_peer_finished
#define SSL_get_peer_full_cert_chain GRPC_SHADOW_SSL_get_peer_full_cert_chain
#define SSL_get_peer_quic_transport_params GRPC_SHADOW_SSL_get_peer_quic_transport_params
#define SSL_get_peer_signature_algorithm GRPC_SHADOW_SSL_get_peer_signature_algorithm
#define SSL_get_pending_cipher GRPC_SHADOW_SSL_get_pending_cipher
#define SSL_get_privatekey GRPC_SHADOW_SSL_get_privatekey
#define SSL_get_psk_identity GRPC_SHADOW_SSL_get_psk_identity
#define SSL_get_psk_identity_hint GRPC_SHADOW_SSL_get_psk_identity_hint
#define SSL_get_quiet_shutdown GRPC_SHADOW_SSL_get_quiet_shutdown
#define SSL_get_rbio GRPC_SHADOW_SSL_get_rbio
#define SSL_get_read_ahead GRPC_SHADOW_SSL_get_read_ahead
#define SSL_get_read_sequence GRPC_SHADOW_SSL_get_read_sequence
#define SSL_get_rfd GRPC_SHADOW_SSL_get_rfd
#define SSL_get_secure_renegotiation_support GRPC_SHADOW_SSL_get_secure_renegotiation_support
#define SSL_get_selected_srtp_profile GRPC_SHADOW_SSL_get_selected_srtp_profile
#define SSL_get_server_random GRPC_SHADOW_SSL_get_server_random
#define SSL_get_server_tmp_key GRPC_SHADOW_SSL_get_server_tmp_key
#define SSL_get_servername GRPC_SHADOW_SSL_get_servername
#define SSL_get_servername_type GRPC_SHADOW_SSL_get_servername_type
#define SSL_get_session GRPC_SHADOW_SSL_get_session
#define SSL_get_shared_ciphers GRPC_SHADOW_SSL_get_shared_ciphers
#define SSL_get_shutdown GRPC_SHADOW_SSL_get_shutdown
#define SSL_get_signature_algorithm_digest GRPC_SHADOW_SSL_get_signature_algorithm_digest
#define SSL_get_signature_algorithm_key_type GRPC_SHADOW_SSL_get_signature_algorithm_key_type
#define SSL_get_signature_algorithm_name GRPC_SHADOW_SSL_get_signature_algorithm_name
#define SSL_get_srtp_profiles GRPC_SHADOW_SSL_get_srtp_profiles
#define SSL_get_ticket_age_skew GRPC_SHADOW_SSL_get_ticket_age_skew
#define SSL_get_tls_channel_id GRPC_SHADOW_SSL_get_tls_channel_id
#define SSL_get_tls_unique GRPC_SHADOW_SSL_get_tls_unique
#define SSL_get_tlsext_status_ocsp_resp GRPC_SHADOW_SSL_get_tlsext_status_ocsp_resp
#define SSL_get_tlsext_status_type GRPC_SHADOW_SSL_get_tlsext_status_type
#define SSL_get_verify_callback GRPC_SHADOW_SSL_get_verify_callback
#define SSL_get_verify_depth GRPC_SHADOW_SSL_get_verify_depth
#define SSL_get_verify_mode GRPC_SHADOW_SSL_get_verify_mode
#define SSL_get_verify_result GRPC_SHADOW_SSL_get_verify_result
#define SSL_get_version GRPC_SHADOW_SSL_get_version
#define SSL_get_wbio GRPC_SHADOW_SSL_get_wbio
#define SSL_get_wfd GRPC_SHADOW_SSL_get_wfd
#define SSL_get_write_sequence GRPC_SHADOW_SSL_get_write_sequence
#define SSL_in_early_data GRPC_SHADOW_SSL_in_early_data
#define SSL_in_false_start GRPC_SHADOW_SSL_in_false_start
#define SSL_in_init GRPC_SHADOW_SSL_in_init
#define SSL_is_dtls GRPC_SHADOW_SSL_is_dtls
#define SSL_is_init_finished GRPC_SHADOW_SSL_is_init_finished
#define SSL_is_server GRPC_SHADOW_SSL_is_server
#define SSL_is_signature_algorithm_rsa_pss GRPC_SHADOW_SSL_is_signature_algorithm_rsa_pss
#define SSL_is_tls13_downgrade GRPC_SHADOW_SSL_is_tls13_downgrade
#define SSL_is_token_binding_negotiated GRPC_SHADOW_SSL_is_token_binding_negotiated
#define SSL_key_update GRPC_SHADOW_SSL_key_update
#define SSL_library_init GRPC_SHADOW_SSL_library_init
#define SSL_load_client_CA_file GRPC_SHADOW_SSL_load_client_CA_file
#define SSL_load_error_strings GRPC_SHADOW_SSL_load_error_strings
#define SSL_magic_pending_session_ptr GRPC_SHADOW_SSL_magic_pending_session_ptr
#define SSL_max_seal_overhead GRPC_SHADOW_SSL_max_seal_overhead
#define SSL_need_tmp_RSA GRPC_SHADOW_SSL_need_tmp_RSA
#define SSL_new GRPC_SHADOW_SSL_new
#define SSL_num_renegotiations GRPC_SHADOW_SSL_num_renegotiations
#define SSL_peek GRPC_SHADOW_SSL_peek
#define SSL_pending GRPC_SHADOW_SSL_pending
#define SSL_pq_experiment_signal_seen GRPC_SHADOW_SSL_pq_experiment_signal_seen
#define SSL_process_quic_post_handshake GRPC_SHADOW_SSL_process_quic_post_handshake
#define SSL_provide_quic_data GRPC_SHADOW_SSL_provide_quic_data
#define SSL_quic_max_handshake_flight_len GRPC_SHADOW_SSL_quic_max_handshake_flight_len
#define SSL_quic_read_level GRPC_SHADOW_SSL_quic_read_level
#define SSL_quic_write_level GRPC_SHADOW_SSL_quic_write_level
#define SSL_read GRPC_SHADOW_SSL_read
#define SSL_renegotiate GRPC_SHADOW_SSL_renegotiate
#define SSL_renegotiate_pending GRPC_SHADOW_SSL_renegotiate_pending
#define SSL_reset_early_data_reject GRPC_SHADOW_SSL_reset_early_data_reject
#define SSL_select_next_proto GRPC_SHADOW_SSL_select_next_proto
#define SSL_send_fatal_alert GRPC_SHADOW_SSL_send_fatal_alert
#define SSL_session_reused GRPC_SHADOW_SSL_session_reused
#define SSL_set0_chain GRPC_SHADOW_SSL_set0_chain
#define SSL_set0_client_CAs GRPC_SHADOW_SSL_set0_client_CAs
#define SSL_set0_rbio GRPC_SHADOW_SSL_set0_rbio
#define SSL_set0_verify_cert_store GRPC_SHADOW_SSL_set0_verify_cert_store
#define SSL_set0_wbio GRPC_SHADOW_SSL_set0_wbio
#define SSL_set1_chain GRPC_SHADOW_SSL_set1_chain
#define SSL_set1_curves GRPC_SHADOW_SSL_set1_curves
#define SSL_set1_curves_list GRPC_SHADOW_SSL_set1_curves_list
#define SSL_set1_delegated_credential GRPC_SHADOW_SSL_set1_delegated_credential
#define SSL_set1_param GRPC_SHADOW_SSL_set1_param
#define SSL_set1_sigalgs GRPC_SHADOW_SSL_set1_sigalgs
#define SSL_set1_sigalgs_list GRPC_SHADOW_SSL_set1_sigalgs_list
#define SSL_set1_tls_channel_id GRPC_SHADOW_SSL_set1_tls_channel_id
#define SSL_set1_verify_cert_store GRPC_SHADOW_SSL_set1_verify_cert_store
#define SSL_set_SSL_CTX GRPC_SHADOW_SSL_set_SSL_CTX
#define SSL_set_accept_state GRPC_SHADOW_SSL_set_accept_state
#define SSL_set_alpn_protos GRPC_SHADOW_SSL_set_alpn_protos
#define SSL_set_bio GRPC_SHADOW_SSL_set_bio
#define SSL_set_cert_cb GRPC_SHADOW_SSL_set_cert_cb
#define SSL_set_chain_and_key GRPC_SHADOW_SSL_set_chain_and_key
#define SSL_set_cipher_list GRPC_SHADOW_SSL_set_cipher_list
#define SSL_set_client_CA_list GRPC_SHADOW_SSL_set_client_CA_list
#define SSL_set_connect_state GRPC_SHADOW_SSL_set_connect_state
#define SSL_set_custom_verify GRPC_SHADOW_SSL_set_custom_verify
#define SSL_set_early_data_enabled GRPC_SHADOW_SSL_set_early_data_enabled
#define SSL_set_enforce_rsa_key_usage GRPC_SHADOW_SSL_set_enforce_rsa_key_usage
#define SSL_set_ex_data GRPC_SHADOW_SSL_set_ex_data
#define SSL_set_fd GRPC_SHADOW_SSL_set_fd
#define SSL_set_ignore_tls13_downgrade GRPC_SHADOW_SSL_set_ignore_tls13_downgrade
#define SSL_set_info_callback GRPC_SHADOW_SSL_set_info_callback
#define SSL_set_jdk11_workaround GRPC_SHADOW_SSL_set_jdk11_workaround
#define SSL_set_max_cert_list GRPC_SHADOW_SSL_set_max_cert_list
#define SSL_set_max_proto_version GRPC_SHADOW_SSL_set_max_proto_version
#define SSL_set_max_send_fragment GRPC_SHADOW_SSL_set_max_send_fragment
#define SSL_set_min_proto_version GRPC_SHADOW_SSL_set_min_proto_version
#define SSL_set_mode GRPC_SHADOW_SSL_set_mode
#define SSL_set_msg_callback GRPC_SHADOW_SSL_set_msg_callback
#define SSL_set_msg_callback_arg GRPC_SHADOW_SSL_set_msg_callback_arg
#define SSL_set_mtu GRPC_SHADOW_SSL_set_mtu
#define SSL_set_ocsp_response GRPC_SHADOW_SSL_set_ocsp_response
#define SSL_set_options GRPC_SHADOW_SSL_set_options
#define SSL_set_private_key_method GRPC_SHADOW_SSL_set_private_key_method
#define SSL_set_psk_client_callback GRPC_SHADOW_SSL_set_psk_client_callback
#define SSL_set_psk_server_callback GRPC_SHADOW_SSL_set_psk_server_callback
#define SSL_set_purpose GRPC_SHADOW_SSL_set_purpose
#define SSL_set_quic_method GRPC_SHADOW_SSL_set_quic_method
#define SSL_set_quic_transport_params GRPC_SHADOW_SSL_set_quic_transport_params
#define SSL_set_quiet_shutdown GRPC_SHADOW_SSL_set_quiet_shutdown
#define SSL_set_read_ahead GRPC_SHADOW_SSL_set_read_ahead
#define SSL_set_renegotiate_mode GRPC_SHADOW_SSL_set_renegotiate_mode
#define SSL_set_retain_only_sha256_of_client_certs GRPC_SHADOW_SSL_set_retain_only_sha256_of_client_certs
#define SSL_set_rfd GRPC_SHADOW_SSL_set_rfd
#define SSL_set_session GRPC_SHADOW_SSL_set_session
#define SSL_set_session_id_context GRPC_SHADOW_SSL_set_session_id_context
#define SSL_set_shed_handshake_config GRPC_SHADOW_SSL_set_shed_handshake_config
#define SSL_set_shutdown GRPC_SHADOW_SSL_set_shutdown
#define SSL_set_signed_cert_timestamp_list GRPC_SHADOW_SSL_set_signed_cert_timestamp_list
#define SSL_set_signing_algorithm_prefs GRPC_SHADOW_SSL_set_signing_algorithm_prefs
#define SSL_set_srtp_profiles GRPC_SHADOW_SSL_set_srtp_profiles
#define SSL_set_state GRPC_SHADOW_SSL_set_state
#define SSL_set_strict_cipher_list GRPC_SHADOW_SSL_set_strict_cipher_list
#define SSL_set_tls_channel_id_enabled GRPC_SHADOW_SSL_set_tls_channel_id_enabled
#define SSL_set_tlsext_host_name GRPC_SHADOW_SSL_set_tlsext_host_name
#define SSL_set_tlsext_status_ocsp_resp GRPC_SHADOW_SSL_set_tlsext_status_ocsp_resp
#define SSL_set_tlsext_status_type GRPC_SHADOW_SSL_set_tlsext_status_type
#define SSL_set_tlsext_use_srtp GRPC_SHADOW_SSL_set_tlsext_use_srtp
#define SSL_set_tmp_dh GRPC_SHADOW_SSL_set_tmp_dh
#define SSL_set_tmp_dh_callback GRPC_SHADOW_SSL_set_tmp_dh_callback
#define SSL_set_tmp_ecdh GRPC_SHADOW_SSL_set_tmp_ecdh
#define SSL_set_tmp_rsa GRPC_SHADOW_SSL_set_tmp_rsa
#define SSL_set_tmp_rsa_callback GRPC_SHADOW_SSL_set_tmp_rsa_callback
#define SSL_set_token_binding_params GRPC_SHADOW_SSL_set_token_binding_params
#define SSL_set_trust GRPC_SHADOW_SSL_set_trust
#define SSL_set_verify GRPC_SHADOW_SSL_set_verify
#define SSL_set_verify_depth GRPC_SHADOW_SSL_set_verify_depth
#define SSL_set_verify_result GRPC_SHADOW_SSL_set_verify_result
#define SSL_set_wfd GRPC_SHADOW_SSL_set_wfd
#define SSL_shutdown GRPC_SHADOW_SSL_shutdown
#define SSL_state GRPC_SHADOW_SSL_state
#define SSL_state_string GRPC_SHADOW_SSL_state_string
#define SSL_state_string_long GRPC_SHADOW_SSL_state_string_long
#define SSL_total_renegotiations GRPC_SHADOW_SSL_total_renegotiations
#define SSL_use_PrivateKey GRPC_SHADOW_SSL_use_PrivateKey
#define SSL_use_PrivateKey_ASN1 GRPC_SHADOW_SSL_use_PrivateKey_ASN1
#define SSL_use_PrivateKey_file GRPC_SHADOW_SSL_use_PrivateKey_file
#define SSL_use_RSAPrivateKey GRPC_SHADOW_SSL_use_RSAPrivateKey
#define SSL_use_RSAPrivateKey_ASN1 GRPC_SHADOW_SSL_use_RSAPrivateKey_ASN1
#define SSL_use_RSAPrivateKey_file GRPC_SHADOW_SSL_use_RSAPrivateKey_file
#define SSL_use_certificate GRPC_SHADOW_SSL_use_certificate
#define SSL_use_certificate_ASN1 GRPC_SHADOW_SSL_use_certificate_ASN1
#define SSL_use_certificate_file GRPC_SHADOW_SSL_use_certificate_file
#define SSL_use_psk_identity_hint GRPC_SHADOW_SSL_use_psk_identity_hint
#define SSL_version GRPC_SHADOW_SSL_version
#define SSL_want GRPC_SHADOW_SSL_want
#define SSL_write GRPC_SHADOW_SSL_write
#define SSLeay GRPC_SHADOW_SSLeay
#define SSLeay_version GRPC_SHADOW_SSLeay_version
#define SSLv23_client_method GRPC_SHADOW_SSLv23_client_method
#define SSLv23_method GRPC_SHADOW_SSLv23_method
#define SSLv23_server_method GRPC_SHADOW_SSLv23_server_method
#define SXNETID_free GRPC_SHADOW_SXNETID_free
#define SXNETID_it GRPC_SHADOW_SXNETID_it
#define SXNETID_new GRPC_SHADOW_SXNETID_new
#define SXNET_add_id_INTEGER GRPC_SHADOW_SXNET_add_id_INTEGER
#define SXNET_add_id_asc GRPC_SHADOW_SXNET_add_id_asc
#define SXNET_add_id_ulong GRPC_SHADOW_SXNET_add_id_ulong
#define SXNET_free GRPC_SHADOW_SXNET_free
#define SXNET_get_id_INTEGER GRPC_SHADOW_SXNET_get_id_INTEGER
#define SXNET_get_id_asc GRPC_SHADOW_SXNET_get_id_asc
#define SXNET_get_id_ulong GRPC_SHADOW_SXNET_get_id_ulong
#define SXNET_it GRPC_SHADOW_SXNET_it
#define SXNET_new GRPC_SHADOW_SXNET_new
#define TLS_client_method GRPC_SHADOW_TLS_client_method
#define TLS_method GRPC_SHADOW_TLS_method
#define TLS_server_method GRPC_SHADOW_TLS_server_method
#define TLS_with_buffers_method GRPC_SHADOW_TLS_with_buffers_method
#define TLSv1_1_client_method GRPC_SHADOW_TLSv1_1_client_method
#define TLSv1_1_method GRPC_SHADOW_TLSv1_1_method
#define TLSv1_1_server_method GRPC_SHADOW_TLSv1_1_server_method
#define TLSv1_2_client_method GRPC_SHADOW_TLSv1_2_client_method
#define TLSv1_2_method GRPC_SHADOW_TLSv1_2_method
#define TLSv1_2_server_method GRPC_SHADOW_TLSv1_2_server_method
#define TLSv1_client_method GRPC_SHADOW_TLSv1_client_method
#define TLSv1_method GRPC_SHADOW_TLSv1_method
#define TLSv1_server_method GRPC_SHADOW_TLSv1_server_method
#define USERNOTICE_free GRPC_SHADOW_USERNOTICE_free
#define USERNOTICE_it GRPC_SHADOW_USERNOTICE_it
#define USERNOTICE_new GRPC_SHADOW_USERNOTICE_new
#define UTF8_getc GRPC_SHADOW_UTF8_getc
#define UTF8_putc GRPC_SHADOW_UTF8_putc
#define X25519 GRPC_SHADOW_X25519
#define X25519_keypair GRPC_SHADOW_X25519_keypair
#define X25519_public_from_private GRPC_SHADOW_X25519_public_from_private
#define X509V3_EXT_CRL_add_nconf GRPC_SHADOW_X509V3_EXT_CRL_add_nconf
#define X509V3_EXT_REQ_add_nconf GRPC_SHADOW_X509V3_EXT_REQ_add_nconf
#define X509V3_EXT_add GRPC_SHADOW_X509V3_EXT_add
#define X509V3_EXT_add_alias GRPC_SHADOW_X509V3_EXT_add_alias
#define X509V3_EXT_add_list GRPC_SHADOW_X509V3_EXT_add_list
#define X509V3_EXT_add_nconf GRPC_SHADOW_X509V3_EXT_add_nconf
#define X509V3_EXT_add_nconf_sk GRPC_SHADOW_X509V3_EXT_add_nconf_sk
#define X509V3_EXT_cleanup GRPC_SHADOW_X509V3_EXT_cleanup
#define X509V3_EXT_d2i GRPC_SHADOW_X509V3_EXT_d2i
#define X509V3_EXT_free GRPC_SHADOW_X509V3_EXT_free
#define X509V3_EXT_get GRPC_SHADOW_X509V3_EXT_get
#define X509V3_EXT_get_nid GRPC_SHADOW_X509V3_EXT_get_nid
#define X509V3_EXT_i2d GRPC_SHADOW_X509V3_EXT_i2d
#define X509V3_EXT_nconf GRPC_SHADOW_X509V3_EXT_nconf
#define X509V3_EXT_nconf_nid GRPC_SHADOW_X509V3_EXT_nconf_nid
#define X509V3_EXT_print GRPC_SHADOW_X509V3_EXT_print
#define X509V3_EXT_print_fp GRPC_SHADOW_X509V3_EXT_print_fp
#define X509V3_EXT_val_prn GRPC_SHADOW_X509V3_EXT_val_prn
#define X509V3_NAME_from_section GRPC_SHADOW_X509V3_NAME_from_section
#define X509V3_add1_i2d GRPC_SHADOW_X509V3_add1_i2d
#define X509V3_add_standard_extensions GRPC_SHADOW_X509V3_add_standard_extensions
#define X509V3_add_value GRPC_SHADOW_X509V3_add_value
#define X509V3_add_value_bool GRPC_SHADOW_X509V3_add_value_bool
#define X509V3_add_value_bool_nf GRPC_SHADOW_X509V3_add_value_bool_nf
#define X509V3_add_value_int GRPC_SHADOW_X509V3_add_value_int
#define X509V3_add_value_uchar GRPC_SHADOW_X509V3_add_value_uchar
#define X509V3_conf_free GRPC_SHADOW_X509V3_conf_free
#define X509V3_extensions_print GRPC_SHADOW_X509V3_extensions_print
#define X509V3_get_d2i GRPC_SHADOW_X509V3_get_d2i
#define X509V3_get_section GRPC_SHADOW_X509V3_get_section
#define X509V3_get_string GRPC_SHADOW_X509V3_get_string
#define X509V3_get_value_bool GRPC_SHADOW_X509V3_get_value_bool
#define X509V3_get_value_int GRPC_SHADOW_X509V3_get_value_int
#define X509V3_parse_list GRPC_SHADOW_X509V3_parse_list
#define X509V3_section_free GRPC_SHADOW_X509V3_section_free
#define X509V3_set_ctx GRPC_SHADOW_X509V3_set_ctx
#define X509V3_set_nconf GRPC_SHADOW_X509V3_set_nconf
#define X509V3_string_free GRPC_SHADOW_X509V3_string_free
#define X509_ALGORS_it GRPC_SHADOW_X509_ALGORS_it
#define X509_ALGOR_cmp GRPC_SHADOW_X509_ALGOR_cmp
#define X509_ALGOR_dup GRPC_SHADOW_X509_ALGOR_dup
#define X509_ALGOR_free GRPC_SHADOW_X509_ALGOR_free
#define X509_ALGOR_get0 GRPC_SHADOW_X509_ALGOR_get0
#define X509_ALGOR_it GRPC_SHADOW_X509_ALGOR_it
#define X509_ALGOR_new GRPC_SHADOW_X509_ALGOR_new
#define X509_ALGOR_set0 GRPC_SHADOW_X509_ALGOR_set0
#define X509_ALGOR_set_md GRPC_SHADOW_X509_ALGOR_set_md
#define X509_ATTRIBUTE_SET_it GRPC_SHADOW_X509_ATTRIBUTE_SET_it
#define X509_ATTRIBUTE_count GRPC_SHADOW_X509_ATTRIBUTE_count
#define X509_ATTRIBUTE_create GRPC_SHADOW_X509_ATTRIBUTE_create
#define X509_ATTRIBUTE_create_by_NID GRPC_SHADOW_X509_ATTRIBUTE_create_by_NID
#define X509_ATTRIBUTE_create_by_OBJ GRPC_SHADOW_X509_ATTRIBUTE_create_by_OBJ
#define X509_ATTRIBUTE_create_by_txt GRPC_SHADOW_X509_ATTRIBUTE_create_by_txt
#define X509_ATTRIBUTE_dup GRPC_SHADOW_X509_ATTRIBUTE_dup
#define X509_ATTRIBUTE_free GRPC_SHADOW_X509_ATTRIBUTE_free
#define X509_ATTRIBUTE_get0_data GRPC_SHADOW_X509_ATTRIBUTE_get0_data
#define X509_ATTRIBUTE_get0_object GRPC_SHADOW_X509_ATTRIBUTE_get0_object
#define X509_ATTRIBUTE_get0_type GRPC_SHADOW_X509_ATTRIBUTE_get0_type
#define X509_ATTRIBUTE_it GRPC_SHADOW_X509_ATTRIBUTE_it
#define X509_ATTRIBUTE_new GRPC_SHADOW_X509_ATTRIBUTE_new
#define X509_ATTRIBUTE_set1_data GRPC_SHADOW_X509_ATTRIBUTE_set1_data
#define X509_ATTRIBUTE_set1_object GRPC_SHADOW_X509_ATTRIBUTE_set1_object
#define X509_CERT_AUX_free GRPC_SHADOW_X509_CERT_AUX_free
#define X509_CERT_AUX_it GRPC_SHADOW_X509_CERT_AUX_it
#define X509_CERT_AUX_new GRPC_SHADOW_X509_CERT_AUX_new
#define X509_CERT_AUX_print GRPC_SHADOW_X509_CERT_AUX_print
#define X509_CINF_free GRPC_SHADOW_X509_CINF_free
#define X509_CINF_it GRPC_SHADOW_X509_CINF_it
#define X509_CINF_new GRPC_SHADOW_X509_CINF_new
#define X509_CRL_INFO_free GRPC_SHADOW_X509_CRL_INFO_free
#define X509_CRL_INFO_it GRPC_SHADOW_X509_CRL_INFO_it
#define X509_CRL_INFO_new GRPC_SHADOW_X509_CRL_INFO_new
#define X509_CRL_METHOD_free GRPC_SHADOW_X509_CRL_METHOD_free
#define X509_CRL_METHOD_new GRPC_SHADOW_X509_CRL_METHOD_new
#define X509_CRL_add0_revoked GRPC_SHADOW_X509_CRL_add0_revoked
#define X509_CRL_add1_ext_i2d GRPC_SHADOW_X509_CRL_add1_ext_i2d
#define X509_CRL_add_ext GRPC_SHADOW_X509_CRL_add_ext
#define X509_CRL_check_suiteb GRPC_SHADOW_X509_CRL_check_suiteb
#define X509_CRL_cmp GRPC_SHADOW_X509_CRL_cmp
#define X509_CRL_delete_ext GRPC_SHADOW_X509_CRL_delete_ext
#define X509_CRL_diff GRPC_SHADOW_X509_CRL_diff
#define X509_CRL_digest GRPC_SHADOW_X509_CRL_digest
#define X509_CRL_dup GRPC_SHADOW_X509_CRL_dup
#define X509_CRL_free GRPC_SHADOW_X509_CRL_free
#define X509_CRL_get0_by_cert GRPC_SHADOW_X509_CRL_get0_by_cert
#define X509_CRL_get0_by_serial GRPC_SHADOW_X509_CRL_get0_by_serial
#define X509_CRL_get0_lastUpdate GRPC_SHADOW_X509_CRL_get0_lastUpdate
#define X509_CRL_get0_nextUpdate GRPC_SHADOW_X509_CRL_get0_nextUpdate
#define X509_CRL_get0_signature GRPC_SHADOW_X509_CRL_get0_signature
#define X509_CRL_get_ext GRPC_SHADOW_X509_CRL_get_ext
#define X509_CRL_get_ext_by_NID GRPC_SHADOW_X509_CRL_get_ext_by_NID
#define X509_CRL_get_ext_by_OBJ GRPC_SHADOW_X509_CRL_get_ext_by_OBJ
#define X509_CRL_get_ext_by_critical GRPC_SHADOW_X509_CRL_get_ext_by_critical
#define X509_CRL_get_ext_count GRPC_SHADOW_X509_CRL_get_ext_count
#define X509_CRL_get_ext_d2i GRPC_SHADOW_X509_CRL_get_ext_d2i
#define X509_CRL_get_meth_data GRPC_SHADOW_X509_CRL_get_meth_data
#define X509_CRL_get_signature_nid GRPC_SHADOW_X509_CRL_get_signature_nid
#define X509_CRL_it GRPC_SHADOW_X509_CRL_it
#define X509_CRL_match GRPC_SHADOW_X509_CRL_match
#define X509_CRL_new GRPC_SHADOW_X509_CRL_new
#define X509_CRL_print GRPC_SHADOW_X509_CRL_print
#define X509_CRL_print_fp GRPC_SHADOW_X509_CRL_print_fp
#define X509_CRL_set_default_method GRPC_SHADOW_X509_CRL_set_default_method
#define X509_CRL_set_issuer_name GRPC_SHADOW_X509_CRL_set_issuer_name
#define X509_CRL_set_lastUpdate GRPC_SHADOW_X509_CRL_set_lastUpdate
#define X509_CRL_set_meth_data GRPC_SHADOW_X509_CRL_set_meth_data
#define X509_CRL_set_nextUpdate GRPC_SHADOW_X509_CRL_set_nextUpdate
#define X509_CRL_set_version GRPC_SHADOW_X509_CRL_set_version
#define X509_CRL_sign GRPC_SHADOW_X509_CRL_sign
#define X509_CRL_sign_ctx GRPC_SHADOW_X509_CRL_sign_ctx
#define X509_CRL_sort GRPC_SHADOW_X509_CRL_sort
#define X509_CRL_up_ref GRPC_SHADOW_X509_CRL_up_ref
#define X509_CRL_verify GRPC_SHADOW_X509_CRL_verify
#define X509_EXTENSIONS_it GRPC_SHADOW_X509_EXTENSIONS_it
#define X509_EXTENSION_create_by_NID GRPC_SHADOW_X509_EXTENSION_create_by_NID
#define X509_EXTENSION_create_by_OBJ GRPC_SHADOW_X509_EXTENSION_create_by_OBJ
#define X509_EXTENSION_dup GRPC_SHADOW_X509_EXTENSION_dup
#define X509_EXTENSION_free GRPC_SHADOW_X509_EXTENSION_free
#define X509_EXTENSION_get_critical GRPC_SHADOW_X509_EXTENSION_get_critical
#define X509_EXTENSION_get_data GRPC_SHADOW_X509_EXTENSION_get_data
#define X509_EXTENSION_get_object GRPC_SHADOW_X509_EXTENSION_get_object
#define X509_EXTENSION_it GRPC_SHADOW_X509_EXTENSION_it
#define X509_EXTENSION_new GRPC_SHADOW_X509_EXTENSION_new
#define X509_EXTENSION_set_critical GRPC_SHADOW_X509_EXTENSION_set_critical
#define X509_EXTENSION_set_data GRPC_SHADOW_X509_EXTENSION_set_data
#define X509_EXTENSION_set_object GRPC_SHADOW_X509_EXTENSION_set_object
#define X509_INFO_free GRPC_SHADOW_X509_INFO_free
#define X509_INFO_new GRPC_SHADOW_X509_INFO_new
#define X509_LOOKUP_by_alias GRPC_SHADOW_X509_LOOKUP_by_alias
#define X509_LOOKUP_by_fingerprint GRPC_SHADOW_X509_LOOKUP_by_fingerprint
#define X509_LOOKUP_by_issuer_serial GRPC_SHADOW_X509_LOOKUP_by_issuer_serial
#define X509_LOOKUP_by_subject GRPC_SHADOW_X509_LOOKUP_by_subject
#define X509_LOOKUP_ctrl GRPC_SHADOW_X509_LOOKUP_ctrl
#define X509_LOOKUP_file GRPC_SHADOW_X509_LOOKUP_file
#define X509_LOOKUP_free GRPC_SHADOW_X509_LOOKUP_free
#define X509_LOOKUP_hash_dir GRPC_SHADOW_X509_LOOKUP_hash_dir
#define X509_LOOKUP_init GRPC_SHADOW_X509_LOOKUP_init
#define X509_LOOKUP_new GRPC_SHADOW_X509_LOOKUP_new
#define X509_LOOKUP_shutdown GRPC_SHADOW_X509_LOOKUP_shutdown
#define X509_NAME_ENTRIES_it GRPC_SHADOW_X509_NAME_ENTRIES_it
#define X509_NAME_ENTRY_create_by_NID GRPC_SHADOW_X509_NAME_ENTRY_create_by_NID
#define X509_NAME_ENTRY_create_by_OBJ GRPC_SHADOW_X509_NAME_ENTRY_create_by_OBJ
#define X509_NAME_ENTRY_create_by_txt GRPC_SHADOW_X509_NAME_ENTRY_create_by_txt
#define X509_NAME_ENTRY_dup GRPC_SHADOW_X509_NAME_ENTRY_dup
#define X509_NAME_ENTRY_free GRPC_SHADOW_X509_NAME_ENTRY_free
#define X509_NAME_ENTRY_get_data GRPC_SHADOW_X509_NAME_ENTRY_get_data
#define X509_NAME_ENTRY_get_object GRPC_SHADOW_X509_NAME_ENTRY_get_object
#define X509_NAME_ENTRY_it GRPC_SHADOW_X509_NAME_ENTRY_it
#define X509_NAME_ENTRY_new GRPC_SHADOW_X509_NAME_ENTRY_new
#define X509_NAME_ENTRY_set GRPC_SHADOW_X509_NAME_ENTRY_set
#define X509_NAME_ENTRY_set_data GRPC_SHADOW_X509_NAME_ENTRY_set_data
#define X509_NAME_ENTRY_set_object GRPC_SHADOW_X509_NAME_ENTRY_set_object
#define X509_NAME_INTERNAL_it GRPC_SHADOW_X509_NAME_INTERNAL_it
#define X509_NAME_add_entry GRPC_SHADOW_X509_NAME_add_entry
#define X509_NAME_add_entry_by_NID GRPC_SHADOW_X509_NAME_add_entry_by_NID
#define X509_NAME_add_entry_by_OBJ GRPC_SHADOW_X509_NAME_add_entry_by_OBJ
#define X509_NAME_add_entry_by_txt GRPC_SHADOW_X509_NAME_add_entry_by_txt
#define X509_NAME_cmp GRPC_SHADOW_X509_NAME_cmp
#define X509_NAME_delete_entry GRPC_SHADOW_X509_NAME_delete_entry
#define X509_NAME_digest GRPC_SHADOW_X509_NAME_digest
#define X509_NAME_dup GRPC_SHADOW_X509_NAME_dup
#define X509_NAME_entry_count GRPC_SHADOW_X509_NAME_entry_count
#define X509_NAME_free GRPC_SHADOW_X509_NAME_free
#define X509_NAME_get0_der GRPC_SHADOW_X509_NAME_get0_der
#define X509_NAME_get_entry GRPC_SHADOW_X509_NAME_get_entry
#define X509_NAME_get_index_by_NID GRPC_SHADOW_X509_NAME_get_index_by_NID
#define X509_NAME_get_index_by_OBJ GRPC_SHADOW_X509_NAME_get_index_by_OBJ
#define X509_NAME_get_text_by_NID GRPC_SHADOW_X509_NAME_get_text_by_NID
#define X509_NAME_get_text_by_OBJ GRPC_SHADOW_X509_NAME_get_text_by_OBJ
#define X509_NAME_hash GRPC_SHADOW_X509_NAME_hash
#define X509_NAME_hash_old GRPC_SHADOW_X509_NAME_hash_old
#define X509_NAME_it GRPC_SHADOW_X509_NAME_it
#define X509_NAME_new GRPC_SHADOW_X509_NAME_new
#define X509_NAME_oneline GRPC_SHADOW_X509_NAME_oneline
#define X509_NAME_print GRPC_SHADOW_X509_NAME_print
#define X509_NAME_print_ex GRPC_SHADOW_X509_NAME_print_ex
#define X509_NAME_print_ex_fp GRPC_SHADOW_X509_NAME_print_ex_fp
#define X509_NAME_set GRPC_SHADOW_X509_NAME_set
#define X509_OBJECT_free_contents GRPC_SHADOW_X509_OBJECT_free_contents
#define X509_OBJECT_get0_X509 GRPC_SHADOW_X509_OBJECT_get0_X509
#define X509_OBJECT_get_type GRPC_SHADOW_X509_OBJECT_get_type
#define X509_OBJECT_idx_by_subject GRPC_SHADOW_X509_OBJECT_idx_by_subject
#define X509_OBJECT_retrieve_by_subject GRPC_SHADOW_X509_OBJECT_retrieve_by_subject
#define X509_OBJECT_retrieve_match GRPC_SHADOW_X509_OBJECT_retrieve_match
#define X509_OBJECT_up_ref_count GRPC_SHADOW_X509_OBJECT_up_ref_count
#define X509_PKEY_free GRPC_SHADOW_X509_PKEY_free
#define X509_PKEY_new GRPC_SHADOW_X509_PKEY_new
#define X509_POLICY_NODE_print GRPC_SHADOW_X509_POLICY_NODE_print
#define X509_PUBKEY_free GRPC_SHADOW_X509_PUBKEY_free
#define X509_PUBKEY_get GRPC_SHADOW_X509_PUBKEY_get
#define X509_PUBKEY_get0_param GRPC_SHADOW_X509_PUBKEY_get0_param
#define X509_PUBKEY_it GRPC_SHADOW_X509_PUBKEY_it
#define X509_PUBKEY_new GRPC_SHADOW_X509_PUBKEY_new
#define X509_PUBKEY_set GRPC_SHADOW_X509_PUBKEY_set
#define X509_PUBKEY_set0_param GRPC_SHADOW_X509_PUBKEY_set0_param
#define X509_PURPOSE_add GRPC_SHADOW_X509_PURPOSE_add
#define X509_PURPOSE_cleanup GRPC_SHADOW_X509_PURPOSE_cleanup
#define X509_PURPOSE_get0 GRPC_SHADOW_X509_PURPOSE_get0
#define X509_PURPOSE_get0_name GRPC_SHADOW_X509_PURPOSE_get0_name
#define X509_PURPOSE_get0_sname GRPC_SHADOW_X509_PURPOSE_get0_sname
#define X509_PURPOSE_get_by_id GRPC_SHADOW_X509_PURPOSE_get_by_id
#define X509_PURPOSE_get_by_sname GRPC_SHADOW_X509_PURPOSE_get_by_sname
#define X509_PURPOSE_get_count GRPC_SHADOW_X509_PURPOSE_get_count
#define X509_PURPOSE_get_id GRPC_SHADOW_X509_PURPOSE_get_id
#define X509_PURPOSE_get_trust GRPC_SHADOW_X509_PURPOSE_get_trust
#define X509_PURPOSE_set GRPC_SHADOW_X509_PURPOSE_set
#define X509_REQ_INFO_free GRPC_SHADOW_X509_REQ_INFO_free
#define X509_REQ_INFO_it GRPC_SHADOW_X509_REQ_INFO_it
#define X509_REQ_INFO_new GRPC_SHADOW_X509_REQ_INFO_new
#define X509_REQ_add1_attr GRPC_SHADOW_X509_REQ_add1_attr
#define X509_REQ_add1_attr_by_NID GRPC_SHADOW_X509_REQ_add1_attr_by_NID
#define X509_REQ_add1_attr_by_OBJ GRPC_SHADOW_X509_REQ_add1_attr_by_OBJ
#define X509_REQ_add1_attr_by_txt GRPC_SHADOW_X509_REQ_add1_attr_by_txt
#define X509_REQ_add_extensions GRPC_SHADOW_X509_REQ_add_extensions
#define X509_REQ_add_extensions_nid GRPC_SHADOW_X509_REQ_add_extensions_nid
#define X509_REQ_check_private_key GRPC_SHADOW_X509_REQ_check_private_key
#define X509_REQ_delete_attr GRPC_SHADOW_X509_REQ_delete_attr
#define X509_REQ_digest GRPC_SHADOW_X509_REQ_digest
#define X509_REQ_dup GRPC_SHADOW_X509_REQ_dup
#define X509_REQ_extension_nid GRPC_SHADOW_X509_REQ_extension_nid
#define X509_REQ_free GRPC_SHADOW_X509_REQ_free
#define X509_REQ_get0_signature GRPC_SHADOW_X509_REQ_get0_signature
#define X509_REQ_get1_email GRPC_SHADOW_X509_REQ_get1_email
#define X509_REQ_get_attr GRPC_SHADOW_X509_REQ_get_attr
#define X509_REQ_get_attr_by_NID GRPC_SHADOW_X509_REQ_get_attr_by_NID
#define X509_REQ_get_attr_by_OBJ GRPC_SHADOW_X509_REQ_get_attr_by_OBJ
#define X509_REQ_get_attr_count GRPC_SHADOW_X509_REQ_get_attr_count
#define X509_REQ_get_extension_nids GRPC_SHADOW_X509_REQ_get_extension_nids
#define X509_REQ_get_extensions GRPC_SHADOW_X509_REQ_get_extensions
#define X509_REQ_get_pubkey GRPC_SHADOW_X509_REQ_get_pubkey
#define X509_REQ_get_signature_nid GRPC_SHADOW_X509_REQ_get_signature_nid
#define X509_REQ_it GRPC_SHADOW_X509_REQ_it
#define X509_REQ_new GRPC_SHADOW_X509_REQ_new
#define X509_REQ_print GRPC_SHADOW_X509_REQ_print
#define X509_REQ_print_ex GRPC_SHADOW_X509_REQ_print_ex
#define X509_REQ_print_fp GRPC_SHADOW_X509_REQ_print_fp
#define X509_REQ_set_extension_nids GRPC_SHADOW_X509_REQ_set_extension_nids
#define X509_REQ_set_pubkey GRPC_SHADOW_X509_REQ_set_pubkey
#define X509_REQ_set_subject_name GRPC_SHADOW_X509_REQ_set_subject_name
#define X509_REQ_set_version GRPC_SHADOW_X509_REQ_set_version
#define X509_REQ_sign GRPC_SHADOW_X509_REQ_sign
#define X509_REQ_sign_ctx GRPC_SHADOW_X509_REQ_sign_ctx
#define X509_REQ_to_X509 GRPC_SHADOW_X509_REQ_to_X509
#define X509_REQ_verify GRPC_SHADOW_X509_REQ_verify
#define X509_REVOKED_add1_ext_i2d GRPC_SHADOW_X509_REVOKED_add1_ext_i2d
#define X509_REVOKED_add_ext GRPC_SHADOW_X509_REVOKED_add_ext
#define X509_REVOKED_delete_ext GRPC_SHADOW_X509_REVOKED_delete_ext
#define X509_REVOKED_dup GRPC_SHADOW_X509_REVOKED_dup
#define X509_REVOKED_free GRPC_SHADOW_X509_REVOKED_free
#define X509_REVOKED_get0_revocationDate GRPC_SHADOW_X509_REVOKED_get0_revocationDate
#define X509_REVOKED_get0_serialNumber GRPC_SHADOW_X509_REVOKED_get0_serialNumber
#define X509_REVOKED_get_ext GRPC_SHADOW_X509_REVOKED_get_ext
#define X509_REVOKED_get_ext_by_NID GRPC_SHADOW_X509_REVOKED_get_ext_by_NID
#define X509_REVOKED_get_ext_by_OBJ GRPC_SHADOW_X509_REVOKED_get_ext_by_OBJ
#define X509_REVOKED_get_ext_by_critical GRPC_SHADOW_X509_REVOKED_get_ext_by_critical
#define X509_REVOKED_get_ext_count GRPC_SHADOW_X509_REVOKED_get_ext_count
#define X509_REVOKED_get_ext_d2i GRPC_SHADOW_X509_REVOKED_get_ext_d2i
#define X509_REVOKED_it GRPC_SHADOW_X509_REVOKED_it
#define X509_REVOKED_new GRPC_SHADOW_X509_REVOKED_new
#define X509_REVOKED_set_revocationDate GRPC_SHADOW_X509_REVOKED_set_revocationDate
#define X509_REVOKED_set_serialNumber GRPC_SHADOW_X509_REVOKED_set_serialNumber
#define X509_SIG_free GRPC_SHADOW_X509_SIG_free
#define X509_SIG_it GRPC_SHADOW_X509_SIG_it
#define X509_SIG_new GRPC_SHADOW_X509_SIG_new
#define X509_STORE_CTX_cleanup GRPC_SHADOW_X509_STORE_CTX_cleanup
#define X509_STORE_CTX_free GRPC_SHADOW_X509_STORE_CTX_free
#define X509_STORE_CTX_get0_cert GRPC_SHADOW_X509_STORE_CTX_get0_cert
#define X509_STORE_CTX_get0_current_crl GRPC_SHADOW_X509_STORE_CTX_get0_current_crl
#define X509_STORE_CTX_get0_current_issuer GRPC_SHADOW_X509_STORE_CTX_get0_current_issuer
#define X509_STORE_CTX_get0_param GRPC_SHADOW_X509_STORE_CTX_get0_param
#define X509_STORE_CTX_get0_parent_ctx GRPC_SHADOW_X509_STORE_CTX_get0_parent_ctx
#define X509_STORE_CTX_get0_policy_tree GRPC_SHADOW_X509_STORE_CTX_get0_policy_tree
#define X509_STORE_CTX_get0_store GRPC_SHADOW_X509_STORE_CTX_get0_store
#define X509_STORE_CTX_get0_untrusted GRPC_SHADOW_X509_STORE_CTX_get0_untrusted
#define X509_STORE_CTX_get1_chain GRPC_SHADOW_X509_STORE_CTX_get1_chain
#define X509_STORE_CTX_get1_issuer GRPC_SHADOW_X509_STORE_CTX_get1_issuer
#define X509_STORE_CTX_get_chain GRPC_SHADOW_X509_STORE_CTX_get_chain
#define X509_STORE_CTX_get_current_cert GRPC_SHADOW_X509_STORE_CTX_get_current_cert
#define X509_STORE_CTX_get_error GRPC_SHADOW_X509_STORE_CTX_get_error
#define X509_STORE_CTX_get_error_depth GRPC_SHADOW_X509_STORE_CTX_get_error_depth
#define X509_STORE_CTX_get_ex_data GRPC_SHADOW_X509_STORE_CTX_get_ex_data
#define X509_STORE_CTX_get_ex_new_index GRPC_SHADOW_X509_STORE_CTX_get_ex_new_index
#define X509_STORE_CTX_get_explicit_policy GRPC_SHADOW_X509_STORE_CTX_get_explicit_policy
#define X509_STORE_CTX_init GRPC_SHADOW_X509_STORE_CTX_init
#define X509_STORE_CTX_new GRPC_SHADOW_X509_STORE_CTX_new
#define X509_STORE_CTX_purpose_inherit GRPC_SHADOW_X509_STORE_CTX_purpose_inherit
#define X509_STORE_CTX_set0_crls GRPC_SHADOW_X509_STORE_CTX_set0_crls
#define X509_STORE_CTX_set0_param GRPC_SHADOW_X509_STORE_CTX_set0_param
#define X509_STORE_CTX_set_cert GRPC_SHADOW_X509_STORE_CTX_set_cert
#define X509_STORE_CTX_set_chain GRPC_SHADOW_X509_STORE_CTX_set_chain
#define X509_STORE_CTX_set_default GRPC_SHADOW_X509_STORE_CTX_set_default
#define X509_STORE_CTX_set_depth GRPC_SHADOW_X509_STORE_CTX_set_depth
#define X509_STORE_CTX_set_error GRPC_SHADOW_X509_STORE_CTX_set_error
#define X509_STORE_CTX_set_ex_data GRPC_SHADOW_X509_STORE_CTX_set_ex_data
#define X509_STORE_CTX_set_flags GRPC_SHADOW_X509_STORE_CTX_set_flags
#define X509_STORE_CTX_set_purpose GRPC_SHADOW_X509_STORE_CTX_set_purpose
#define X509_STORE_CTX_set_time GRPC_SHADOW_X509_STORE_CTX_set_time
#define X509_STORE_CTX_set_trust GRPC_SHADOW_X509_STORE_CTX_set_trust
#define X509_STORE_CTX_set_verify_cb GRPC_SHADOW_X509_STORE_CTX_set_verify_cb
#define X509_STORE_CTX_trusted_stack GRPC_SHADOW_X509_STORE_CTX_trusted_stack
#define X509_STORE_CTX_zero GRPC_SHADOW_X509_STORE_CTX_zero
#define X509_STORE_add_cert GRPC_SHADOW_X509_STORE_add_cert
#define X509_STORE_add_crl GRPC_SHADOW_X509_STORE_add_crl
#define X509_STORE_add_lookup GRPC_SHADOW_X509_STORE_add_lookup
#define X509_STORE_free GRPC_SHADOW_X509_STORE_free
#define X509_STORE_get0_objects GRPC_SHADOW_X509_STORE_get0_objects
#define X509_STORE_get0_param GRPC_SHADOW_X509_STORE_get0_param
#define X509_STORE_get1_certs GRPC_SHADOW_X509_STORE_get1_certs
#define X509_STORE_get1_crls GRPC_SHADOW_X509_STORE_get1_crls
#define X509_STORE_get_by_subject GRPC_SHADOW_X509_STORE_get_by_subject
#define X509_STORE_get_cert_crl GRPC_SHADOW_X509_STORE_get_cert_crl
#define X509_STORE_get_check_crl GRPC_SHADOW_X509_STORE_get_check_crl
#define X509_STORE_get_check_issued GRPC_SHADOW_X509_STORE_get_check_issued
#define X509_STORE_get_check_revocation GRPC_SHADOW_X509_STORE_get_check_revocation
#define X509_STORE_get_cleanup GRPC_SHADOW_X509_STORE_get_cleanup
#define X509_STORE_get_get_crl GRPC_SHADOW_X509_STORE_get_get_crl
#define X509_STORE_get_get_issuer GRPC_SHADOW_X509_STORE_get_get_issuer
#define X509_STORE_get_lookup_certs GRPC_SHADOW_X509_STORE_get_lookup_certs
#define X509_STORE_get_lookup_crls GRPC_SHADOW_X509_STORE_get_lookup_crls
#define X509_STORE_get_verify GRPC_SHADOW_X509_STORE_get_verify
#define X509_STORE_get_verify_cb GRPC_SHADOW_X509_STORE_get_verify_cb
#define X509_STORE_load_locations GRPC_SHADOW_X509_STORE_load_locations
#define X509_STORE_new GRPC_SHADOW_X509_STORE_new
#define X509_STORE_set0_additional_untrusted GRPC_SHADOW_X509_STORE_set0_additional_untrusted
#define X509_STORE_set1_param GRPC_SHADOW_X509_STORE_set1_param
#define X509_STORE_set_cert_crl GRPC_SHADOW_X509_STORE_set_cert_crl
#define X509_STORE_set_check_crl GRPC_SHADOW_X509_STORE_set_check_crl
#define X509_STORE_set_check_issued GRPC_SHADOW_X509_STORE_set_check_issued
#define X509_STORE_set_check_revocation GRPC_SHADOW_X509_STORE_set_check_revocation
#define X509_STORE_set_cleanup GRPC_SHADOW_X509_STORE_set_cleanup
#define X509_STORE_set_default_paths GRPC_SHADOW_X509_STORE_set_default_paths
#define X509_STORE_set_depth GRPC_SHADOW_X509_STORE_set_depth
#define X509_STORE_set_flags GRPC_SHADOW_X509_STORE_set_flags
#define X509_STORE_set_get_crl GRPC_SHADOW_X509_STORE_set_get_crl
#define X509_STORE_set_get_issuer GRPC_SHADOW_X509_STORE_set_get_issuer
#define X509_STORE_set_lookup_certs GRPC_SHADOW_X509_STORE_set_lookup_certs
#define X509_STORE_set_lookup_crls GRPC_SHADOW_X509_STORE_set_lookup_crls
#define X509_STORE_set_purpose GRPC_SHADOW_X509_STORE_set_purpose
#define X509_STORE_set_trust GRPC_SHADOW_X509_STORE_set_trust
#define X509_STORE_set_verify GRPC_SHADOW_X509_STORE_set_verify
#define X509_STORE_set_verify_cb GRPC_SHADOW_X509_STORE_set_verify_cb
#define X509_STORE_up_ref GRPC_SHADOW_X509_STORE_up_ref
#define X509_TRUST_add GRPC_SHADOW_X509_TRUST_add
#define X509_TRUST_cleanup GRPC_SHADOW_X509_TRUST_cleanup
#define X509_TRUST_get0 GRPC_SHADOW_X509_TRUST_get0
#define X509_TRUST_get0_name GRPC_SHADOW_X509_TRUST_get0_name
#define X509_TRUST_get_by_id GRPC_SHADOW_X509_TRUST_get_by_id
#define X509_TRUST_get_count GRPC_SHADOW_X509_TRUST_get_count
#define X509_TRUST_get_flags GRPC_SHADOW_X509_TRUST_get_flags
#define X509_TRUST_get_trust GRPC_SHADOW_X509_TRUST_get_trust
#define X509_TRUST_set GRPC_SHADOW_X509_TRUST_set
#define X509_TRUST_set_default GRPC_SHADOW_X509_TRUST_set_default
#define X509_VAL_free GRPC_SHADOW_X509_VAL_free
#define X509_VAL_it GRPC_SHADOW_X509_VAL_it
#define X509_VAL_new GRPC_SHADOW_X509_VAL_new
#define X509_VERIFY_PARAM_add0_policy GRPC_SHADOW_X509_VERIFY_PARAM_add0_policy
#define X509_VERIFY_PARAM_add0_table GRPC_SHADOW_X509_VERIFY_PARAM_add0_table
#define X509_VERIFY_PARAM_add1_host GRPC_SHADOW_X509_VERIFY_PARAM_add1_host
#define X509_VERIFY_PARAM_clear_flags GRPC_SHADOW_X509_VERIFY_PARAM_clear_flags
#define X509_VERIFY_PARAM_free GRPC_SHADOW_X509_VERIFY_PARAM_free
#define X509_VERIFY_PARAM_get0 GRPC_SHADOW_X509_VERIFY_PARAM_get0
#define X509_VERIFY_PARAM_get0_name GRPC_SHADOW_X509_VERIFY_PARAM_get0_name
#define X509_VERIFY_PARAM_get0_peername GRPC_SHADOW_X509_VERIFY_PARAM_get0_peername
#define X509_VERIFY_PARAM_get_count GRPC_SHADOW_X509_VERIFY_PARAM_get_count
#define X509_VERIFY_PARAM_get_depth GRPC_SHADOW_X509_VERIFY_PARAM_get_depth
#define X509_VERIFY_PARAM_get_flags GRPC_SHADOW_X509_VERIFY_PARAM_get_flags
#define X509_VERIFY_PARAM_inherit GRPC_SHADOW_X509_VERIFY_PARAM_inherit
#define X509_VERIFY_PARAM_lookup GRPC_SHADOW_X509_VERIFY_PARAM_lookup
#define X509_VERIFY_PARAM_new GRPC_SHADOW_X509_VERIFY_PARAM_new
#define X509_VERIFY_PARAM_set1 GRPC_SHADOW_X509_VERIFY_PARAM_set1
#define X509_VERIFY_PARAM_set1_email GRPC_SHADOW_X509_VERIFY_PARAM_set1_email
#define X509_VERIFY_PARAM_set1_host GRPC_SHADOW_X509_VERIFY_PARAM_set1_host
#define X509_VERIFY_PARAM_set1_ip GRPC_SHADOW_X509_VERIFY_PARAM_set1_ip
#define X509_VERIFY_PARAM_set1_ip_asc GRPC_SHADOW_X509_VERIFY_PARAM_set1_ip_asc
#define X509_VERIFY_PARAM_set1_name GRPC_SHADOW_X509_VERIFY_PARAM_set1_name
#define X509_VERIFY_PARAM_set1_policies GRPC_SHADOW_X509_VERIFY_PARAM_set1_policies
#define X509_VERIFY_PARAM_set_depth GRPC_SHADOW_X509_VERIFY_PARAM_set_depth
#define X509_VERIFY_PARAM_set_flags GRPC_SHADOW_X509_VERIFY_PARAM_set_flags
#define X509_VERIFY_PARAM_set_hostflags GRPC_SHADOW_X509_VERIFY_PARAM_set_hostflags
#define X509_VERIFY_PARAM_set_purpose GRPC_SHADOW_X509_VERIFY_PARAM_set_purpose
#define X509_VERIFY_PARAM_set_time GRPC_SHADOW_X509_VERIFY_PARAM_set_time
#define X509_VERIFY_PARAM_set_trust GRPC_SHADOW_X509_VERIFY_PARAM_set_trust
#define X509_VERIFY_PARAM_table_cleanup GRPC_SHADOW_X509_VERIFY_PARAM_table_cleanup
#define X509_add1_ext_i2d GRPC_SHADOW_X509_add1_ext_i2d
#define X509_add1_reject_object GRPC_SHADOW_X509_add1_reject_object
#define X509_add1_trust_object GRPC_SHADOW_X509_add1_trust_object
#define X509_add_ext GRPC_SHADOW_X509_add_ext
#define X509_alias_get0 GRPC_SHADOW_X509_alias_get0
#define X509_alias_set1 GRPC_SHADOW_X509_alias_set1
#define X509_chain_check_suiteb GRPC_SHADOW_X509_chain_check_suiteb
#define X509_chain_up_ref GRPC_SHADOW_X509_chain_up_ref
#define X509_check_akid GRPC_SHADOW_X509_check_akid
#define X509_check_ca GRPC_SHADOW_X509_check_ca
#define X509_check_email GRPC_SHADOW_X509_check_email
#define X509_check_host GRPC_SHADOW_X509_check_host
#define X509_check_ip GRPC_SHADOW_X509_check_ip
#define X509_check_ip_asc GRPC_SHADOW_X509_check_ip_asc
#define X509_check_issued GRPC_SHADOW_X509_check_issued
#define X509_check_private_key GRPC_SHADOW_X509_check_private_key
#define X509_check_purpose GRPC_SHADOW_X509_check_purpose
#define X509_check_trust GRPC_SHADOW_X509_check_trust
#define X509_cmp GRPC_SHADOW_X509_cmp
#define X509_cmp_current_time GRPC_SHADOW_X509_cmp_current_time
#define X509_cmp_time GRPC_SHADOW_X509_cmp_time
#define X509_delete_ext GRPC_SHADOW_X509_delete_ext
#define X509_digest GRPC_SHADOW_X509_digest
#define X509_dup GRPC_SHADOW_X509_dup
#define X509_email_free GRPC_SHADOW_X509_email_free
#define X509_find_by_issuer_and_serial GRPC_SHADOW_X509_find_by_issuer_and_serial
#define X509_find_by_subject GRPC_SHADOW_X509_find_by_subject
#define X509_free GRPC_SHADOW_X509_free
#define X509_get0_extensions GRPC_SHADOW_X509_get0_extensions
#define X509_get0_notAfter GRPC_SHADOW_X509_get0_notAfter
#define X509_get0_notBefore GRPC_SHADOW_X509_get0_notBefore
#define X509_get0_pubkey_bitstr GRPC_SHADOW_X509_get0_pubkey_bitstr
#define X509_get0_signature GRPC_SHADOW_X509_get0_signature
#define X509_get0_tbs_sigalg GRPC_SHADOW_X509_get0_tbs_sigalg
#define X509_get1_email GRPC_SHADOW_X509_get1_email
#define X509_get1_ocsp GRPC_SHADOW_X509_get1_ocsp
#define X509_get_default_cert_area GRPC_SHADOW_X509_get_default_cert_area
#define X509_get_default_cert_dir GRPC_SHADOW_X509_get_default_cert_dir
#define X509_get_default_cert_dir_env GRPC_SHADOW_X509_get_default_cert_dir_env
#define X509_get_default_cert_file GRPC_SHADOW_X509_get_default_cert_file
#define X509_get_default_cert_file_env GRPC_SHADOW_X509_get_default_cert_file_env
#define X509_get_default_private_dir GRPC_SHADOW_X509_get_default_private_dir
#define X509_get_ex_data GRPC_SHADOW_X509_get_ex_data
#define X509_get_ex_new_index GRPC_SHADOW_X509_get_ex_new_index
#define X509_get_ext GRPC_SHADOW_X509_get_ext
#define X509_get_ext_by_NID GRPC_SHADOW_X509_get_ext_by_NID
#define X509_get_ext_by_OBJ GRPC_SHADOW_X509_get_ext_by_OBJ
#define X509_get_ext_by_critical GRPC_SHADOW_X509_get_ext_by_critical
#define X509_get_ext_count GRPC_SHADOW_X509_get_ext_count
#define X509_get_ext_d2i GRPC_SHADOW_X509_get_ext_d2i
#define X509_get_extended_key_usage GRPC_SHADOW_X509_get_extended_key_usage
#define X509_get_extension_flags GRPC_SHADOW_X509_get_extension_flags
#define X509_get_issuer_name GRPC_SHADOW_X509_get_issuer_name
#define X509_get_key_usage GRPC_SHADOW_X509_get_key_usage
#define X509_get_pubkey GRPC_SHADOW_X509_get_pubkey
#define X509_get_serialNumber GRPC_SHADOW_X509_get_serialNumber
#define X509_get_signature_nid GRPC_SHADOW_X509_get_signature_nid
#define X509_get_subject_name GRPC_SHADOW_X509_get_subject_name
#define X509_gmtime_adj GRPC_SHADOW_X509_gmtime_adj
#define X509_issuer_and_serial_cmp GRPC_SHADOW_X509_issuer_and_serial_cmp
#define X509_issuer_and_serial_hash GRPC_SHADOW_X509_issuer_and_serial_hash
#define X509_issuer_name_cmp GRPC_SHADOW_X509_issuer_name_cmp
#define X509_issuer_name_hash GRPC_SHADOW_X509_issuer_name_hash
#define X509_issuer_name_hash_old GRPC_SHADOW_X509_issuer_name_hash_old
#define X509_it GRPC_SHADOW_X509_it
#define X509_keyid_get0 GRPC_SHADOW_X509_keyid_get0
#define X509_keyid_set1 GRPC_SHADOW_X509_keyid_set1
#define X509_load_cert_crl_file GRPC_SHADOW_X509_load_cert_crl_file
#define X509_load_cert_file GRPC_SHADOW_X509_load_cert_file
#define X509_load_crl_file GRPC_SHADOW_X509_load_crl_file
#define X509_new GRPC_SHADOW_X509_new
#define X509_ocspid_print GRPC_SHADOW_X509_ocspid_print
#define X509_parse_from_buffer GRPC_SHADOW_X509_parse_from_buffer
#define X509_policy_check GRPC_SHADOW_X509_policy_check
#define X509_policy_level_get0_node GRPC_SHADOW_X509_policy_level_get0_node
#define X509_policy_level_node_count GRPC_SHADOW_X509_policy_level_node_count
#define X509_policy_node_get0_parent GRPC_SHADOW_X509_policy_node_get0_parent
#define X509_policy_node_get0_policy GRPC_SHADOW_X509_policy_node_get0_policy
#define X509_policy_node_get0_qualifiers GRPC_SHADOW_X509_policy_node_get0_qualifiers
#define X509_policy_tree_free GRPC_SHADOW_X509_policy_tree_free
#define X509_policy_tree_get0_level GRPC_SHADOW_X509_policy_tree_get0_level
#define X509_policy_tree_get0_policies GRPC_SHADOW_X509_policy_tree_get0_policies
#define X509_policy_tree_get0_user_policies GRPC_SHADOW_X509_policy_tree_get0_user_policies
#define X509_policy_tree_level_count GRPC_SHADOW_X509_policy_tree_level_count
#define X509_print GRPC_SHADOW_X509_print
#define X509_print_ex GRPC_SHADOW_X509_print_ex
#define X509_print_ex_fp GRPC_SHADOW_X509_print_ex_fp
#define X509_print_fp GRPC_SHADOW_X509_print_fp
#define X509_pubkey_digest GRPC_SHADOW_X509_pubkey_digest
#define X509_reject_clear GRPC_SHADOW_X509_reject_clear
#define X509_set_ex_data GRPC_SHADOW_X509_set_ex_data
#define X509_set_issuer_name GRPC_SHADOW_X509_set_issuer_name
#define X509_set_notAfter GRPC_SHADOW_X509_set_notAfter
#define X509_set_notBefore GRPC_SHADOW_X509_set_notBefore
#define X509_set_pubkey GRPC_SHADOW_X509_set_pubkey
#define X509_set_serialNumber GRPC_SHADOW_X509_set_serialNumber
#define X509_set_subject_name GRPC_SHADOW_X509_set_subject_name
#define X509_set_version GRPC_SHADOW_X509_set_version
#define X509_sign GRPC_SHADOW_X509_sign
#define X509_sign_ctx GRPC_SHADOW_X509_sign_ctx
#define X509_signature_dump GRPC_SHADOW_X509_signature_dump
#define X509_signature_print GRPC_SHADOW_X509_signature_print
#define X509_subject_name_cmp GRPC_SHADOW_X509_subject_name_cmp
#define X509_subject_name_hash GRPC_SHADOW_X509_subject_name_hash
#define X509_subject_name_hash_old GRPC_SHADOW_X509_subject_name_hash_old
#define X509_supported_extension GRPC_SHADOW_X509_supported_extension
#define X509_time_adj GRPC_SHADOW_X509_time_adj
#define X509_time_adj_ex GRPC_SHADOW_X509_time_adj_ex
#define X509_to_X509_REQ GRPC_SHADOW_X509_to_X509_REQ
#define X509_trust_clear GRPC_SHADOW_X509_trust_clear
#define X509_up_ref GRPC_SHADOW_X509_up_ref
#define X509_verify GRPC_SHADOW_X509_verify
#define X509_verify_cert GRPC_SHADOW_X509_verify_cert
#define X509_verify_cert_error_string GRPC_SHADOW_X509_verify_cert_error_string
#define X509at_add1_attr GRPC_SHADOW_X509at_add1_attr
#define X509at_add1_attr_by_NID GRPC_SHADOW_X509at_add1_attr_by_NID
#define X509at_add1_attr_by_OBJ GRPC_SHADOW_X509at_add1_attr_by_OBJ
#define X509at_add1_attr_by_txt GRPC_SHADOW_X509at_add1_attr_by_txt
#define X509at_delete_attr GRPC_SHADOW_X509at_delete_attr
#define X509at_get0_data_by_OBJ GRPC_SHADOW_X509at_get0_data_by_OBJ
#define X509at_get_attr GRPC_SHADOW_X509at_get_attr
#define X509at_get_attr_by_NID GRPC_SHADOW_X509at_get_attr_by_NID
#define X509at_get_attr_by_OBJ GRPC_SHADOW_X509at_get_attr_by_OBJ
#define X509at_get_attr_count GRPC_SHADOW_X509at_get_attr_count
#define X509v3_add_ext GRPC_SHADOW_X509v3_add_ext
#define X509v3_delete_ext GRPC_SHADOW_X509v3_delete_ext
#define X509v3_get_ext GRPC_SHADOW_X509v3_get_ext
#define X509v3_get_ext_by_NID GRPC_SHADOW_X509v3_get_ext_by_NID
#define X509v3_get_ext_by_OBJ GRPC_SHADOW_X509v3_get_ext_by_OBJ
#define X509v3_get_ext_by_critical GRPC_SHADOW_X509v3_get_ext_by_critical
#define X509v3_get_ext_count GRPC_SHADOW_X509v3_get_ext_count
#define a2i_GENERAL_NAME GRPC_SHADOW_a2i_GENERAL_NAME
#define a2i_IPADDRESS GRPC_SHADOW_a2i_IPADDRESS
#define a2i_IPADDRESS_NC GRPC_SHADOW_a2i_IPADDRESS_NC
#define a2i_ipadd GRPC_SHADOW_a2i_ipadd
#define abi_test_bad_unwind_temporary GRPC_SHADOW_abi_test_bad_unwind_temporary
#define abi_test_bad_unwind_wrong_register GRPC_SHADOW_abi_test_bad_unwind_wrong_register
#define abi_test_clobber_r10 GRPC_SHADOW_abi_test_clobber_r10
#define abi_test_clobber_r11 GRPC_SHADOW_abi_test_clobber_r11
#define abi_test_clobber_r12 GRPC_SHADOW_abi_test_clobber_r12
#define abi_test_clobber_r13 GRPC_SHADOW_abi_test_clobber_r13
#define abi_test_clobber_r14 GRPC_SHADOW_abi_test_clobber_r14
#define abi_test_clobber_r15 GRPC_SHADOW_abi_test_clobber_r15
#define abi_test_clobber_r8 GRPC_SHADOW_abi_test_clobber_r8
#define abi_test_clobber_r9 GRPC_SHADOW_abi_test_clobber_r9
#define abi_test_clobber_rax GRPC_SHADOW_abi_test_clobber_rax
#define abi_test_clobber_rbp GRPC_SHADOW_abi_test_clobber_rbp
#define abi_test_clobber_rbx GRPC_SHADOW_abi_test_clobber_rbx
#define abi_test_clobber_rcx GRPC_SHADOW_abi_test_clobber_rcx
#define abi_test_clobber_rdi GRPC_SHADOW_abi_test_clobber_rdi
#define abi_test_clobber_rdx GRPC_SHADOW_abi_test_clobber_rdx
#define abi_test_clobber_rsi GRPC_SHADOW_abi_test_clobber_rsi
#define abi_test_clobber_xmm0 GRPC_SHADOW_abi_test_clobber_xmm0
#define abi_test_clobber_xmm1 GRPC_SHADOW_abi_test_clobber_xmm1
#define abi_test_clobber_xmm10 GRPC_SHADOW_abi_test_clobber_xmm10
#define abi_test_clobber_xmm11 GRPC_SHADOW_abi_test_clobber_xmm11
#define abi_test_clobber_xmm12 GRPC_SHADOW_abi_test_clobber_xmm12
#define abi_test_clobber_xmm13 GRPC_SHADOW_abi_test_clobber_xmm13
#define abi_test_clobber_xmm14 GRPC_SHADOW_abi_test_clobber_xmm14
#define abi_test_clobber_xmm15 GRPC_SHADOW_abi_test_clobber_xmm15
#define abi_test_clobber_xmm2 GRPC_SHADOW_abi_test_clobber_xmm2
#define abi_test_clobber_xmm3 GRPC_SHADOW_abi_test_clobber_xmm3
#define abi_test_clobber_xmm4 GRPC_SHADOW_abi_test_clobber_xmm4
#define abi_test_clobber_xmm5 GRPC_SHADOW_abi_test_clobber_xmm5
#define abi_test_clobber_xmm6 GRPC_SHADOW_abi_test_clobber_xmm6
#define abi_test_clobber_xmm7 GRPC_SHADOW_abi_test_clobber_xmm7
#define abi_test_clobber_xmm8 GRPC_SHADOW_abi_test_clobber_xmm8
#define abi_test_clobber_xmm9 GRPC_SHADOW_abi_test_clobber_xmm9
#define abi_test_get_and_clear_direction_flag GRPC_SHADOW_abi_test_get_and_clear_direction_flag
#define abi_test_set_direction_flag GRPC_SHADOW_abi_test_set_direction_flag
#define abi_test_trampoline GRPC_SHADOW_abi_test_trampoline
#define abi_test_unwind_return GRPC_SHADOW_abi_test_unwind_return
#define abi_test_unwind_start GRPC_SHADOW_abi_test_unwind_start
#define abi_test_unwind_stop GRPC_SHADOW_abi_test_unwind_stop
#define aes128gcmsiv_aes_ks GRPC_SHADOW_aes128gcmsiv_aes_ks
#define aes128gcmsiv_aes_ks_enc_x1 GRPC_SHADOW_aes128gcmsiv_aes_ks_enc_x1
#define aes128gcmsiv_dec GRPC_SHADOW_aes128gcmsiv_dec
#define aes128gcmsiv_ecb_enc_block GRPC_SHADOW_aes128gcmsiv_ecb_enc_block
#define aes128gcmsiv_enc_msg_x4 GRPC_SHADOW_aes128gcmsiv_enc_msg_x4
#define aes128gcmsiv_enc_msg_x8 GRPC_SHADOW_aes128gcmsiv_enc_msg_x8
#define aes128gcmsiv_kdf GRPC_SHADOW_aes128gcmsiv_kdf
#define aes256gcmsiv_aes_ks GRPC_SHADOW_aes256gcmsiv_aes_ks
#define aes256gcmsiv_aes_ks_enc_x1 GRPC_SHADOW_aes256gcmsiv_aes_ks_enc_x1
#define aes256gcmsiv_dec GRPC_SHADOW_aes256gcmsiv_dec
#define aes256gcmsiv_ecb_enc_block GRPC_SHADOW_aes256gcmsiv_ecb_enc_block
#define aes256gcmsiv_enc_msg_x4 GRPC_SHADOW_aes256gcmsiv_enc_msg_x4
#define aes256gcmsiv_enc_msg_x8 GRPC_SHADOW_aes256gcmsiv_enc_msg_x8
#define aes256gcmsiv_kdf GRPC_SHADOW_aes256gcmsiv_kdf
#define aes_ctr_set_key GRPC_SHADOW_aes_ctr_set_key
#define aes_hw_cbc_encrypt GRPC_SHADOW_aes_hw_cbc_encrypt
#define aes_hw_ctr32_encrypt_blocks GRPC_SHADOW_aes_hw_ctr32_encrypt_blocks
#define aes_hw_decrypt GRPC_SHADOW_aes_hw_decrypt
#define aes_hw_ecb_encrypt GRPC_SHADOW_aes_hw_ecb_encrypt
#define aes_hw_encrypt GRPC_SHADOW_aes_hw_encrypt
#define aes_hw_set_decrypt_key GRPC_SHADOW_aes_hw_set_decrypt_key
#define aes_hw_set_encrypt_key GRPC_SHADOW_aes_hw_set_encrypt_key
#define aes_nohw_cbc_encrypt GRPC_SHADOW_aes_nohw_cbc_encrypt
#define aes_nohw_decrypt GRPC_SHADOW_aes_nohw_decrypt
#define aes_nohw_encrypt GRPC_SHADOW_aes_nohw_encrypt
#define aes_nohw_set_decrypt_key GRPC_SHADOW_aes_nohw_set_decrypt_key
#define aes_nohw_set_encrypt_key GRPC_SHADOW_aes_nohw_set_encrypt_key
#define aesgcmsiv_htable6_init GRPC_SHADOW_aesgcmsiv_htable6_init
#define aesgcmsiv_htable_init GRPC_SHADOW_aesgcmsiv_htable_init
#define aesgcmsiv_htable_polyval GRPC_SHADOW_aesgcmsiv_htable_polyval
#define aesgcmsiv_polyval_horner GRPC_SHADOW_aesgcmsiv_polyval_horner
#define aesni_gcm_decrypt GRPC_SHADOW_aesni_gcm_decrypt
#define aesni_gcm_encrypt GRPC_SHADOW_aesni_gcm_encrypt
#define asn1_do_adb GRPC_SHADOW_asn1_do_adb
#define asn1_enc_free GRPC_SHADOW_asn1_enc_free
#define asn1_enc_init GRPC_SHADOW_asn1_enc_init
#define asn1_enc_restore GRPC_SHADOW_asn1_enc_restore
#define asn1_enc_save GRPC_SHADOW_asn1_enc_save
#define asn1_ex_c2i GRPC_SHADOW_asn1_ex_c2i
#define asn1_ex_i2c GRPC_SHADOW_asn1_ex_i2c
#define asn1_generalizedtime_to_tm GRPC_SHADOW_asn1_generalizedtime_to_tm
#define asn1_get_choice_selector GRPC_SHADOW_asn1_get_choice_selector
#define asn1_get_field_ptr GRPC_SHADOW_asn1_get_field_ptr
#define asn1_item_combine_free GRPC_SHADOW_asn1_item_combine_free
#define asn1_refcount_dec_and_test_zero GRPC_SHADOW_asn1_refcount_dec_and_test_zero
#define asn1_refcount_set_one GRPC_SHADOW_asn1_refcount_set_one
#define asn1_set_choice_selector GRPC_SHADOW_asn1_set_choice_selector
#define asn1_utctime_to_tm GRPC_SHADOW_asn1_utctime_to_tm
#define beeu_mod_inverse_vartime GRPC_SHADOW_beeu_mod_inverse_vartime
#define bio_clear_socket_error GRPC_SHADOW_bio_clear_socket_error
#define bio_fd_should_retry GRPC_SHADOW_bio_fd_should_retry
#define bio_ip_and_port_to_socket_and_addr GRPC_SHADOW_bio_ip_and_port_to_socket_and_addr
#define bio_sock_error GRPC_SHADOW_bio_sock_error
#define bio_socket_nbio GRPC_SHADOW_bio_socket_nbio
#define bn_abs_sub_consttime GRPC_SHADOW_bn_abs_sub_consttime
#define bn_add_words GRPC_SHADOW_bn_add_words
#define bn_copy_words GRPC_SHADOW_bn_copy_words
#define bn_div_consttime GRPC_SHADOW_bn_div_consttime
#define bn_expand GRPC_SHADOW_bn_expand
#define bn_fits_in_words GRPC_SHADOW_bn_fits_in_words
#define bn_from_montgomery GRPC_SHADOW_bn_from_montgomery
#define bn_from_montgomery_small GRPC_SHADOW_bn_from_montgomery_small
#define bn_gather5 GRPC_SHADOW_bn_gather5
#define bn_in_range_words GRPC_SHADOW_bn_in_range_words
#define bn_is_bit_set_words GRPC_SHADOW_bn_is_bit_set_words
#define bn_is_relatively_prime GRPC_SHADOW_bn_is_relatively_prime
#define bn_jacobi GRPC_SHADOW_bn_jacobi
#define bn_lcm_consttime GRPC_SHADOW_bn_lcm_consttime
#define bn_less_than_montgomery_R GRPC_SHADOW_bn_less_than_montgomery_R
#define bn_less_than_words GRPC_SHADOW_bn_less_than_words
#define bn_miller_rabin_init GRPC_SHADOW_bn_miller_rabin_init
#define bn_miller_rabin_iteration GRPC_SHADOW_bn_miller_rabin_iteration
#define bn_minimal_width GRPC_SHADOW_bn_minimal_width
#define bn_mod_add_consttime GRPC_SHADOW_bn_mod_add_consttime
#define bn_mod_add_words GRPC_SHADOW_bn_mod_add_words
#define bn_mod_exp_base_2_consttime GRPC_SHADOW_bn_mod_exp_base_2_consttime
#define bn_mod_exp_mont_small GRPC_SHADOW_bn_mod_exp_mont_small
#define bn_mod_inverse_consttime GRPC_SHADOW_bn_mod_inverse_consttime
#define bn_mod_inverse_prime GRPC_SHADOW_bn_mod_inverse_prime
#define bn_mod_inverse_prime_mont_small GRPC_SHADOW_bn_mod_inverse_prime_mont_small
#define bn_mod_inverse_secret_prime GRPC_SHADOW_bn_mod_inverse_secret_prime
#define bn_mod_lshift1_consttime GRPC_SHADOW_bn_mod_lshift1_consttime
#define bn_mod_lshift_consttime GRPC_SHADOW_bn_mod_lshift_consttime
#define bn_mod_mul_montgomery_small GRPC_SHADOW_bn_mod_mul_montgomery_small
#define bn_mod_sub_consttime GRPC_SHADOW_bn_mod_sub_consttime
#define bn_mod_sub_words GRPC_SHADOW_bn_mod_sub_words
#define bn_mod_u16_consttime GRPC_SHADOW_bn_mod_u16_consttime
#define bn_mont_n0 GRPC_SHADOW_bn_mont_n0
#define bn_mul_add_words GRPC_SHADOW_bn_mul_add_words
#define bn_mul_comba4 GRPC_SHADOW_bn_mul_comba4
#define bn_mul_comba8 GRPC_SHADOW_bn_mul_comba8
#define bn_mul_consttime GRPC_SHADOW_bn_mul_consttime
#define bn_mul_mont GRPC_SHADOW_bn_mul_mont
#define bn_mul_mont_gather5 GRPC_SHADOW_bn_mul_mont_gather5
#define bn_mul_small GRPC_SHADOW_bn_mul_small
#define bn_mul_words GRPC_SHADOW_bn_mul_words
#define bn_odd_number_is_obviously_composite GRPC_SHADOW_bn_odd_number_is_obviously_composite
#define bn_one_to_montgomery GRPC_SHADOW_bn_one_to_montgomery
#define bn_power5 GRPC_SHADOW_bn_power5
#define bn_rand_range_words GRPC_SHADOW_bn_rand_range_words
#define bn_rand_secret_range GRPC_SHADOW_bn_rand_secret_range
#define bn_reduce_once GRPC_SHADOW_bn_reduce_once
#define bn_reduce_once_in_place GRPC_SHADOW_bn_reduce_once_in_place
#define bn_resize_words GRPC_SHADOW_bn_resize_words
#define bn_rshift1_words GRPC_SHADOW_bn_rshift1_words
#define bn_rshift_secret_shift GRPC_SHADOW_bn_rshift_secret_shift
#define bn_rshift_words GRPC_SHADOW_bn_rshift_words
#define bn_scatter5 GRPC_SHADOW_bn_scatter5
#define bn_select_words GRPC_SHADOW_bn_select_words
#define bn_set_minimal_width GRPC_SHADOW_bn_set_minimal_width
#define bn_set_words GRPC_SHADOW_bn_set_words
#define bn_sqr8x_internal GRPC_SHADOW_bn_sqr8x_internal
#define bn_sqr_comba4 GRPC_SHADOW_bn_sqr_comba4
#define bn_sqr_comba8 GRPC_SHADOW_bn_sqr_comba8
#define bn_sqr_consttime GRPC_SHADOW_bn_sqr_consttime
#define bn_sqr_small GRPC_SHADOW_bn_sqr_small
#define bn_sqr_words GRPC_SHADOW_bn_sqr_words
#define bn_sqrx8x_internal GRPC_SHADOW_bn_sqrx8x_internal
#define bn_sub_words GRPC_SHADOW_bn_sub_words
#define bn_to_montgomery_small GRPC_SHADOW_bn_to_montgomery_small
#define bn_uadd_consttime GRPC_SHADOW_bn_uadd_consttime
#define bn_usub_consttime GRPC_SHADOW_bn_usub_consttime
#define bn_wexpand GRPC_SHADOW_bn_wexpand
#define boringssl_fips_self_test GRPC_SHADOW_boringssl_fips_self_test
#define c2i_ASN1_BIT_STRING GRPC_SHADOW_c2i_ASN1_BIT_STRING
#define c2i_ASN1_INTEGER GRPC_SHADOW_c2i_ASN1_INTEGER
#define c2i_ASN1_OBJECT GRPC_SHADOW_c2i_ASN1_OBJECT
#define cbb_add_latin1 GRPC_SHADOW_cbb_add_latin1
#define cbb_add_ucs2_be GRPC_SHADOW_cbb_add_ucs2_be
#define cbb_add_utf32_be GRPC_SHADOW_cbb_add_utf32_be
#define cbb_add_utf8 GRPC_SHADOW_cbb_add_utf8
#define cbb_get_utf8_len GRPC_SHADOW_cbb_get_utf8_len
#define cbs_get_latin1 GRPC_SHADOW_cbs_get_latin1
#define cbs_get_ucs2_be GRPC_SHADOW_cbs_get_ucs2_be
#define cbs_get_utf32_be GRPC_SHADOW_cbs_get_utf32_be
#define cbs_get_utf8 GRPC_SHADOW_cbs_get_utf8
#define chacha20_poly1305_open GRPC_SHADOW_chacha20_poly1305_open
#define chacha20_poly1305_seal GRPC_SHADOW_chacha20_poly1305_seal
#define crypto_gcm_clmul_enabled GRPC_SHADOW_crypto_gcm_clmul_enabled
#define d2i_ACCESS_DESCRIPTION GRPC_SHADOW_d2i_ACCESS_DESCRIPTION
#define d2i_ASN1_BIT_STRING GRPC_SHADOW_d2i_ASN1_BIT_STRING
#define d2i_ASN1_BMPSTRING GRPC_SHADOW_d2i_ASN1_BMPSTRING
#define d2i_ASN1_BOOLEAN GRPC_SHADOW_d2i_ASN1_BOOLEAN
#define d2i_ASN1_ENUMERATED GRPC_SHADOW_d2i_ASN1_ENUMERATED
#define d2i_ASN1_GENERALIZEDTIME GRPC_SHADOW_d2i_ASN1_GENERALIZEDTIME
#define d2i_ASN1_GENERALSTRING GRPC_SHADOW_d2i_ASN1_GENERALSTRING
#define d2i_ASN1_IA5STRING GRPC_SHADOW_d2i_ASN1_IA5STRING
#define d2i_ASN1_INTEGER GRPC_SHADOW_d2i_ASN1_INTEGER
#define d2i_ASN1_NULL GRPC_SHADOW_d2i_ASN1_NULL
#define d2i_ASN1_OBJECT GRPC_SHADOW_d2i_ASN1_OBJECT
#define d2i_ASN1_OCTET_STRING GRPC_SHADOW_d2i_ASN1_OCTET_STRING
#define d2i_ASN1_PRINTABLE GRPC_SHADOW_d2i_ASN1_PRINTABLE
#define d2i_ASN1_PRINTABLESTRING GRPC_SHADOW_d2i_ASN1_PRINTABLESTRING
#define d2i_ASN1_SEQUENCE_ANY GRPC_SHADOW_d2i_ASN1_SEQUENCE_ANY
#define d2i_ASN1_SET_ANY GRPC_SHADOW_d2i_ASN1_SET_ANY
#define d2i_ASN1_T61STRING GRPC_SHADOW_d2i_ASN1_T61STRING
#define d2i_ASN1_TIME GRPC_SHADOW_d2i_ASN1_TIME
#define d2i_ASN1_TYPE GRPC_SHADOW_d2i_ASN1_TYPE
#define d2i_ASN1_UNIVERSALSTRING GRPC_SHADOW_d2i_ASN1_UNIVERSALSTRING
#define d2i_ASN1_UTCTIME GRPC_SHADOW_d2i_ASN1_UTCTIME
#define d2i_ASN1_UTF8STRING GRPC_SHADOW_d2i_ASN1_UTF8STRING
#define d2i_ASN1_VISIBLESTRING GRPC_SHADOW_d2i_ASN1_VISIBLESTRING
#define d2i_AUTHORITY_INFO_ACCESS GRPC_SHADOW_d2i_AUTHORITY_INFO_ACCESS
#define d2i_AUTHORITY_KEYID GRPC_SHADOW_d2i_AUTHORITY_KEYID
#define d2i_AutoPrivateKey GRPC_SHADOW_d2i_AutoPrivateKey
#define d2i_BASIC_CONSTRAINTS GRPC_SHADOW_d2i_BASIC_CONSTRAINTS
#define d2i_CERTIFICATEPOLICIES GRPC_SHADOW_d2i_CERTIFICATEPOLICIES
#define d2i_CRL_DIST_POINTS GRPC_SHADOW_d2i_CRL_DIST_POINTS
#define d2i_DHparams GRPC_SHADOW_d2i_DHparams
#define d2i_DHparams_bio GRPC_SHADOW_d2i_DHparams_bio
#define d2i_DIRECTORYSTRING GRPC_SHADOW_d2i_DIRECTORYSTRING
#define d2i_DISPLAYTEXT GRPC_SHADOW_d2i_DISPLAYTEXT
#define d2i_DIST_POINT GRPC_SHADOW_d2i_DIST_POINT
#define d2i_DIST_POINT_NAME GRPC_SHADOW_d2i_DIST_POINT_NAME
#define d2i_DSAPrivateKey GRPC_SHADOW_d2i_DSAPrivateKey
#define d2i_DSAPrivateKey_bio GRPC_SHADOW_d2i_DSAPrivateKey_bio
#define d2i_DSAPrivateKey_fp GRPC_SHADOW_d2i_DSAPrivateKey_fp
#define d2i_DSAPublicKey GRPC_SHADOW_d2i_DSAPublicKey
#define d2i_DSA_PUBKEY GRPC_SHADOW_d2i_DSA_PUBKEY
#define d2i_DSA_PUBKEY_bio GRPC_SHADOW_d2i_DSA_PUBKEY_bio
#define d2i_DSA_PUBKEY_fp GRPC_SHADOW_d2i_DSA_PUBKEY_fp
#define d2i_DSA_SIG GRPC_SHADOW_d2i_DSA_SIG
#define d2i_DSAparams GRPC_SHADOW_d2i_DSAparams
#define d2i_ECDSA_SIG GRPC_SHADOW_d2i_ECDSA_SIG
#define d2i_ECParameters GRPC_SHADOW_d2i_ECParameters
#define d2i_ECPrivateKey GRPC_SHADOW_d2i_ECPrivateKey
#define d2i_ECPrivateKey_bio GRPC_SHADOW_d2i_ECPrivateKey_bio
#define d2i_ECPrivateKey_fp GRPC_SHADOW_d2i_ECPrivateKey_fp
#define d2i_EC_PUBKEY GRPC_SHADOW_d2i_EC_PUBKEY
#define d2i_EC_PUBKEY_bio GRPC_SHADOW_d2i_EC_PUBKEY_bio
#define d2i_EC_PUBKEY_fp GRPC_SHADOW_d2i_EC_PUBKEY_fp
#define d2i_EDIPARTYNAME GRPC_SHADOW_d2i_EDIPARTYNAME
#define d2i_EXTENDED_KEY_USAGE GRPC_SHADOW_d2i_EXTENDED_KEY_USAGE
#define d2i_GENERAL_NAME GRPC_SHADOW_d2i_GENERAL_NAME
#define d2i_GENERAL_NAMES GRPC_SHADOW_d2i_GENERAL_NAMES
#define d2i_ISSUING_DIST_POINT GRPC_SHADOW_d2i_ISSUING_DIST_POINT
#define d2i_NETSCAPE_SPKAC GRPC_SHADOW_d2i_NETSCAPE_SPKAC
#define d2i_NETSCAPE_SPKI GRPC_SHADOW_d2i_NETSCAPE_SPKI
#define d2i_NOTICEREF GRPC_SHADOW_d2i_NOTICEREF
#define d2i_OTHERNAME GRPC_SHADOW_d2i_OTHERNAME
#define d2i_PKCS12 GRPC_SHADOW_d2i_PKCS12
#define d2i_PKCS12_bio GRPC_SHADOW_d2i_PKCS12_bio
#define d2i_PKCS12_fp GRPC_SHADOW_d2i_PKCS12_fp
#define d2i_PKCS7 GRPC_SHADOW_d2i_PKCS7
#define d2i_PKCS7_bio GRPC_SHADOW_d2i_PKCS7_bio
#define d2i_PKCS8PrivateKey_bio GRPC_SHADOW_d2i_PKCS8PrivateKey_bio
#define d2i_PKCS8PrivateKey_fp GRPC_SHADOW_d2i_PKCS8PrivateKey_fp
#define d2i_PKCS8_PRIV_KEY_INFO GRPC_SHADOW_d2i_PKCS8_PRIV_KEY_INFO
#define d2i_PKCS8_PRIV_KEY_INFO_bio GRPC_SHADOW_d2i_PKCS8_PRIV_KEY_INFO_bio
#define d2i_PKCS8_PRIV_KEY_INFO_fp GRPC_SHADOW_d2i_PKCS8_PRIV_KEY_INFO_fp
#define d2i_PKCS8_bio GRPC_SHADOW_d2i_PKCS8_bio
#define d2i_PKCS8_fp GRPC_SHADOW_d2i_PKCS8_fp
#define d2i_PKEY_USAGE_PERIOD GRPC_SHADOW_d2i_PKEY_USAGE_PERIOD
#define d2i_POLICYINFO GRPC_SHADOW_d2i_POLICYINFO
#define d2i_POLICYQUALINFO GRPC_SHADOW_d2i_POLICYQUALINFO
#define d2i_PROXY_CERT_INFO_EXTENSION GRPC_SHADOW_d2i_PROXY_CERT_INFO_EXTENSION
#define d2i_PROXY_POLICY GRPC_SHADOW_d2i_PROXY_POLICY
#define d2i_PUBKEY GRPC_SHADOW_d2i_PUBKEY
#define d2i_PUBKEY_bio GRPC_SHADOW_d2i_PUBKEY_bio
#define d2i_PUBKEY_fp GRPC_SHADOW_d2i_PUBKEY_fp
#define d2i_PrivateKey GRPC_SHADOW_d2i_PrivateKey
#define d2i_PrivateKey_bio GRPC_SHADOW_d2i_PrivateKey_bio
#define d2i_PrivateKey_fp GRPC_SHADOW_d2i_PrivateKey_fp
#define d2i_PublicKey GRPC_SHADOW_d2i_PublicKey
#define d2i_RSAPrivateKey GRPC_SHADOW_d2i_RSAPrivateKey
#define d2i_RSAPrivateKey_bio GRPC_SHADOW_d2i_RSAPrivateKey_bio
#define d2i_RSAPrivateKey_fp GRPC_SHADOW_d2i_RSAPrivateKey_fp
#define d2i_RSAPublicKey GRPC_SHADOW_d2i_RSAPublicKey
#define d2i_RSAPublicKey_bio GRPC_SHADOW_d2i_RSAPublicKey_bio
#define d2i_RSAPublicKey_fp GRPC_SHADOW_d2i_RSAPublicKey_fp
#define d2i_RSA_PSS_PARAMS GRPC_SHADOW_d2i_RSA_PSS_PARAMS
#define d2i_RSA_PUBKEY GRPC_SHADOW_d2i_RSA_PUBKEY
#define d2i_RSA_PUBKEY_bio GRPC_SHADOW_d2i_RSA_PUBKEY_bio
#define d2i_RSA_PUBKEY_fp GRPC_SHADOW_d2i_RSA_PUBKEY_fp
#define d2i_SSL_SESSION GRPC_SHADOW_d2i_SSL_SESSION
#define d2i_SSL_SESSION_bio GRPC_SHADOW_d2i_SSL_SESSION_bio
#define d2i_SXNET GRPC_SHADOW_d2i_SXNET
#define d2i_SXNETID GRPC_SHADOW_d2i_SXNETID
#define d2i_USERNOTICE GRPC_SHADOW_d2i_USERNOTICE
#define d2i_X509 GRPC_SHADOW_d2i_X509
#define d2i_X509_ALGOR GRPC_SHADOW_d2i_X509_ALGOR
#define d2i_X509_ALGORS GRPC_SHADOW_d2i_X509_ALGORS
#define d2i_X509_ATTRIBUTE GRPC_SHADOW_d2i_X509_ATTRIBUTE
#define d2i_X509_AUX GRPC_SHADOW_d2i_X509_AUX
#define d2i_X509_CERT_AUX GRPC_SHADOW_d2i_X509_CERT_AUX
#define d2i_X509_CINF GRPC_SHADOW_d2i_X509_CINF
#define d2i_X509_CRL GRPC_SHADOW_d2i_X509_CRL
#define d2i_X509_CRL_INFO GRPC_SHADOW_d2i_X509_CRL_INFO
#define d2i_X509_CRL_bio GRPC_SHADOW_d2i_X509_CRL_bio
#define d2i_X509_CRL_fp GRPC_SHADOW_d2i_X509_CRL_fp
#define d2i_X509_EXTENSION GRPC_SHADOW_d2i_X509_EXTENSION
#define d2i_X509_EXTENSIONS GRPC_SHADOW_d2i_X509_EXTENSIONS
#define d2i_X509_NAME GRPC_SHADOW_d2i_X509_NAME
#define d2i_X509_NAME_ENTRY GRPC_SHADOW_d2i_X509_NAME_ENTRY
#define d2i_X509_PUBKEY GRPC_SHADOW_d2i_X509_PUBKEY
#define d2i_X509_REQ GRPC_SHADOW_d2i_X509_REQ
#define d2i_X509_REQ_INFO GRPC_SHADOW_d2i_X509_REQ_INFO
#define d2i_X509_REQ_bio GRPC_SHADOW_d2i_X509_REQ_bio
#define d2i_X509_REQ_fp GRPC_SHADOW_d2i_X509_REQ_fp
#define d2i_X509_REVOKED GRPC_SHADOW_d2i_X509_REVOKED
#define d2i_X509_SIG GRPC_SHADOW_d2i_X509_SIG
#define d2i_X509_VAL GRPC_SHADOW_d2i_X509_VAL
#define d2i_X509_bio GRPC_SHADOW_d2i_X509_bio
#define d2i_X509_fp GRPC_SHADOW_d2i_X509_fp
#define dsa_asn1_meth GRPC_SHADOW_dsa_asn1_meth
#define ec_GFp_mont_add GRPC_SHADOW_ec_GFp_mont_add
#define ec_GFp_mont_bignum_to_felem GRPC_SHADOW_ec_GFp_mont_bignum_to_felem
#define ec_GFp_mont_dbl GRPC_SHADOW_ec_GFp_mont_dbl
#define ec_GFp_mont_felem_mul GRPC_SHADOW_ec_GFp_mont_felem_mul
#define ec_GFp_mont_felem_sqr GRPC_SHADOW_ec_GFp_mont_felem_sqr
#define ec_GFp_mont_felem_to_bignum GRPC_SHADOW_ec_GFp_mont_felem_to_bignum
#define ec_GFp_mont_group_finish GRPC_SHADOW_ec_GFp_mont_group_finish
#define ec_GFp_mont_group_init GRPC_SHADOW_ec_GFp_mont_group_init
#define ec_GFp_mont_group_set_curve GRPC_SHADOW_ec_GFp_mont_group_set_curve
#define ec_GFp_mont_mul GRPC_SHADOW_ec_GFp_mont_mul
#define ec_GFp_mont_mul_base GRPC_SHADOW_ec_GFp_mont_mul_base
#define ec_GFp_mont_mul_public GRPC_SHADOW_ec_GFp_mont_mul_public
#define ec_GFp_nistp_recode_scalar_bits GRPC_SHADOW_ec_GFp_nistp_recode_scalar_bits
#define ec_GFp_simple_cmp GRPC_SHADOW_ec_GFp_simple_cmp
#define ec_GFp_simple_cmp_x_coordinate GRPC_SHADOW_ec_GFp_simple_cmp_x_coordinate
#define ec_GFp_simple_group_finish GRPC_SHADOW_ec_GFp_simple_group_finish
#define ec_GFp_simple_group_get_curve GRPC_SHADOW_ec_GFp_simple_group_get_curve
#define ec_GFp_simple_group_init GRPC_SHADOW_ec_GFp_simple_group_init
#define ec_GFp_simple_group_set_curve GRPC_SHADOW_ec_GFp_simple_group_set_curve
#define ec_GFp_simple_invert GRPC_SHADOW_ec_GFp_simple_invert
#define ec_GFp_simple_is_at_infinity GRPC_SHADOW_ec_GFp_simple_is_at_infinity
#define ec_GFp_simple_is_on_curve GRPC_SHADOW_ec_GFp_simple_is_on_curve
#define ec_GFp_simple_mont_inv_mod_ord_vartime GRPC_SHADOW_ec_GFp_simple_mont_inv_mod_ord_vartime
#define ec_GFp_simple_point_copy GRPC_SHADOW_ec_GFp_simple_point_copy
#define ec_GFp_simple_point_init GRPC_SHADOW_ec_GFp_simple_point_init
#define ec_GFp_simple_point_set_affine_coordinates GRPC_SHADOW_ec_GFp_simple_point_set_affine_coordinates
#define ec_GFp_simple_point_set_to_infinity GRPC_SHADOW_ec_GFp_simple_point_set_to_infinity
#define ec_asn1_meth GRPC_SHADOW_ec_asn1_meth
#define ec_bignum_to_felem GRPC_SHADOW_ec_bignum_to_felem
#define ec_bignum_to_scalar GRPC_SHADOW_ec_bignum_to_scalar
#define ec_cmp_x_coordinate GRPC_SHADOW_ec_cmp_x_coordinate
#define ec_compute_wNAF GRPC_SHADOW_ec_compute_wNAF
#define ec_felem_add GRPC_SHADOW_ec_felem_add
#define ec_felem_equal GRPC_SHADOW_ec_felem_equal
#define ec_felem_neg GRPC_SHADOW_ec_felem_neg
#define ec_felem_non_zero_mask GRPC_SHADOW_ec_felem_non_zero_mask
#define ec_felem_select GRPC_SHADOW_ec_felem_select
#define ec_felem_sub GRPC_SHADOW_ec_felem_sub
#define ec_felem_to_bignum GRPC_SHADOW_ec_felem_to_bignum
#define ec_get_x_coordinate_as_scalar GRPC_SHADOW_ec_get_x_coordinate_as_scalar
#define ec_group_new GRPC_SHADOW_ec_group_new
#define ec_pkey_meth GRPC_SHADOW_ec_pkey_meth
#define ec_point_get_affine_coordinate_bytes GRPC_SHADOW_ec_point_get_affine_coordinate_bytes
#define ec_point_mul_scalar GRPC_SHADOW_ec_point_mul_scalar
#define ec_point_mul_scalar_base GRPC_SHADOW_ec_point_mul_scalar_base
#define ec_point_mul_scalar_public GRPC_SHADOW_ec_point_mul_scalar_public
#define ec_random_nonzero_scalar GRPC_SHADOW_ec_random_nonzero_scalar
#define ec_scalar_add GRPC_SHADOW_ec_scalar_add
#define ec_scalar_equal_vartime GRPC_SHADOW_ec_scalar_equal_vartime
#define ec_scalar_from_montgomery GRPC_SHADOW_ec_scalar_from_montgomery
#define ec_scalar_inv_montgomery GRPC_SHADOW_ec_scalar_inv_montgomery
#define ec_scalar_inv_montgomery_vartime GRPC_SHADOW_ec_scalar_inv_montgomery_vartime
#define ec_scalar_is_zero GRPC_SHADOW_ec_scalar_is_zero
#define ec_scalar_mul_montgomery GRPC_SHADOW_ec_scalar_mul_montgomery
#define ec_scalar_to_montgomery GRPC_SHADOW_ec_scalar_to_montgomery
#define ec_simple_scalar_inv_montgomery GRPC_SHADOW_ec_simple_scalar_inv_montgomery
#define ecp_nistz256_avx2_select_w7 GRPC_SHADOW_ecp_nistz256_avx2_select_w7
#define ecp_nistz256_mul_mont GRPC_SHADOW_ecp_nistz256_mul_mont
#define ecp_nistz256_neg GRPC_SHADOW_ecp_nistz256_neg
#define ecp_nistz256_ord_mul_mont GRPC_SHADOW_ecp_nistz256_ord_mul_mont
#define ecp_nistz256_ord_sqr_mont GRPC_SHADOW_ecp_nistz256_ord_sqr_mont
#define ecp_nistz256_point_add GRPC_SHADOW_ecp_nistz256_point_add
#define ecp_nistz256_point_add_affine GRPC_SHADOW_ecp_nistz256_point_add_affine
#define ecp_nistz256_point_double GRPC_SHADOW_ecp_nistz256_point_double
#define ecp_nistz256_select_w5 GRPC_SHADOW_ecp_nistz256_select_w5
#define ecp_nistz256_select_w7 GRPC_SHADOW_ecp_nistz256_select_w7
#define ecp_nistz256_sqr_mont GRPC_SHADOW_ecp_nistz256_sqr_mont
#define ed25519_asn1_meth GRPC_SHADOW_ed25519_asn1_meth
#define ed25519_pkey_meth GRPC_SHADOW_ed25519_pkey_meth
#define gcm_ghash_4bit GRPC_SHADOW_gcm_ghash_4bit
#define gcm_ghash_avx GRPC_SHADOW_gcm_ghash_avx
#define gcm_ghash_clmul GRPC_SHADOW_gcm_ghash_clmul
#define gcm_ghash_ssse3 GRPC_SHADOW_gcm_ghash_ssse3
#define gcm_gmult_4bit GRPC_SHADOW_gcm_gmult_4bit
#define gcm_gmult_avx GRPC_SHADOW_gcm_gmult_avx
#define gcm_gmult_clmul GRPC_SHADOW_gcm_gmult_clmul
#define gcm_gmult_ssse3 GRPC_SHADOW_gcm_gmult_ssse3
#define gcm_init_4bit GRPC_SHADOW_gcm_init_4bit
#define gcm_init_avx GRPC_SHADOW_gcm_init_avx
#define gcm_init_clmul GRPC_SHADOW_gcm_init_clmul
#define gcm_init_ssse3 GRPC_SHADOW_gcm_init_ssse3
#define i2a_ACCESS_DESCRIPTION GRPC_SHADOW_i2a_ACCESS_DESCRIPTION
#define i2a_ASN1_ENUMERATED GRPC_SHADOW_i2a_ASN1_ENUMERATED
#define i2a_ASN1_INTEGER GRPC_SHADOW_i2a_ASN1_INTEGER
#define i2a_ASN1_OBJECT GRPC_SHADOW_i2a_ASN1_OBJECT
#define i2a_ASN1_STRING GRPC_SHADOW_i2a_ASN1_STRING
#define i2c_ASN1_BIT_STRING GRPC_SHADOW_i2c_ASN1_BIT_STRING
#define i2c_ASN1_INTEGER GRPC_SHADOW_i2c_ASN1_INTEGER
#define i2d_ACCESS_DESCRIPTION GRPC_SHADOW_i2d_ACCESS_DESCRIPTION
#define i2d_ASN1_BIT_STRING GRPC_SHADOW_i2d_ASN1_BIT_STRING
#define i2d_ASN1_BMPSTRING GRPC_SHADOW_i2d_ASN1_BMPSTRING
#define i2d_ASN1_BOOLEAN GRPC_SHADOW_i2d_ASN1_BOOLEAN
#define i2d_ASN1_ENUMERATED GRPC_SHADOW_i2d_ASN1_ENUMERATED
#define i2d_ASN1_GENERALIZEDTIME GRPC_SHADOW_i2d_ASN1_GENERALIZEDTIME
#define i2d_ASN1_GENERALSTRING GRPC_SHADOW_i2d_ASN1_GENERALSTRING
#define i2d_ASN1_IA5STRING GRPC_SHADOW_i2d_ASN1_IA5STRING
#define i2d_ASN1_INTEGER GRPC_SHADOW_i2d_ASN1_INTEGER
#define i2d_ASN1_NULL GRPC_SHADOW_i2d_ASN1_NULL
#define i2d_ASN1_OBJECT GRPC_SHADOW_i2d_ASN1_OBJECT
#define i2d_ASN1_OCTET_STRING GRPC_SHADOW_i2d_ASN1_OCTET_STRING
#define i2d_ASN1_PRINTABLE GRPC_SHADOW_i2d_ASN1_PRINTABLE
#define i2d_ASN1_PRINTABLESTRING GRPC_SHADOW_i2d_ASN1_PRINTABLESTRING
#define i2d_ASN1_SEQUENCE_ANY GRPC_SHADOW_i2d_ASN1_SEQUENCE_ANY
#define i2d_ASN1_SET_ANY GRPC_SHADOW_i2d_ASN1_SET_ANY
#define i2d_ASN1_T61STRING GRPC_SHADOW_i2d_ASN1_T61STRING
#define i2d_ASN1_TIME GRPC_SHADOW_i2d_ASN1_TIME
#define i2d_ASN1_TYPE GRPC_SHADOW_i2d_ASN1_TYPE
#define i2d_ASN1_UNIVERSALSTRING GRPC_SHADOW_i2d_ASN1_UNIVERSALSTRING
#define i2d_ASN1_UTCTIME GRPC_SHADOW_i2d_ASN1_UTCTIME
#define i2d_ASN1_UTF8STRING GRPC_SHADOW_i2d_ASN1_UTF8STRING
#define i2d_ASN1_VISIBLESTRING GRPC_SHADOW_i2d_ASN1_VISIBLESTRING
#define i2d_AUTHORITY_INFO_ACCESS GRPC_SHADOW_i2d_AUTHORITY_INFO_ACCESS
#define i2d_AUTHORITY_KEYID GRPC_SHADOW_i2d_AUTHORITY_KEYID
#define i2d_BASIC_CONSTRAINTS GRPC_SHADOW_i2d_BASIC_CONSTRAINTS
#define i2d_CERTIFICATEPOLICIES GRPC_SHADOW_i2d_CERTIFICATEPOLICIES
#define i2d_CRL_DIST_POINTS GRPC_SHADOW_i2d_CRL_DIST_POINTS
#define i2d_DHparams GRPC_SHADOW_i2d_DHparams
#define i2d_DHparams_bio GRPC_SHADOW_i2d_DHparams_bio
#define i2d_DIRECTORYSTRING GRPC_SHADOW_i2d_DIRECTORYSTRING
#define i2d_DISPLAYTEXT GRPC_SHADOW_i2d_DISPLAYTEXT
#define i2d_DIST_POINT GRPC_SHADOW_i2d_DIST_POINT
#define i2d_DIST_POINT_NAME GRPC_SHADOW_i2d_DIST_POINT_NAME
#define i2d_DSAPrivateKey GRPC_SHADOW_i2d_DSAPrivateKey
#define i2d_DSAPrivateKey_bio GRPC_SHADOW_i2d_DSAPrivateKey_bio
#define i2d_DSAPrivateKey_fp GRPC_SHADOW_i2d_DSAPrivateKey_fp
#define i2d_DSAPublicKey GRPC_SHADOW_i2d_DSAPublicKey
#define i2d_DSA_PUBKEY GRPC_SHADOW_i2d_DSA_PUBKEY
#define i2d_DSA_PUBKEY_bio GRPC_SHADOW_i2d_DSA_PUBKEY_bio
#define i2d_DSA_PUBKEY_fp GRPC_SHADOW_i2d_DSA_PUBKEY_fp
#define i2d_DSA_SIG GRPC_SHADOW_i2d_DSA_SIG
#define i2d_DSAparams GRPC_SHADOW_i2d_DSAparams
#define i2d_ECDSA_SIG GRPC_SHADOW_i2d_ECDSA_SIG
#define i2d_ECParameters GRPC_SHADOW_i2d_ECParameters
#define i2d_ECPrivateKey GRPC_SHADOW_i2d_ECPrivateKey
#define i2d_ECPrivateKey_bio GRPC_SHADOW_i2d_ECPrivateKey_bio
#define i2d_ECPrivateKey_fp GRPC_SHADOW_i2d_ECPrivateKey_fp
#define i2d_EC_PUBKEY GRPC_SHADOW_i2d_EC_PUBKEY
#define i2d_EC_PUBKEY_bio GRPC_SHADOW_i2d_EC_PUBKEY_bio
#define i2d_EC_PUBKEY_fp GRPC_SHADOW_i2d_EC_PUBKEY_fp
#define i2d_EDIPARTYNAME GRPC_SHADOW_i2d_EDIPARTYNAME
#define i2d_EXTENDED_KEY_USAGE GRPC_SHADOW_i2d_EXTENDED_KEY_USAGE
#define i2d_GENERAL_NAME GRPC_SHADOW_i2d_GENERAL_NAME
#define i2d_GENERAL_NAMES GRPC_SHADOW_i2d_GENERAL_NAMES
#define i2d_ISSUING_DIST_POINT GRPC_SHADOW_i2d_ISSUING_DIST_POINT
#define i2d_NETSCAPE_SPKAC GRPC_SHADOW_i2d_NETSCAPE_SPKAC
#define i2d_NETSCAPE_SPKI GRPC_SHADOW_i2d_NETSCAPE_SPKI
#define i2d_NOTICEREF GRPC_SHADOW_i2d_NOTICEREF
#define i2d_OTHERNAME GRPC_SHADOW_i2d_OTHERNAME
#define i2d_PKCS12 GRPC_SHADOW_i2d_PKCS12
#define i2d_PKCS12_bio GRPC_SHADOW_i2d_PKCS12_bio
#define i2d_PKCS12_fp GRPC_SHADOW_i2d_PKCS12_fp
#define i2d_PKCS7 GRPC_SHADOW_i2d_PKCS7
#define i2d_PKCS7_bio GRPC_SHADOW_i2d_PKCS7_bio
#define i2d_PKCS8PrivateKeyInfo_bio GRPC_SHADOW_i2d_PKCS8PrivateKeyInfo_bio
#define i2d_PKCS8PrivateKeyInfo_fp GRPC_SHADOW_i2d_PKCS8PrivateKeyInfo_fp
#define i2d_PKCS8PrivateKey_bio GRPC_SHADOW_i2d_PKCS8PrivateKey_bio
#define i2d_PKCS8PrivateKey_fp GRPC_SHADOW_i2d_PKCS8PrivateKey_fp
#define i2d_PKCS8PrivateKey_nid_bio GRPC_SHADOW_i2d_PKCS8PrivateKey_nid_bio
#define i2d_PKCS8PrivateKey_nid_fp GRPC_SHADOW_i2d_PKCS8PrivateKey_nid_fp
#define i2d_PKCS8_PRIV_KEY_INFO GRPC_SHADOW_i2d_PKCS8_PRIV_KEY_INFO
#define i2d_PKCS8_PRIV_KEY_INFO_bio GRPC_SHADOW_i2d_PKCS8_PRIV_KEY_INFO_bio
#define i2d_PKCS8_PRIV_KEY_INFO_fp GRPC_SHADOW_i2d_PKCS8_PRIV_KEY_INFO_fp
#define i2d_PKCS8_bio GRPC_SHADOW_i2d_PKCS8_bio
#define i2d_PKCS8_fp GRPC_SHADOW_i2d_PKCS8_fp
#define i2d_PKEY_USAGE_PERIOD GRPC_SHADOW_i2d_PKEY_USAGE_PERIOD
#define i2d_POLICYINFO GRPC_SHADOW_i2d_POLICYINFO
#define i2d_POLICYQUALINFO GRPC_SHADOW_i2d_POLICYQUALINFO
#define i2d_PROXY_CERT_INFO_EXTENSION GRPC_SHADOW_i2d_PROXY_CERT_INFO_EXTENSION
#define i2d_PROXY_POLICY GRPC_SHADOW_i2d_PROXY_POLICY
#define i2d_PUBKEY GRPC_SHADOW_i2d_PUBKEY
#define i2d_PUBKEY_bio GRPC_SHADOW_i2d_PUBKEY_bio
#define i2d_PUBKEY_fp GRPC_SHADOW_i2d_PUBKEY_fp
#define i2d_PrivateKey GRPC_SHADOW_i2d_PrivateKey
#define i2d_PrivateKey_bio GRPC_SHADOW_i2d_PrivateKey_bio
#define i2d_PrivateKey_fp GRPC_SHADOW_i2d_PrivateKey_fp
#define i2d_PublicKey GRPC_SHADOW_i2d_PublicKey
#define i2d_RSAPrivateKey GRPC_SHADOW_i2d_RSAPrivateKey
#define i2d_RSAPrivateKey_bio GRPC_SHADOW_i2d_RSAPrivateKey_bio
#define i2d_RSAPrivateKey_fp GRPC_SHADOW_i2d_RSAPrivateKey_fp
#define i2d_RSAPublicKey GRPC_SHADOW_i2d_RSAPublicKey
#define i2d_RSAPublicKey_bio GRPC_SHADOW_i2d_RSAPublicKey_bio
#define i2d_RSAPublicKey_fp GRPC_SHADOW_i2d_RSAPublicKey_fp
#define i2d_RSA_PSS_PARAMS GRPC_SHADOW_i2d_RSA_PSS_PARAMS
#define i2d_RSA_PUBKEY GRPC_SHADOW_i2d_RSA_PUBKEY
#define i2d_RSA_PUBKEY_bio GRPC_SHADOW_i2d_RSA_PUBKEY_bio
#define i2d_RSA_PUBKEY_fp GRPC_SHADOW_i2d_RSA_PUBKEY_fp
#define i2d_SSL_SESSION GRPC_SHADOW_i2d_SSL_SESSION
#define i2d_SSL_SESSION_bio GRPC_SHADOW_i2d_SSL_SESSION_bio
#define i2d_SXNET GRPC_SHADOW_i2d_SXNET
#define i2d_SXNETID GRPC_SHADOW_i2d_SXNETID
#define i2d_USERNOTICE GRPC_SHADOW_i2d_USERNOTICE
#define i2d_X509 GRPC_SHADOW_i2d_X509
#define i2d_X509_ALGOR GRPC_SHADOW_i2d_X509_ALGOR
#define i2d_X509_ALGORS GRPC_SHADOW_i2d_X509_ALGORS
#define i2d_X509_ATTRIBUTE GRPC_SHADOW_i2d_X509_ATTRIBUTE
#define i2d_X509_AUX GRPC_SHADOW_i2d_X509_AUX
#define i2d_X509_CERT_AUX GRPC_SHADOW_i2d_X509_CERT_AUX
#define i2d_X509_CINF GRPC_SHADOW_i2d_X509_CINF
#define i2d_X509_CRL GRPC_SHADOW_i2d_X509_CRL
#define i2d_X509_CRL_INFO GRPC_SHADOW_i2d_X509_CRL_INFO
#define i2d_X509_CRL_bio GRPC_SHADOW_i2d_X509_CRL_bio
#define i2d_X509_CRL_fp GRPC_SHADOW_i2d_X509_CRL_fp
#define i2d_X509_EXTENSION GRPC_SHADOW_i2d_X509_EXTENSION
#define i2d_X509_EXTENSIONS GRPC_SHADOW_i2d_X509_EXTENSIONS
#define i2d_X509_NAME GRPC_SHADOW_i2d_X509_NAME
#define i2d_X509_NAME_ENTRY GRPC_SHADOW_i2d_X509_NAME_ENTRY
#define i2d_X509_PUBKEY GRPC_SHADOW_i2d_X509_PUBKEY
#define i2d_X509_REQ GRPC_SHADOW_i2d_X509_REQ
#define i2d_X509_REQ_INFO GRPC_SHADOW_i2d_X509_REQ_INFO
#define i2d_X509_REQ_bio GRPC_SHADOW_i2d_X509_REQ_bio
#define i2d_X509_REQ_fp GRPC_SHADOW_i2d_X509_REQ_fp
#define i2d_X509_REVOKED GRPC_SHADOW_i2d_X509_REVOKED
#define i2d_X509_SIG GRPC_SHADOW_i2d_X509_SIG
#define i2d_X509_VAL GRPC_SHADOW_i2d_X509_VAL
#define i2d_X509_bio GRPC_SHADOW_i2d_X509_bio
#define i2d_X509_fp GRPC_SHADOW_i2d_X509_fp
#define i2d_re_X509_CRL_tbs GRPC_SHADOW_i2d_re_X509_CRL_tbs
#define i2d_re_X509_REQ_tbs GRPC_SHADOW_i2d_re_X509_REQ_tbs
#define i2d_re_X509_tbs GRPC_SHADOW_i2d_re_X509_tbs
#define i2o_ECPublicKey GRPC_SHADOW_i2o_ECPublicKey
#define i2s_ASN1_ENUMERATED GRPC_SHADOW_i2s_ASN1_ENUMERATED
#define i2s_ASN1_ENUMERATED_TABLE GRPC_SHADOW_i2s_ASN1_ENUMERATED_TABLE
#define i2s_ASN1_INTEGER GRPC_SHADOW_i2s_ASN1_INTEGER
#define i2s_ASN1_OCTET_STRING GRPC_SHADOW_i2s_ASN1_OCTET_STRING
#define i2t_ASN1_OBJECT GRPC_SHADOW_i2t_ASN1_OBJECT
#define i2v_ASN1_BIT_STRING GRPC_SHADOW_i2v_ASN1_BIT_STRING
#define i2v_GENERAL_NAME GRPC_SHADOW_i2v_GENERAL_NAME
#define i2v_GENERAL_NAMES GRPC_SHADOW_i2v_GENERAL_NAMES
#define kBoringSSLRSASqrtTwo GRPC_SHADOW_kBoringSSLRSASqrtTwo
#define kBoringSSLRSASqrtTwoLen GRPC_SHADOW_kBoringSSLRSASqrtTwoLen
#define kOpenSSLReasonStringData GRPC_SHADOW_kOpenSSLReasonStringData
#define kOpenSSLReasonValues GRPC_SHADOW_kOpenSSLReasonValues
#define kOpenSSLReasonValuesLen GRPC_SHADOW_kOpenSSLReasonValuesLen
#define level_add_node GRPC_SHADOW_level_add_node
#define level_find_node GRPC_SHADOW_level_find_node
#define lh_delete GRPC_SHADOW_lh_delete
#define lh_doall_arg GRPC_SHADOW_lh_doall_arg
#define lh_free GRPC_SHADOW_lh_free
#define lh_insert GRPC_SHADOW_lh_insert
#define lh_new GRPC_SHADOW_lh_new
#define lh_num_items GRPC_SHADOW_lh_num_items
#define lh_retrieve GRPC_SHADOW_lh_retrieve
#define lh_retrieve_key GRPC_SHADOW_lh_retrieve_key
#define lh_strhash GRPC_SHADOW_lh_strhash
#define md4_block_data_order GRPC_SHADOW_md4_block_data_order
#define md5_block_asm_data_order GRPC_SHADOW_md5_block_asm_data_order
#define o2i_ECPublicKey GRPC_SHADOW_o2i_ECPublicKey
#define pkcs12_iterations_acceptable GRPC_SHADOW_pkcs12_iterations_acceptable
#define pkcs12_key_gen GRPC_SHADOW_pkcs12_key_gen
#define pkcs12_pbe_encrypt_init GRPC_SHADOW_pkcs12_pbe_encrypt_init
#define pkcs7_bundle GRPC_SHADOW_pkcs7_bundle
#define pkcs7_parse_header GRPC_SHADOW_pkcs7_parse_header
#define pkcs8_pbe_decrypt GRPC_SHADOW_pkcs8_pbe_decrypt
#define policy_cache_find_data GRPC_SHADOW_policy_cache_find_data
#define policy_cache_free GRPC_SHADOW_policy_cache_free
#define policy_cache_set GRPC_SHADOW_policy_cache_set
#define policy_cache_set_mapping GRPC_SHADOW_policy_cache_set_mapping
#define policy_data_free GRPC_SHADOW_policy_data_free
#define policy_data_new GRPC_SHADOW_policy_data_new
#define policy_node_cmp_new GRPC_SHADOW_policy_node_cmp_new
#define policy_node_free GRPC_SHADOW_policy_node_free
#define policy_node_match GRPC_SHADOW_policy_node_match
#define rand_fork_unsafe_buffering_enabled GRPC_SHADOW_rand_fork_unsafe_buffering_enabled
#define rsa_asn1_meth GRPC_SHADOW_rsa_asn1_meth
#define rsa_default_decrypt GRPC_SHADOW_rsa_default_decrypt
#define rsa_default_private_transform GRPC_SHADOW_rsa_default_private_transform
#define rsa_default_sign_raw GRPC_SHADOW_rsa_default_sign_raw
#define rsa_default_size GRPC_SHADOW_rsa_default_size
#define rsa_pkey_meth GRPC_SHADOW_rsa_pkey_meth
#define rsaz_1024_gather5_avx2 GRPC_SHADOW_rsaz_1024_gather5_avx2
#define rsaz_1024_mul_avx2 GRPC_SHADOW_rsaz_1024_mul_avx2
#define rsaz_1024_norm2red_avx2 GRPC_SHADOW_rsaz_1024_norm2red_avx2
#define rsaz_1024_red2norm_avx2 GRPC_SHADOW_rsaz_1024_red2norm_avx2
#define rsaz_1024_scatter5_avx2 GRPC_SHADOW_rsaz_1024_scatter5_avx2
#define rsaz_1024_sqr_avx2 GRPC_SHADOW_rsaz_1024_sqr_avx2
#define s2i_ASN1_INTEGER GRPC_SHADOW_s2i_ASN1_INTEGER
#define s2i_ASN1_OCTET_STRING GRPC_SHADOW_s2i_ASN1_OCTET_STRING
#define sdallocx GRPC_SHADOW_sdallocx
#define sha1_block_data_order GRPC_SHADOW_sha1_block_data_order
#define sha256_block_data_order GRPC_SHADOW_sha256_block_data_order
#define sha512_block_data_order GRPC_SHADOW_sha512_block_data_order
#define sk_CRYPTO_BUFFER_call_copy_func GRPC_SHADOW_sk_CRYPTO_BUFFER_call_copy_func
#define sk_CRYPTO_BUFFER_call_copy_func GRPC_SHADOW_sk_CRYPTO_BUFFER_call_copy_func
#define sk_CRYPTO_BUFFER_call_free_func GRPC_SHADOW_sk_CRYPTO_BUFFER_call_free_func
#define sk_CRYPTO_BUFFER_call_free_func GRPC_SHADOW_sk_CRYPTO_BUFFER_call_free_func
#define sk_CRYPTO_BUFFER_deep_copy GRPC_SHADOW_sk_CRYPTO_BUFFER_deep_copy
#define sk_CRYPTO_BUFFER_deep_copy GRPC_SHADOW_sk_CRYPTO_BUFFER_deep_copy
#define sk_CRYPTO_BUFFER_new_null GRPC_SHADOW_sk_CRYPTO_BUFFER_new_null
#define sk_CRYPTO_BUFFER_new_null GRPC_SHADOW_sk_CRYPTO_BUFFER_new_null
#define sk_CRYPTO_BUFFER_new_null GRPC_SHADOW_sk_CRYPTO_BUFFER_new_null
#define sk_CRYPTO_BUFFER_new_null GRPC_SHADOW_sk_CRYPTO_BUFFER_new_null
#define sk_CRYPTO_BUFFER_new_null GRPC_SHADOW_sk_CRYPTO_BUFFER_new_null
#define sk_CRYPTO_BUFFER_new_null GRPC_SHADOW_sk_CRYPTO_BUFFER_new_null
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_num GRPC_SHADOW_sk_CRYPTO_BUFFER_num
#define sk_CRYPTO_BUFFER_push GRPC_SHADOW_sk_CRYPTO_BUFFER_push
#define sk_CRYPTO_BUFFER_set GRPC_SHADOW_sk_CRYPTO_BUFFER_set
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_CRYPTO_BUFFER_value GRPC_SHADOW_sk_CRYPTO_BUFFER_value
#define sk_SRTP_PROTECTION_PROFILE_new_null GRPC_SHADOW_sk_SRTP_PROTECTION_PROFILE_new_null
#define sk_SRTP_PROTECTION_PROFILE_num GRPC_SHADOW_sk_SRTP_PROTECTION_PROFILE_num
#define sk_SRTP_PROTECTION_PROFILE_push GRPC_SHADOW_sk_SRTP_PROTECTION_PROFILE_push
#define sk_SSL_CIPHER_call_cmp_func GRPC_SHADOW_sk_SSL_CIPHER_call_cmp_func
#define sk_SSL_CIPHER_call_cmp_func GRPC_SHADOW_sk_SSL_CIPHER_call_cmp_func
#define sk_SSL_CIPHER_call_cmp_func GRPC_SHADOW_sk_SSL_CIPHER_call_cmp_func
#define sk_SSL_CIPHER_call_cmp_func GRPC_SHADOW_sk_SSL_CIPHER_call_cmp_func
#define sk_SSL_CIPHER_delete GRPC_SHADOW_sk_SSL_CIPHER_delete
#define sk_SSL_CIPHER_dup GRPC_SHADOW_sk_SSL_CIPHER_dup
#define sk_SSL_CIPHER_find GRPC_SHADOW_sk_SSL_CIPHER_find
#define sk_SSL_CIPHER_find GRPC_SHADOW_sk_SSL_CIPHER_find
#define sk_SSL_CIPHER_find GRPC_SHADOW_sk_SSL_CIPHER_find
#define sk_SSL_CIPHER_find GRPC_SHADOW_sk_SSL_CIPHER_find
#define sk_SSL_CIPHER_new_null GRPC_SHADOW_sk_SSL_CIPHER_new_null
#define sk_SSL_CIPHER_new_null GRPC_SHADOW_sk_SSL_CIPHER_new_null
#define sk_SSL_CIPHER_new_null GRPC_SHADOW_sk_SSL_CIPHER_new_null
#define sk_SSL_CIPHER_num GRPC_SHADOW_sk_SSL_CIPHER_num
#define sk_SSL_CIPHER_num GRPC_SHADOW_sk_SSL_CIPHER_num
#define sk_SSL_CIPHER_num GRPC_SHADOW_sk_SSL_CIPHER_num
#define sk_SSL_CIPHER_num GRPC_SHADOW_sk_SSL_CIPHER_num
#define sk_SSL_CIPHER_push GRPC_SHADOW_sk_SSL_CIPHER_push
#define sk_SSL_CIPHER_push GRPC_SHADOW_sk_SSL_CIPHER_push
#define sk_SSL_CIPHER_push GRPC_SHADOW_sk_SSL_CIPHER_push
#define sk_SSL_CIPHER_value GRPC_SHADOW_sk_SSL_CIPHER_value
#define sk_SSL_CIPHER_value GRPC_SHADOW_sk_SSL_CIPHER_value
#define sk_X509_NAME_call_cmp_func GRPC_SHADOW_sk_X509_NAME_call_cmp_func
#define sk_X509_NAME_call_copy_func GRPC_SHADOW_sk_X509_NAME_call_copy_func
#define sk_X509_NAME_call_free_func GRPC_SHADOW_sk_X509_NAME_call_free_func
#define sk_X509_NAME_call_free_func GRPC_SHADOW_sk_X509_NAME_call_free_func
#define sk_X509_NAME_deep_copy GRPC_SHADOW_sk_X509_NAME_deep_copy
#define sk_X509_NAME_find GRPC_SHADOW_sk_X509_NAME_find
#define sk_X509_NAME_free GRPC_SHADOW_sk_X509_NAME_free
#define sk_X509_NAME_new GRPC_SHADOW_sk_X509_NAME_new
#define sk_X509_NAME_new_null GRPC_SHADOW_sk_X509_NAME_new_null
#define sk_X509_NAME_new_null GRPC_SHADOW_sk_X509_NAME_new_null
#define sk_X509_NAME_pop_free GRPC_SHADOW_sk_X509_NAME_pop_free
#define sk_X509_NAME_pop_free GRPC_SHADOW_sk_X509_NAME_pop_free
#define sk_X509_NAME_push GRPC_SHADOW_sk_X509_NAME_push
#define sk_X509_NAME_set_cmp_func GRPC_SHADOW_sk_X509_NAME_set_cmp_func
#define sk_X509_NAME_sort GRPC_SHADOW_sk_X509_NAME_sort
#define sk_X509_call_free_func GRPC_SHADOW_sk_X509_call_free_func
#define sk_X509_new_null GRPC_SHADOW_sk_X509_new_null
#define sk_X509_num GRPC_SHADOW_sk_X509_num
#define sk_X509_pop_free GRPC_SHADOW_sk_X509_pop_free
#define sk_X509_shift GRPC_SHADOW_sk_X509_shift
#define sk_X509_value GRPC_SHADOW_sk_X509_value
#define sk_deep_copy GRPC_SHADOW_sk_deep_copy
#define sk_delete GRPC_SHADOW_sk_delete
#define sk_delete_ptr GRPC_SHADOW_sk_delete_ptr
#define sk_dup GRPC_SHADOW_sk_dup
#define sk_find GRPC_SHADOW_sk_find
#define sk_free GRPC_SHADOW_sk_free
#define sk_insert GRPC_SHADOW_sk_insert
#define sk_is_sorted GRPC_SHADOW_sk_is_sorted
#define sk_new GRPC_SHADOW_sk_new
#define sk_new_null GRPC_SHADOW_sk_new_null
#define sk_num GRPC_SHADOW_sk_num
#define sk_pop GRPC_SHADOW_sk_pop
#define sk_pop_free GRPC_SHADOW_sk_pop_free
#define sk_pop_free_ex GRPC_SHADOW_sk_pop_free_ex
#define sk_push GRPC_SHADOW_sk_push
#define sk_set GRPC_SHADOW_sk_set
#define sk_set_cmp_func GRPC_SHADOW_sk_set_cmp_func
#define sk_shift GRPC_SHADOW_sk_shift
#define sk_sort GRPC_SHADOW_sk_sort
#define sk_value GRPC_SHADOW_sk_value
#define sk_zero GRPC_SHADOW_sk_zero
#define tree_find_sk GRPC_SHADOW_tree_find_sk
#define v2i_ASN1_BIT_STRING GRPC_SHADOW_v2i_ASN1_BIT_STRING
#define v2i_GENERAL_NAME GRPC_SHADOW_v2i_GENERAL_NAME
#define v2i_GENERAL_NAMES GRPC_SHADOW_v2i_GENERAL_NAMES
#define v2i_GENERAL_NAME_ex GRPC_SHADOW_v2i_GENERAL_NAME_ex
#define v3_akey_id GRPC_SHADOW_v3_akey_id
#define v3_alt GRPC_SHADOW_v3_alt
#define v3_bcons GRPC_SHADOW_v3_bcons
#define v3_cpols GRPC_SHADOW_v3_cpols
#define v3_crl_invdate GRPC_SHADOW_v3_crl_invdate
#define v3_crl_num GRPC_SHADOW_v3_crl_num
#define v3_crl_reason GRPC_SHADOW_v3_crl_reason
#define v3_crld GRPC_SHADOW_v3_crld
#define v3_delta_crl GRPC_SHADOW_v3_delta_crl
#define v3_ext_ku GRPC_SHADOW_v3_ext_ku
#define v3_freshest_crl GRPC_SHADOW_v3_freshest_crl
#define v3_idp GRPC_SHADOW_v3_idp
#define v3_info GRPC_SHADOW_v3_info
#define v3_inhibit_anyp GRPC_SHADOW_v3_inhibit_anyp
#define v3_key_usage GRPC_SHADOW_v3_key_usage
#define v3_name_constraints GRPC_SHADOW_v3_name_constraints
#define v3_ns_ia5_list GRPC_SHADOW_v3_ns_ia5_list
#define v3_nscert GRPC_SHADOW_v3_nscert
#define v3_ocsp_accresp GRPC_SHADOW_v3_ocsp_accresp
#define v3_ocsp_nocheck GRPC_SHADOW_v3_ocsp_nocheck
#define v3_pci GRPC_SHADOW_v3_pci
#define v3_pkey_usage_period GRPC_SHADOW_v3_pkey_usage_period
#define v3_policy_constraints GRPC_SHADOW_v3_policy_constraints
#define v3_policy_mappings GRPC_SHADOW_v3_policy_mappings
#define v3_sinfo GRPC_SHADOW_v3_sinfo
#define v3_skey_id GRPC_SHADOW_v3_skey_id
#define v3_sxnet GRPC_SHADOW_v3_sxnet
#define vpaes_cbc_encrypt GRPC_SHADOW_vpaes_cbc_encrypt
#define vpaes_ctr32_encrypt_blocks GRPC_SHADOW_vpaes_ctr32_encrypt_blocks
#define vpaes_decrypt GRPC_SHADOW_vpaes_decrypt
#define vpaes_encrypt GRPC_SHADOW_vpaes_encrypt
#define vpaes_set_decrypt_key GRPC_SHADOW_vpaes_set_decrypt_key
#define vpaes_set_encrypt_key GRPC_SHADOW_vpaes_set_encrypt_key
#define x25519_asn1_meth GRPC_SHADOW_x25519_asn1_meth
#define x25519_ge_add GRPC_SHADOW_x25519_ge_add
#define x25519_ge_frombytes_vartime GRPC_SHADOW_x25519_ge_frombytes_vartime
#define x25519_ge_p1p1_to_p2 GRPC_SHADOW_x25519_ge_p1p1_to_p2
#define x25519_ge_p1p1_to_p3 GRPC_SHADOW_x25519_ge_p1p1_to_p3
#define x25519_ge_p3_to_cached GRPC_SHADOW_x25519_ge_p3_to_cached
#define x25519_ge_scalarmult GRPC_SHADOW_x25519_ge_scalarmult
#define x25519_ge_scalarmult_base GRPC_SHADOW_x25519_ge_scalarmult_base
#define x25519_ge_scalarmult_small_precomp GRPC_SHADOW_x25519_ge_scalarmult_small_precomp
#define x25519_ge_sub GRPC_SHADOW_x25519_ge_sub
#define x25519_ge_tobytes GRPC_SHADOW_x25519_ge_tobytes
#define x25519_pkey_meth GRPC_SHADOW_x25519_pkey_meth
#define x25519_sc_reduce GRPC_SHADOW_x25519_sc_reduce
#define x509_digest_sign_algorithm GRPC_SHADOW_x509_digest_sign_algorithm
#define x509_digest_verify_init GRPC_SHADOW_x509_digest_verify_init
#define x509_print_rsa_pss_params GRPC_SHADOW_x509_print_rsa_pss_params
#define x509_rsa_ctx_to_pss GRPC_SHADOW_x509_rsa_ctx_to_pss
#define x509_rsa_pss_to_ctx GRPC_SHADOW_x509_rsa_pss_to_ctx
#define x509v3_bytes_to_hex GRPC_SHADOW_x509v3_bytes_to_hex
#define x509v3_hex_to_bytes GRPC_SHADOW_x509v3_hex_to_bytes
#define x509v3_looks_like_dns_name GRPC_SHADOW_x509v3_looks_like_dns_name
#define x509v3_name_cmp GRPC_SHADOW_x509v3_name_cmp

#endif /* GRPC_SHADOW_BORINGSSL_SYMBOLS */

#endif /* GRPC_CORE_TSI_GRPC_SHADOW_BORINGSSL_H */
