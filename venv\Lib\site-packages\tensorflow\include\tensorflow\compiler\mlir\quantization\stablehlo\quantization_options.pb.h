// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/quantization/stablehlo/quantization_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto;
namespace stablehlo {
namespace quantization {
class CustomQuantizationMethod;
struct CustomQuantizationMethodDefaultTypeInternal;
extern CustomQuantizationMethodDefaultTypeInternal _CustomQuantizationMethod_default_instance_;
class PresetQuantizationMethod;
struct PresetQuantizationMethodDefaultTypeInternal;
extern PresetQuantizationMethodDefaultTypeInternal _PresetQuantizationMethod_default_instance_;
class QuantizationComponentSpec;
struct QuantizationComponentSpecDefaultTypeInternal;
extern QuantizationComponentSpecDefaultTypeInternal _QuantizationComponentSpec_default_instance_;
class QuantizationMethod;
struct QuantizationMethodDefaultTypeInternal;
extern QuantizationMethodDefaultTypeInternal _QuantizationMethod_default_instance_;
class QuantizationOptions;
struct QuantizationOptionsDefaultTypeInternal;
extern QuantizationOptionsDefaultTypeInternal _QuantizationOptions_default_instance_;
}  // namespace quantization
}  // namespace stablehlo
PROTOBUF_NAMESPACE_OPEN
template<> ::stablehlo::quantization::CustomQuantizationMethod* Arena::CreateMaybeMessage<::stablehlo::quantization::CustomQuantizationMethod>(Arena*);
template<> ::stablehlo::quantization::PresetQuantizationMethod* Arena::CreateMaybeMessage<::stablehlo::quantization::PresetQuantizationMethod>(Arena*);
template<> ::stablehlo::quantization::QuantizationComponentSpec* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationComponentSpec>(Arena*);
template<> ::stablehlo::quantization::QuantizationMethod* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationMethod>(Arena*);
template<> ::stablehlo::quantization::QuantizationOptions* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace stablehlo {
namespace quantization {

enum PresetQuantizationMethod_PresetMethod : int {
  PresetQuantizationMethod_PresetMethod_METHOD_UNSPECIFIED = 0,
  PresetQuantizationMethod_PresetMethod_WEIGHT_ONLY = 1,
  PresetQuantizationMethod_PresetMethod_POST_TRAINING_QUANTIZATION_DYNAMIC_RANGE = 2,
  PresetQuantizationMethod_PresetMethod_FLOAT16 = 3,
  PresetQuantizationMethod_PresetMethod_POST_TRAINING_QUANTIZATION_STATIC_RANGE = 4,
  PresetQuantizationMethod_PresetMethod_PresetQuantizationMethod_PresetMethod_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  PresetQuantizationMethod_PresetMethod_PresetQuantizationMethod_PresetMethod_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool PresetQuantizationMethod_PresetMethod_IsValid(int value);
constexpr PresetQuantizationMethod_PresetMethod PresetQuantizationMethod_PresetMethod_PresetMethod_MIN = PresetQuantizationMethod_PresetMethod_METHOD_UNSPECIFIED;
constexpr PresetQuantizationMethod_PresetMethod PresetQuantizationMethod_PresetMethod_PresetMethod_MAX = PresetQuantizationMethod_PresetMethod_POST_TRAINING_QUANTIZATION_STATIC_RANGE;
constexpr int PresetQuantizationMethod_PresetMethod_PresetMethod_ARRAYSIZE = PresetQuantizationMethod_PresetMethod_PresetMethod_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PresetQuantizationMethod_PresetMethod_descriptor();
template<typename T>
inline const std::string& PresetQuantizationMethod_PresetMethod_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PresetQuantizationMethod_PresetMethod>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PresetQuantizationMethod_PresetMethod_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PresetQuantizationMethod_PresetMethod_descriptor(), enum_t_value);
}
inline bool PresetQuantizationMethod_PresetMethod_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PresetQuantizationMethod_PresetMethod* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PresetQuantizationMethod_PresetMethod>(
    PresetQuantizationMethod_PresetMethod_descriptor(), name, value);
}
enum QuantizationComponentSpec_QuantizationComponent : int {
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_UNSPECIFIED = 0,
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_ACTIVATION = 1,
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_WEIGHT = 2,
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_BIAS = 3,
  QuantizationComponentSpec_QuantizationComponent_QuantizationComponentSpec_QuantizationComponent_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationComponentSpec_QuantizationComponent_QuantizationComponentSpec_QuantizationComponent_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationComponentSpec_QuantizationComponent_IsValid(int value);
constexpr QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MIN = QuantizationComponentSpec_QuantizationComponent_COMPONENT_UNSPECIFIED;
constexpr QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MAX = QuantizationComponentSpec_QuantizationComponent_COMPONENT_BIAS;
constexpr int QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_ARRAYSIZE = QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationComponentSpec_QuantizationComponent_descriptor();
template<typename T>
inline const std::string& QuantizationComponentSpec_QuantizationComponent_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationComponentSpec_QuantizationComponent>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationComponentSpec_QuantizationComponent_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationComponentSpec_QuantizationComponent_descriptor(), enum_t_value);
}
inline bool QuantizationComponentSpec_QuantizationComponent_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationComponentSpec_QuantizationComponent* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationComponentSpec_QuantizationComponent>(
    QuantizationComponentSpec_QuantizationComponent_descriptor(), name, value);
}
enum QuantizationComponentSpec_BitWidth : int {
  QuantizationComponentSpec_BitWidth_BIT_WIDTH_UNSPECIFIED = 0,
  QuantizationComponentSpec_BitWidth_BIT_WIDTH_4 = 1,
  QuantizationComponentSpec_BitWidth_BIT_WIDTH_8 = 2,
  QuantizationComponentSpec_BitWidth_BIT_WIDTH_16 = 3,
  QuantizationComponentSpec_BitWidth_BIT_WIDTH_32 = 4,
  QuantizationComponentSpec_BitWidth_QuantizationComponentSpec_BitWidth_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationComponentSpec_BitWidth_QuantizationComponentSpec_BitWidth_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationComponentSpec_BitWidth_IsValid(int value);
constexpr QuantizationComponentSpec_BitWidth QuantizationComponentSpec_BitWidth_BitWidth_MIN = QuantizationComponentSpec_BitWidth_BIT_WIDTH_UNSPECIFIED;
constexpr QuantizationComponentSpec_BitWidth QuantizationComponentSpec_BitWidth_BitWidth_MAX = QuantizationComponentSpec_BitWidth_BIT_WIDTH_32;
constexpr int QuantizationComponentSpec_BitWidth_BitWidth_ARRAYSIZE = QuantizationComponentSpec_BitWidth_BitWidth_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationComponentSpec_BitWidth_descriptor();
template<typename T>
inline const std::string& QuantizationComponentSpec_BitWidth_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationComponentSpec_BitWidth>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationComponentSpec_BitWidth_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationComponentSpec_BitWidth_descriptor(), enum_t_value);
}
inline bool QuantizationComponentSpec_BitWidth_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationComponentSpec_BitWidth* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationComponentSpec_BitWidth>(
    QuantizationComponentSpec_BitWidth_descriptor(), name, value);
}
enum QuantizationComponentSpec_BitType : int {
  QuantizationComponentSpec_BitType_BIT_TYPE_UNSPECIFIED = 0,
  QuantizationComponentSpec_BitType_BIT_TYPE_INT = 1,
  QuantizationComponentSpec_BitType_BIT_TYPE_FLOAT = 2,
  QuantizationComponentSpec_BitType_BIT_TYPE_BFLOAT = 3,
  QuantizationComponentSpec_BitType_QuantizationComponentSpec_BitType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationComponentSpec_BitType_QuantizationComponentSpec_BitType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationComponentSpec_BitType_IsValid(int value);
constexpr QuantizationComponentSpec_BitType QuantizationComponentSpec_BitType_BitType_MIN = QuantizationComponentSpec_BitType_BIT_TYPE_UNSPECIFIED;
constexpr QuantizationComponentSpec_BitType QuantizationComponentSpec_BitType_BitType_MAX = QuantizationComponentSpec_BitType_BIT_TYPE_BFLOAT;
constexpr int QuantizationComponentSpec_BitType_BitType_ARRAYSIZE = QuantizationComponentSpec_BitType_BitType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationComponentSpec_BitType_descriptor();
template<typename T>
inline const std::string& QuantizationComponentSpec_BitType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationComponentSpec_BitType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationComponentSpec_BitType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationComponentSpec_BitType_descriptor(), enum_t_value);
}
inline bool QuantizationComponentSpec_BitType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationComponentSpec_BitType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationComponentSpec_BitType>(
    QuantizationComponentSpec_BitType_descriptor(), name, value);
}
// ===================================================================

class QuantizationOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationOptions) */ {
 public:
  inline QuantizationOptions() : QuantizationOptions(nullptr) {}
  ~QuantizationOptions() override;
  explicit PROTOBUF_CONSTEXPR QuantizationOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationOptions(const QuantizationOptions& from);
  QuantizationOptions(QuantizationOptions&& from) noexcept
    : QuantizationOptions() {
    *this = ::std::move(from);
  }

  inline QuantizationOptions& operator=(const QuantizationOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationOptions& operator=(QuantizationOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationOptions* internal_default_instance() {
    return reinterpret_cast<const QuantizationOptions*>(
               &_QuantizationOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(QuantizationOptions& a, QuantizationOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationOptions& from) {
    QuantizationOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationOptions";
  }
  protected:
  explicit QuantizationOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQuantizationMethodFieldNumber = 1,
  };
  // .stablehlo.quantization.QuantizationMethod quantization_method = 1;
  bool has_quantization_method() const;
  private:
  bool _internal_has_quantization_method() const;
  public:
  void clear_quantization_method();
  const ::stablehlo::quantization::QuantizationMethod& quantization_method() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::QuantizationMethod* release_quantization_method();
  ::stablehlo::quantization::QuantizationMethod* mutable_quantization_method();
  void set_allocated_quantization_method(::stablehlo::quantization::QuantizationMethod* quantization_method);
  private:
  const ::stablehlo::quantization::QuantizationMethod& _internal_quantization_method() const;
  ::stablehlo::quantization::QuantizationMethod* _internal_mutable_quantization_method();
  public:
  void unsafe_arena_set_allocated_quantization_method(
      ::stablehlo::quantization::QuantizationMethod* quantization_method);
  ::stablehlo::quantization::QuantizationMethod* unsafe_arena_release_quantization_method();

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::stablehlo::quantization::QuantizationMethod* quantization_method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class QuantizationMethod final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationMethod) */ {
 public:
  inline QuantizationMethod() : QuantizationMethod(nullptr) {}
  ~QuantizationMethod() override;
  explicit PROTOBUF_CONSTEXPR QuantizationMethod(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationMethod(const QuantizationMethod& from);
  QuantizationMethod(QuantizationMethod&& from) noexcept
    : QuantizationMethod() {
    *this = ::std::move(from);
  }

  inline QuantizationMethod& operator=(const QuantizationMethod& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationMethod& operator=(QuantizationMethod&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationMethod& default_instance() {
    return *internal_default_instance();
  }
  enum QuantizationMethodCase {
    kPresetQuantizationMethod = 1,
    kCustomQuantizationMethod = 2,
    QUANTIZATION_METHOD_NOT_SET = 0,
  };

  static inline const QuantizationMethod* internal_default_instance() {
    return reinterpret_cast<const QuantizationMethod*>(
               &_QuantizationMethod_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(QuantizationMethod& a, QuantizationMethod& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationMethod* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationMethod* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationMethod* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationMethod>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationMethod& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationMethod& from) {
    QuantizationMethod::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationMethod* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationMethod";
  }
  protected:
  explicit QuantizationMethod(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPresetQuantizationMethodFieldNumber = 1,
    kCustomQuantizationMethodFieldNumber = 2,
  };
  // .stablehlo.quantization.PresetQuantizationMethod preset_quantization_method = 1;
  bool has_preset_quantization_method() const;
  private:
  bool _internal_has_preset_quantization_method() const;
  public:
  void clear_preset_quantization_method();
  const ::stablehlo::quantization::PresetQuantizationMethod& preset_quantization_method() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::PresetQuantizationMethod* release_preset_quantization_method();
  ::stablehlo::quantization::PresetQuantizationMethod* mutable_preset_quantization_method();
  void set_allocated_preset_quantization_method(::stablehlo::quantization::PresetQuantizationMethod* preset_quantization_method);
  private:
  const ::stablehlo::quantization::PresetQuantizationMethod& _internal_preset_quantization_method() const;
  ::stablehlo::quantization::PresetQuantizationMethod* _internal_mutable_preset_quantization_method();
  public:
  void unsafe_arena_set_allocated_preset_quantization_method(
      ::stablehlo::quantization::PresetQuantizationMethod* preset_quantization_method);
  ::stablehlo::quantization::PresetQuantizationMethod* unsafe_arena_release_preset_quantization_method();

  // .stablehlo.quantization.CustomQuantizationMethod custom_quantization_method = 2;
  bool has_custom_quantization_method() const;
  private:
  bool _internal_has_custom_quantization_method() const;
  public:
  void clear_custom_quantization_method();
  const ::stablehlo::quantization::CustomQuantizationMethod& custom_quantization_method() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::CustomQuantizationMethod* release_custom_quantization_method();
  ::stablehlo::quantization::CustomQuantizationMethod* mutable_custom_quantization_method();
  void set_allocated_custom_quantization_method(::stablehlo::quantization::CustomQuantizationMethod* custom_quantization_method);
  private:
  const ::stablehlo::quantization::CustomQuantizationMethod& _internal_custom_quantization_method() const;
  ::stablehlo::quantization::CustomQuantizationMethod* _internal_mutable_custom_quantization_method();
  public:
  void unsafe_arena_set_allocated_custom_quantization_method(
      ::stablehlo::quantization::CustomQuantizationMethod* custom_quantization_method);
  ::stablehlo::quantization::CustomQuantizationMethod* unsafe_arena_release_custom_quantization_method();

  void clear_quantization_method();
  QuantizationMethodCase quantization_method_case() const;
  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationMethod)
 private:
  class _Internal;
  void set_has_preset_quantization_method();
  void set_has_custom_quantization_method();

  inline bool has_quantization_method() const;
  inline void clear_has_quantization_method();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union QuantizationMethodUnion {
      constexpr QuantizationMethodUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stablehlo::quantization::PresetQuantizationMethod* preset_quantization_method_;
      ::stablehlo::quantization::CustomQuantizationMethod* custom_quantization_method_;
    } quantization_method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class PresetQuantizationMethod final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.PresetQuantizationMethod) */ {
 public:
  inline PresetQuantizationMethod() : PresetQuantizationMethod(nullptr) {}
  ~PresetQuantizationMethod() override;
  explicit PROTOBUF_CONSTEXPR PresetQuantizationMethod(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PresetQuantizationMethod(const PresetQuantizationMethod& from);
  PresetQuantizationMethod(PresetQuantizationMethod&& from) noexcept
    : PresetQuantizationMethod() {
    *this = ::std::move(from);
  }

  inline PresetQuantizationMethod& operator=(const PresetQuantizationMethod& from) {
    CopyFrom(from);
    return *this;
  }
  inline PresetQuantizationMethod& operator=(PresetQuantizationMethod&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PresetQuantizationMethod& default_instance() {
    return *internal_default_instance();
  }
  static inline const PresetQuantizationMethod* internal_default_instance() {
    return reinterpret_cast<const PresetQuantizationMethod*>(
               &_PresetQuantizationMethod_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(PresetQuantizationMethod& a, PresetQuantizationMethod& b) {
    a.Swap(&b);
  }
  inline void Swap(PresetQuantizationMethod* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PresetQuantizationMethod* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PresetQuantizationMethod* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PresetQuantizationMethod>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PresetQuantizationMethod& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PresetQuantizationMethod& from) {
    PresetQuantizationMethod::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PresetQuantizationMethod* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.PresetQuantizationMethod";
  }
  protected:
  explicit PresetQuantizationMethod(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef PresetQuantizationMethod_PresetMethod PresetMethod;
  static constexpr PresetMethod METHOD_UNSPECIFIED =
    PresetQuantizationMethod_PresetMethod_METHOD_UNSPECIFIED;
  static constexpr PresetMethod WEIGHT_ONLY =
    PresetQuantizationMethod_PresetMethod_WEIGHT_ONLY;
  static constexpr PresetMethod POST_TRAINING_QUANTIZATION_DYNAMIC_RANGE =
    PresetQuantizationMethod_PresetMethod_POST_TRAINING_QUANTIZATION_DYNAMIC_RANGE;
  static constexpr PresetMethod FLOAT16 =
    PresetQuantizationMethod_PresetMethod_FLOAT16;
  static constexpr PresetMethod POST_TRAINING_QUANTIZATION_STATIC_RANGE =
    PresetQuantizationMethod_PresetMethod_POST_TRAINING_QUANTIZATION_STATIC_RANGE;
  static inline bool PresetMethod_IsValid(int value) {
    return PresetQuantizationMethod_PresetMethod_IsValid(value);
  }
  static constexpr PresetMethod PresetMethod_MIN =
    PresetQuantizationMethod_PresetMethod_PresetMethod_MIN;
  static constexpr PresetMethod PresetMethod_MAX =
    PresetQuantizationMethod_PresetMethod_PresetMethod_MAX;
  static constexpr int PresetMethod_ARRAYSIZE =
    PresetQuantizationMethod_PresetMethod_PresetMethod_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  PresetMethod_descriptor() {
    return PresetQuantizationMethod_PresetMethod_descriptor();
  }
  template<typename T>
  static inline const std::string& PresetMethod_Name(T enum_t_value) {
    static_assert(::std::is_same<T, PresetMethod>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function PresetMethod_Name.");
    return PresetQuantizationMethod_PresetMethod_Name(enum_t_value);
  }
  static inline bool PresetMethod_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      PresetMethod* value) {
    return PresetQuantizationMethod_PresetMethod_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kPresetMethodFieldNumber = 1,
  };
  // .stablehlo.quantization.PresetQuantizationMethod.PresetMethod preset_method = 1;
  void clear_preset_method();
  ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod preset_method() const;
  void set_preset_method(::stablehlo::quantization::PresetQuantizationMethod_PresetMethod value);
  private:
  ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod _internal_preset_method() const;
  void _internal_set_preset_method(::stablehlo::quantization::PresetQuantizationMethod_PresetMethod value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.PresetQuantizationMethod)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int preset_method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class CustomQuantizationMethod final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.CustomQuantizationMethod) */ {
 public:
  inline CustomQuantizationMethod() : CustomQuantizationMethod(nullptr) {}
  ~CustomQuantizationMethod() override;
  explicit PROTOBUF_CONSTEXPR CustomQuantizationMethod(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CustomQuantizationMethod(const CustomQuantizationMethod& from);
  CustomQuantizationMethod(CustomQuantizationMethod&& from) noexcept
    : CustomQuantizationMethod() {
    *this = ::std::move(from);
  }

  inline CustomQuantizationMethod& operator=(const CustomQuantizationMethod& from) {
    CopyFrom(from);
    return *this;
  }
  inline CustomQuantizationMethod& operator=(CustomQuantizationMethod&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CustomQuantizationMethod& default_instance() {
    return *internal_default_instance();
  }
  static inline const CustomQuantizationMethod* internal_default_instance() {
    return reinterpret_cast<const CustomQuantizationMethod*>(
               &_CustomQuantizationMethod_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CustomQuantizationMethod& a, CustomQuantizationMethod& b) {
    a.Swap(&b);
  }
  inline void Swap(CustomQuantizationMethod* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CustomQuantizationMethod* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CustomQuantizationMethod* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CustomQuantizationMethod>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CustomQuantizationMethod& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CustomQuantizationMethod& from) {
    CustomQuantizationMethod::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CustomQuantizationMethod* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.CustomQuantizationMethod";
  }
  protected:
  explicit CustomQuantizationMethod(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQuantizationComponentSpecFieldNumber = 1,
  };
  // repeated .stablehlo.quantization.QuantizationComponentSpec quantization_component_spec = 1;
  int quantization_component_spec_size() const;
  private:
  int _internal_quantization_component_spec_size() const;
  public:
  void clear_quantization_component_spec();
  ::stablehlo::quantization::QuantizationComponentSpec* mutable_quantization_component_spec(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationComponentSpec >*
      mutable_quantization_component_spec();
  private:
  const ::stablehlo::quantization::QuantizationComponentSpec& _internal_quantization_component_spec(int index) const;
  ::stablehlo::quantization::QuantizationComponentSpec* _internal_add_quantization_component_spec();
  public:
  const ::stablehlo::quantization::QuantizationComponentSpec& quantization_component_spec(int index) const;
  ::stablehlo::quantization::QuantizationComponentSpec* add_quantization_component_spec();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationComponentSpec >&
      quantization_component_spec() const;

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.CustomQuantizationMethod)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationComponentSpec > quantization_component_spec_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class QuantizationComponentSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationComponentSpec) */ {
 public:
  inline QuantizationComponentSpec() : QuantizationComponentSpec(nullptr) {}
  ~QuantizationComponentSpec() override;
  explicit PROTOBUF_CONSTEXPR QuantizationComponentSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationComponentSpec(const QuantizationComponentSpec& from);
  QuantizationComponentSpec(QuantizationComponentSpec&& from) noexcept
    : QuantizationComponentSpec() {
    *this = ::std::move(from);
  }

  inline QuantizationComponentSpec& operator=(const QuantizationComponentSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationComponentSpec& operator=(QuantizationComponentSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationComponentSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationComponentSpec* internal_default_instance() {
    return reinterpret_cast<const QuantizationComponentSpec*>(
               &_QuantizationComponentSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(QuantizationComponentSpec& a, QuantizationComponentSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationComponentSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationComponentSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationComponentSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationComponentSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationComponentSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationComponentSpec& from) {
    QuantizationComponentSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationComponentSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationComponentSpec";
  }
  protected:
  explicit QuantizationComponentSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef QuantizationComponentSpec_QuantizationComponent QuantizationComponent;
  static constexpr QuantizationComponent COMPONENT_UNSPECIFIED =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_UNSPECIFIED;
  static constexpr QuantizationComponent COMPONENT_ACTIVATION =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_ACTIVATION;
  static constexpr QuantizationComponent COMPONENT_WEIGHT =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_WEIGHT;
  static constexpr QuantizationComponent COMPONENT_BIAS =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_BIAS;
  static inline bool QuantizationComponent_IsValid(int value) {
    return QuantizationComponentSpec_QuantizationComponent_IsValid(value);
  }
  static constexpr QuantizationComponent QuantizationComponent_MIN =
    QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MIN;
  static constexpr QuantizationComponent QuantizationComponent_MAX =
    QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MAX;
  static constexpr int QuantizationComponent_ARRAYSIZE =
    QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  QuantizationComponent_descriptor() {
    return QuantizationComponentSpec_QuantizationComponent_descriptor();
  }
  template<typename T>
  static inline const std::string& QuantizationComponent_Name(T enum_t_value) {
    static_assert(::std::is_same<T, QuantizationComponent>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function QuantizationComponent_Name.");
    return QuantizationComponentSpec_QuantizationComponent_Name(enum_t_value);
  }
  static inline bool QuantizationComponent_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      QuantizationComponent* value) {
    return QuantizationComponentSpec_QuantizationComponent_Parse(name, value);
  }

  typedef QuantizationComponentSpec_BitWidth BitWidth;
  static constexpr BitWidth BIT_WIDTH_UNSPECIFIED =
    QuantizationComponentSpec_BitWidth_BIT_WIDTH_UNSPECIFIED;
  static constexpr BitWidth BIT_WIDTH_4 =
    QuantizationComponentSpec_BitWidth_BIT_WIDTH_4;
  static constexpr BitWidth BIT_WIDTH_8 =
    QuantizationComponentSpec_BitWidth_BIT_WIDTH_8;
  static constexpr BitWidth BIT_WIDTH_16 =
    QuantizationComponentSpec_BitWidth_BIT_WIDTH_16;
  static constexpr BitWidth BIT_WIDTH_32 =
    QuantizationComponentSpec_BitWidth_BIT_WIDTH_32;
  static inline bool BitWidth_IsValid(int value) {
    return QuantizationComponentSpec_BitWidth_IsValid(value);
  }
  static constexpr BitWidth BitWidth_MIN =
    QuantizationComponentSpec_BitWidth_BitWidth_MIN;
  static constexpr BitWidth BitWidth_MAX =
    QuantizationComponentSpec_BitWidth_BitWidth_MAX;
  static constexpr int BitWidth_ARRAYSIZE =
    QuantizationComponentSpec_BitWidth_BitWidth_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  BitWidth_descriptor() {
    return QuantizationComponentSpec_BitWidth_descriptor();
  }
  template<typename T>
  static inline const std::string& BitWidth_Name(T enum_t_value) {
    static_assert(::std::is_same<T, BitWidth>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function BitWidth_Name.");
    return QuantizationComponentSpec_BitWidth_Name(enum_t_value);
  }
  static inline bool BitWidth_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      BitWidth* value) {
    return QuantizationComponentSpec_BitWidth_Parse(name, value);
  }

  typedef QuantizationComponentSpec_BitType BitType;
  static constexpr BitType BIT_TYPE_UNSPECIFIED =
    QuantizationComponentSpec_BitType_BIT_TYPE_UNSPECIFIED;
  static constexpr BitType BIT_TYPE_INT =
    QuantizationComponentSpec_BitType_BIT_TYPE_INT;
  static constexpr BitType BIT_TYPE_FLOAT =
    QuantizationComponentSpec_BitType_BIT_TYPE_FLOAT;
  static constexpr BitType BIT_TYPE_BFLOAT =
    QuantizationComponentSpec_BitType_BIT_TYPE_BFLOAT;
  static inline bool BitType_IsValid(int value) {
    return QuantizationComponentSpec_BitType_IsValid(value);
  }
  static constexpr BitType BitType_MIN =
    QuantizationComponentSpec_BitType_BitType_MIN;
  static constexpr BitType BitType_MAX =
    QuantizationComponentSpec_BitType_BitType_MAX;
  static constexpr int BitType_ARRAYSIZE =
    QuantizationComponentSpec_BitType_BitType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  BitType_descriptor() {
    return QuantizationComponentSpec_BitType_descriptor();
  }
  template<typename T>
  static inline const std::string& BitType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, BitType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function BitType_Name.");
    return QuantizationComponentSpec_BitType_Name(enum_t_value);
  }
  static inline bool BitType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      BitType* value) {
    return QuantizationComponentSpec_BitType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kQuantizationComponentFieldNumber = 1,
    kBitWidthFieldNumber = 2,
    kBitTypeFieldNumber = 3,
    kEnableNarrowRangeFieldNumber = 4,
    kEnablePerChannelQuantizationFieldNumber = 5,
    kEnableSymmetricFieldNumber = 6,
  };
  // .stablehlo.quantization.QuantizationComponentSpec.QuantizationComponent quantization_component = 1;
  void clear_quantization_component();
  ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent quantization_component() const;
  void set_quantization_component(::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent value);
  private:
  ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent _internal_quantization_component() const;
  void _internal_set_quantization_component(::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent value);
  public:

  // .stablehlo.quantization.QuantizationComponentSpec.BitWidth bit_width = 2;
  void clear_bit_width();
  ::stablehlo::quantization::QuantizationComponentSpec_BitWidth bit_width() const;
  void set_bit_width(::stablehlo::quantization::QuantizationComponentSpec_BitWidth value);
  private:
  ::stablehlo::quantization::QuantizationComponentSpec_BitWidth _internal_bit_width() const;
  void _internal_set_bit_width(::stablehlo::quantization::QuantizationComponentSpec_BitWidth value);
  public:

  // .stablehlo.quantization.QuantizationComponentSpec.BitType bit_type = 3;
  void clear_bit_type();
  ::stablehlo::quantization::QuantizationComponentSpec_BitType bit_type() const;
  void set_bit_type(::stablehlo::quantization::QuantizationComponentSpec_BitType value);
  private:
  ::stablehlo::quantization::QuantizationComponentSpec_BitType _internal_bit_type() const;
  void _internal_set_bit_type(::stablehlo::quantization::QuantizationComponentSpec_BitType value);
  public:

  // bool enable_narrow_range = 4;
  void clear_enable_narrow_range();
  bool enable_narrow_range() const;
  void set_enable_narrow_range(bool value);
  private:
  bool _internal_enable_narrow_range() const;
  void _internal_set_enable_narrow_range(bool value);
  public:

  // bool enable_per_channel_quantization = 5;
  void clear_enable_per_channel_quantization();
  bool enable_per_channel_quantization() const;
  void set_enable_per_channel_quantization(bool value);
  private:
  bool _internal_enable_per_channel_quantization() const;
  void _internal_set_enable_per_channel_quantization(bool value);
  public:

  // bool enable_symmetric = 6;
  void clear_enable_symmetric();
  bool enable_symmetric() const;
  void set_enable_symmetric(bool value);
  private:
  bool _internal_enable_symmetric() const;
  void _internal_set_enable_symmetric(bool value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationComponentSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int quantization_component_;
    int bit_width_;
    int bit_type_;
    bool enable_narrow_range_;
    bool enable_per_channel_quantization_;
    bool enable_symmetric_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// QuantizationOptions

// .stablehlo.quantization.QuantizationMethod quantization_method = 1;
inline bool QuantizationOptions::_internal_has_quantization_method() const {
  return this != internal_default_instance() && _impl_.quantization_method_ != nullptr;
}
inline bool QuantizationOptions::has_quantization_method() const {
  return _internal_has_quantization_method();
}
inline void QuantizationOptions::clear_quantization_method() {
  if (GetArenaForAllocation() == nullptr && _impl_.quantization_method_ != nullptr) {
    delete _impl_.quantization_method_;
  }
  _impl_.quantization_method_ = nullptr;
}
inline const ::stablehlo::quantization::QuantizationMethod& QuantizationOptions::_internal_quantization_method() const {
  const ::stablehlo::quantization::QuantizationMethod* p = _impl_.quantization_method_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::QuantizationMethod&>(
      ::stablehlo::quantization::_QuantizationMethod_default_instance_);
}
inline const ::stablehlo::quantization::QuantizationMethod& QuantizationOptions::quantization_method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationOptions.quantization_method)
  return _internal_quantization_method();
}
inline void QuantizationOptions::unsafe_arena_set_allocated_quantization_method(
    ::stablehlo::quantization::QuantizationMethod* quantization_method) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_method_);
  }
  _impl_.quantization_method_ = quantization_method;
  if (quantization_method) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationOptions.quantization_method)
}
inline ::stablehlo::quantization::QuantizationMethod* QuantizationOptions::release_quantization_method() {
  
  ::stablehlo::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::QuantizationMethod* QuantizationOptions::unsafe_arena_release_quantization_method() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationOptions.quantization_method)
  
  ::stablehlo::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::QuantizationMethod* QuantizationOptions::_internal_mutable_quantization_method() {
  
  if (_impl_.quantization_method_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::QuantizationMethod>(GetArenaForAllocation());
    _impl_.quantization_method_ = p;
  }
  return _impl_.quantization_method_;
}
inline ::stablehlo::quantization::QuantizationMethod* QuantizationOptions::mutable_quantization_method() {
  ::stablehlo::quantization::QuantizationMethod* _msg = _internal_mutable_quantization_method();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationOptions.quantization_method)
  return _msg;
}
inline void QuantizationOptions::set_allocated_quantization_method(::stablehlo::quantization::QuantizationMethod* quantization_method) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.quantization_method_;
  }
  if (quantization_method) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(quantization_method);
    if (message_arena != submessage_arena) {
      quantization_method = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantization_method, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.quantization_method_ = quantization_method;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationOptions.quantization_method)
}

// -------------------------------------------------------------------

// QuantizationMethod

// .stablehlo.quantization.PresetQuantizationMethod preset_quantization_method = 1;
inline bool QuantizationMethod::_internal_has_preset_quantization_method() const {
  return quantization_method_case() == kPresetQuantizationMethod;
}
inline bool QuantizationMethod::has_preset_quantization_method() const {
  return _internal_has_preset_quantization_method();
}
inline void QuantizationMethod::set_has_preset_quantization_method() {
  _impl_._oneof_case_[0] = kPresetQuantizationMethod;
}
inline void QuantizationMethod::clear_preset_quantization_method() {
  if (_internal_has_preset_quantization_method()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.quantization_method_.preset_quantization_method_;
    }
    clear_has_quantization_method();
  }
}
inline ::stablehlo::quantization::PresetQuantizationMethod* QuantizationMethod::release_preset_quantization_method() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationMethod.preset_quantization_method)
  if (_internal_has_preset_quantization_method()) {
    clear_has_quantization_method();
    ::stablehlo::quantization::PresetQuantizationMethod* temp = _impl_.quantization_method_.preset_quantization_method_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.quantization_method_.preset_quantization_method_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::PresetQuantizationMethod& QuantizationMethod::_internal_preset_quantization_method() const {
  return _internal_has_preset_quantization_method()
      ? *_impl_.quantization_method_.preset_quantization_method_
      : reinterpret_cast< ::stablehlo::quantization::PresetQuantizationMethod&>(::stablehlo::quantization::_PresetQuantizationMethod_default_instance_);
}
inline const ::stablehlo::quantization::PresetQuantizationMethod& QuantizationMethod::preset_quantization_method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationMethod.preset_quantization_method)
  return _internal_preset_quantization_method();
}
inline ::stablehlo::quantization::PresetQuantizationMethod* QuantizationMethod::unsafe_arena_release_preset_quantization_method() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.QuantizationMethod.preset_quantization_method)
  if (_internal_has_preset_quantization_method()) {
    clear_has_quantization_method();
    ::stablehlo::quantization::PresetQuantizationMethod* temp = _impl_.quantization_method_.preset_quantization_method_;
    _impl_.quantization_method_.preset_quantization_method_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QuantizationMethod::unsafe_arena_set_allocated_preset_quantization_method(::stablehlo::quantization::PresetQuantizationMethod* preset_quantization_method) {
  clear_quantization_method();
  if (preset_quantization_method) {
    set_has_preset_quantization_method();
    _impl_.quantization_method_.preset_quantization_method_ = preset_quantization_method;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationMethod.preset_quantization_method)
}
inline ::stablehlo::quantization::PresetQuantizationMethod* QuantizationMethod::_internal_mutable_preset_quantization_method() {
  if (!_internal_has_preset_quantization_method()) {
    clear_quantization_method();
    set_has_preset_quantization_method();
    _impl_.quantization_method_.preset_quantization_method_ = CreateMaybeMessage< ::stablehlo::quantization::PresetQuantizationMethod >(GetArenaForAllocation());
  }
  return _impl_.quantization_method_.preset_quantization_method_;
}
inline ::stablehlo::quantization::PresetQuantizationMethod* QuantizationMethod::mutable_preset_quantization_method() {
  ::stablehlo::quantization::PresetQuantizationMethod* _msg = _internal_mutable_preset_quantization_method();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationMethod.preset_quantization_method)
  return _msg;
}

// .stablehlo.quantization.CustomQuantizationMethod custom_quantization_method = 2;
inline bool QuantizationMethod::_internal_has_custom_quantization_method() const {
  return quantization_method_case() == kCustomQuantizationMethod;
}
inline bool QuantizationMethod::has_custom_quantization_method() const {
  return _internal_has_custom_quantization_method();
}
inline void QuantizationMethod::set_has_custom_quantization_method() {
  _impl_._oneof_case_[0] = kCustomQuantizationMethod;
}
inline void QuantizationMethod::clear_custom_quantization_method() {
  if (_internal_has_custom_quantization_method()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.quantization_method_.custom_quantization_method_;
    }
    clear_has_quantization_method();
  }
}
inline ::stablehlo::quantization::CustomQuantizationMethod* QuantizationMethod::release_custom_quantization_method() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationMethod.custom_quantization_method)
  if (_internal_has_custom_quantization_method()) {
    clear_has_quantization_method();
    ::stablehlo::quantization::CustomQuantizationMethod* temp = _impl_.quantization_method_.custom_quantization_method_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.quantization_method_.custom_quantization_method_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::CustomQuantizationMethod& QuantizationMethod::_internal_custom_quantization_method() const {
  return _internal_has_custom_quantization_method()
      ? *_impl_.quantization_method_.custom_quantization_method_
      : reinterpret_cast< ::stablehlo::quantization::CustomQuantizationMethod&>(::stablehlo::quantization::_CustomQuantizationMethod_default_instance_);
}
inline const ::stablehlo::quantization::CustomQuantizationMethod& QuantizationMethod::custom_quantization_method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationMethod.custom_quantization_method)
  return _internal_custom_quantization_method();
}
inline ::stablehlo::quantization::CustomQuantizationMethod* QuantizationMethod::unsafe_arena_release_custom_quantization_method() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.QuantizationMethod.custom_quantization_method)
  if (_internal_has_custom_quantization_method()) {
    clear_has_quantization_method();
    ::stablehlo::quantization::CustomQuantizationMethod* temp = _impl_.quantization_method_.custom_quantization_method_;
    _impl_.quantization_method_.custom_quantization_method_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QuantizationMethod::unsafe_arena_set_allocated_custom_quantization_method(::stablehlo::quantization::CustomQuantizationMethod* custom_quantization_method) {
  clear_quantization_method();
  if (custom_quantization_method) {
    set_has_custom_quantization_method();
    _impl_.quantization_method_.custom_quantization_method_ = custom_quantization_method;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationMethod.custom_quantization_method)
}
inline ::stablehlo::quantization::CustomQuantizationMethod* QuantizationMethod::_internal_mutable_custom_quantization_method() {
  if (!_internal_has_custom_quantization_method()) {
    clear_quantization_method();
    set_has_custom_quantization_method();
    _impl_.quantization_method_.custom_quantization_method_ = CreateMaybeMessage< ::stablehlo::quantization::CustomQuantizationMethod >(GetArenaForAllocation());
  }
  return _impl_.quantization_method_.custom_quantization_method_;
}
inline ::stablehlo::quantization::CustomQuantizationMethod* QuantizationMethod::mutable_custom_quantization_method() {
  ::stablehlo::quantization::CustomQuantizationMethod* _msg = _internal_mutable_custom_quantization_method();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationMethod.custom_quantization_method)
  return _msg;
}

inline bool QuantizationMethod::has_quantization_method() const {
  return quantization_method_case() != QUANTIZATION_METHOD_NOT_SET;
}
inline void QuantizationMethod::clear_has_quantization_method() {
  _impl_._oneof_case_[0] = QUANTIZATION_METHOD_NOT_SET;
}
inline QuantizationMethod::QuantizationMethodCase QuantizationMethod::quantization_method_case() const {
  return QuantizationMethod::QuantizationMethodCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// PresetQuantizationMethod

// .stablehlo.quantization.PresetQuantizationMethod.PresetMethod preset_method = 1;
inline void PresetQuantizationMethod::clear_preset_method() {
  _impl_.preset_method_ = 0;
}
inline ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod PresetQuantizationMethod::_internal_preset_method() const {
  return static_cast< ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod >(_impl_.preset_method_);
}
inline ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod PresetQuantizationMethod::preset_method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.PresetQuantizationMethod.preset_method)
  return _internal_preset_method();
}
inline void PresetQuantizationMethod::_internal_set_preset_method(::stablehlo::quantization::PresetQuantizationMethod_PresetMethod value) {
  
  _impl_.preset_method_ = value;
}
inline void PresetQuantizationMethod::set_preset_method(::stablehlo::quantization::PresetQuantizationMethod_PresetMethod value) {
  _internal_set_preset_method(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.PresetQuantizationMethod.preset_method)
}

// -------------------------------------------------------------------

// CustomQuantizationMethod

// repeated .stablehlo.quantization.QuantizationComponentSpec quantization_component_spec = 1;
inline int CustomQuantizationMethod::_internal_quantization_component_spec_size() const {
  return _impl_.quantization_component_spec_.size();
}
inline int CustomQuantizationMethod::quantization_component_spec_size() const {
  return _internal_quantization_component_spec_size();
}
inline void CustomQuantizationMethod::clear_quantization_component_spec() {
  _impl_.quantization_component_spec_.Clear();
}
inline ::stablehlo::quantization::QuantizationComponentSpec* CustomQuantizationMethod::mutable_quantization_component_spec(int index) {
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.CustomQuantizationMethod.quantization_component_spec)
  return _impl_.quantization_component_spec_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationComponentSpec >*
CustomQuantizationMethod::mutable_quantization_component_spec() {
  // @@protoc_insertion_point(field_mutable_list:stablehlo.quantization.CustomQuantizationMethod.quantization_component_spec)
  return &_impl_.quantization_component_spec_;
}
inline const ::stablehlo::quantization::QuantizationComponentSpec& CustomQuantizationMethod::_internal_quantization_component_spec(int index) const {
  return _impl_.quantization_component_spec_.Get(index);
}
inline const ::stablehlo::quantization::QuantizationComponentSpec& CustomQuantizationMethod::quantization_component_spec(int index) const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CustomQuantizationMethod.quantization_component_spec)
  return _internal_quantization_component_spec(index);
}
inline ::stablehlo::quantization::QuantizationComponentSpec* CustomQuantizationMethod::_internal_add_quantization_component_spec() {
  return _impl_.quantization_component_spec_.Add();
}
inline ::stablehlo::quantization::QuantizationComponentSpec* CustomQuantizationMethod::add_quantization_component_spec() {
  ::stablehlo::quantization::QuantizationComponentSpec* _add = _internal_add_quantization_component_spec();
  // @@protoc_insertion_point(field_add:stablehlo.quantization.CustomQuantizationMethod.quantization_component_spec)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationComponentSpec >&
CustomQuantizationMethod::quantization_component_spec() const {
  // @@protoc_insertion_point(field_list:stablehlo.quantization.CustomQuantizationMethod.quantization_component_spec)
  return _impl_.quantization_component_spec_;
}

// -------------------------------------------------------------------

// QuantizationComponentSpec

// .stablehlo.quantization.QuantizationComponentSpec.QuantizationComponent quantization_component = 1;
inline void QuantizationComponentSpec::clear_quantization_component() {
  _impl_.quantization_component_ = 0;
}
inline ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec::_internal_quantization_component() const {
  return static_cast< ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent >(_impl_.quantization_component_);
}
inline ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec::quantization_component() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationComponentSpec.quantization_component)
  return _internal_quantization_component();
}
inline void QuantizationComponentSpec::_internal_set_quantization_component(::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent value) {
  
  _impl_.quantization_component_ = value;
}
inline void QuantizationComponentSpec::set_quantization_component(::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent value) {
  _internal_set_quantization_component(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationComponentSpec.quantization_component)
}

// .stablehlo.quantization.QuantizationComponentSpec.BitWidth bit_width = 2;
inline void QuantizationComponentSpec::clear_bit_width() {
  _impl_.bit_width_ = 0;
}
inline ::stablehlo::quantization::QuantizationComponentSpec_BitWidth QuantizationComponentSpec::_internal_bit_width() const {
  return static_cast< ::stablehlo::quantization::QuantizationComponentSpec_BitWidth >(_impl_.bit_width_);
}
inline ::stablehlo::quantization::QuantizationComponentSpec_BitWidth QuantizationComponentSpec::bit_width() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationComponentSpec.bit_width)
  return _internal_bit_width();
}
inline void QuantizationComponentSpec::_internal_set_bit_width(::stablehlo::quantization::QuantizationComponentSpec_BitWidth value) {
  
  _impl_.bit_width_ = value;
}
inline void QuantizationComponentSpec::set_bit_width(::stablehlo::quantization::QuantizationComponentSpec_BitWidth value) {
  _internal_set_bit_width(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationComponentSpec.bit_width)
}

// .stablehlo.quantization.QuantizationComponentSpec.BitType bit_type = 3;
inline void QuantizationComponentSpec::clear_bit_type() {
  _impl_.bit_type_ = 0;
}
inline ::stablehlo::quantization::QuantizationComponentSpec_BitType QuantizationComponentSpec::_internal_bit_type() const {
  return static_cast< ::stablehlo::quantization::QuantizationComponentSpec_BitType >(_impl_.bit_type_);
}
inline ::stablehlo::quantization::QuantizationComponentSpec_BitType QuantizationComponentSpec::bit_type() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationComponentSpec.bit_type)
  return _internal_bit_type();
}
inline void QuantizationComponentSpec::_internal_set_bit_type(::stablehlo::quantization::QuantizationComponentSpec_BitType value) {
  
  _impl_.bit_type_ = value;
}
inline void QuantizationComponentSpec::set_bit_type(::stablehlo::quantization::QuantizationComponentSpec_BitType value) {
  _internal_set_bit_type(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationComponentSpec.bit_type)
}

// bool enable_narrow_range = 4;
inline void QuantizationComponentSpec::clear_enable_narrow_range() {
  _impl_.enable_narrow_range_ = false;
}
inline bool QuantizationComponentSpec::_internal_enable_narrow_range() const {
  return _impl_.enable_narrow_range_;
}
inline bool QuantizationComponentSpec::enable_narrow_range() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationComponentSpec.enable_narrow_range)
  return _internal_enable_narrow_range();
}
inline void QuantizationComponentSpec::_internal_set_enable_narrow_range(bool value) {
  
  _impl_.enable_narrow_range_ = value;
}
inline void QuantizationComponentSpec::set_enable_narrow_range(bool value) {
  _internal_set_enable_narrow_range(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationComponentSpec.enable_narrow_range)
}

// bool enable_per_channel_quantization = 5;
inline void QuantizationComponentSpec::clear_enable_per_channel_quantization() {
  _impl_.enable_per_channel_quantization_ = false;
}
inline bool QuantizationComponentSpec::_internal_enable_per_channel_quantization() const {
  return _impl_.enable_per_channel_quantization_;
}
inline bool QuantizationComponentSpec::enable_per_channel_quantization() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationComponentSpec.enable_per_channel_quantization)
  return _internal_enable_per_channel_quantization();
}
inline void QuantizationComponentSpec::_internal_set_enable_per_channel_quantization(bool value) {
  
  _impl_.enable_per_channel_quantization_ = value;
}
inline void QuantizationComponentSpec::set_enable_per_channel_quantization(bool value) {
  _internal_set_enable_per_channel_quantization(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationComponentSpec.enable_per_channel_quantization)
}

// bool enable_symmetric = 6;
inline void QuantizationComponentSpec::clear_enable_symmetric() {
  _impl_.enable_symmetric_ = false;
}
inline bool QuantizationComponentSpec::_internal_enable_symmetric() const {
  return _impl_.enable_symmetric_;
}
inline bool QuantizationComponentSpec::enable_symmetric() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationComponentSpec.enable_symmetric)
  return _internal_enable_symmetric();
}
inline void QuantizationComponentSpec::_internal_set_enable_symmetric(bool value) {
  
  _impl_.enable_symmetric_ = value;
}
inline void QuantizationComponentSpec::set_enable_symmetric(bool value) {
  _internal_set_enable_symmetric(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationComponentSpec.enable_symmetric)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace quantization
}  // namespace stablehlo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod>() {
  return ::stablehlo::quantization::PresetQuantizationMethod_PresetMethod_descriptor();
}
template <> struct is_proto_enum< ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent>() {
  return ::stablehlo::quantization::QuantizationComponentSpec_QuantizationComponent_descriptor();
}
template <> struct is_proto_enum< ::stablehlo::quantization::QuantizationComponentSpec_BitWidth> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stablehlo::quantization::QuantizationComponentSpec_BitWidth>() {
  return ::stablehlo::quantization::QuantizationComponentSpec_BitWidth_descriptor();
}
template <> struct is_proto_enum< ::stablehlo::quantization::QuantizationComponentSpec_BitType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stablehlo::quantization::QuantizationComponentSpec_BitType>() {
  return ::stablehlo::quantization::QuantizationComponentSpec_BitType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5foptions_2eproto
