# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

def AddStep(arg0: int, arg1: str, arg2: str, arg3: str) -> float: ...
def DeleteProfiler() -> None: ...
def NewProfiler(arg0: str, arg1: str) -> bool: ...
def PrintModelAnalysis(arg0: str, arg1: str, arg2: str, arg3: str, arg4: str) -> bytes: ...
def Profile(arg0: str, arg1: str) -> bytes: ...
def ProfilerFromFile(arg0: str) -> None: ...
def SerializeToString() -> bytes: ...
def WriteProfile(arg0: str) -> None: ...
