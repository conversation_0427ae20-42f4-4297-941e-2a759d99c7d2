/* This file was generated by upbc (the upb compiler) from the input
 * file:
 *
 *     src/proto/grpc/gcp/handshaker.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#ifndef SRC_PROTO_GRPC_GCP_HANDSHAKER_PROTO_UPB_H_
#define SRC_PROTO_GRPC_GCP_HANDSHAKER_PROTO_UPB_H_

#include "upb/generated_util.h"
#include "upb/msg.h"
#include "upb/decode.h"
#include "upb/encode.h"

#include "upb/port_def.inc"

#ifdef __cplusplus
extern "C" {
#endif

struct grpc_gcp_Endpoint;
struct grpc_gcp_Identity;
struct grpc_gcp_Identity_AttributesEntry;
struct grpc_gcp_StartClientHandshakeReq;
struct grpc_gcp_ServerHandshakeParameters;
struct grpc_gcp_StartServerHandshakeReq;
struct grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry;
struct grpc_gcp_NextHandshakeMessageReq;
struct grpc_gcp_HandshakerReq;
struct grpc_gcp_HandshakerResult;
struct grpc_gcp_HandshakerStatus;
struct grpc_gcp_HandshakerResp;
typedef struct grpc_gcp_Endpoint grpc_gcp_Endpoint;
typedef struct grpc_gcp_Identity grpc_gcp_Identity;
typedef struct grpc_gcp_Identity_AttributesEntry grpc_gcp_Identity_AttributesEntry;
typedef struct grpc_gcp_StartClientHandshakeReq grpc_gcp_StartClientHandshakeReq;
typedef struct grpc_gcp_ServerHandshakeParameters grpc_gcp_ServerHandshakeParameters;
typedef struct grpc_gcp_StartServerHandshakeReq grpc_gcp_StartServerHandshakeReq;
typedef struct grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry;
typedef struct grpc_gcp_NextHandshakeMessageReq grpc_gcp_NextHandshakeMessageReq;
typedef struct grpc_gcp_HandshakerReq grpc_gcp_HandshakerReq;
typedef struct grpc_gcp_HandshakerResult grpc_gcp_HandshakerResult;
typedef struct grpc_gcp_HandshakerStatus grpc_gcp_HandshakerStatus;
typedef struct grpc_gcp_HandshakerResp grpc_gcp_HandshakerResp;
extern const upb_msglayout grpc_gcp_Endpoint_msginit;
extern const upb_msglayout grpc_gcp_Identity_msginit;
extern const upb_msglayout grpc_gcp_Identity_AttributesEntry_msginit;
extern const upb_msglayout grpc_gcp_StartClientHandshakeReq_msginit;
extern const upb_msglayout grpc_gcp_ServerHandshakeParameters_msginit;
extern const upb_msglayout grpc_gcp_StartServerHandshakeReq_msginit;
extern const upb_msglayout grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_msginit;
extern const upb_msglayout grpc_gcp_NextHandshakeMessageReq_msginit;
extern const upb_msglayout grpc_gcp_HandshakerReq_msginit;
extern const upb_msglayout grpc_gcp_HandshakerResult_msginit;
extern const upb_msglayout grpc_gcp_HandshakerStatus_msginit;
extern const upb_msglayout grpc_gcp_HandshakerResp_msginit;
struct grpc_gcp_RpcProtocolVersions;
extern const upb_msglayout grpc_gcp_RpcProtocolVersions_msginit;

typedef enum {
  grpc_gcp_HANDSHAKE_PROTOCOL_UNSPECIFIED = 0,
  grpc_gcp_TLS = 1,
  grpc_gcp_ALTS = 2
} grpc_gcp_HandshakeProtocol;

typedef enum {
  grpc_gcp_NETWORK_PROTOCOL_UNSPECIFIED = 0,
  grpc_gcp_TCP = 1,
  grpc_gcp_UDP = 2
} grpc_gcp_NetworkProtocol;


/* grpc.gcp.Endpoint */

UPB_INLINE grpc_gcp_Endpoint *grpc_gcp_Endpoint_new(upb_arena *arena) {
  return (grpc_gcp_Endpoint *)upb_msg_new(&grpc_gcp_Endpoint_msginit, arena);
}
UPB_INLINE grpc_gcp_Endpoint *grpc_gcp_Endpoint_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_Endpoint *ret = grpc_gcp_Endpoint_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_Endpoint_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_Endpoint_serialize(const grpc_gcp_Endpoint *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_Endpoint_msginit, arena, len);
}

UPB_INLINE upb_strview grpc_gcp_Endpoint_ip_address(const grpc_gcp_Endpoint *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(12, 16)); }
UPB_INLINE int32_t grpc_gcp_Endpoint_port(const grpc_gcp_Endpoint *msg) { return UPB_FIELD_AT(msg, int32_t, UPB_SIZE(8, 8)); }
UPB_INLINE int32_t grpc_gcp_Endpoint_protocol(const grpc_gcp_Endpoint *msg) { return UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)); }

UPB_INLINE void grpc_gcp_Endpoint_set_ip_address(grpc_gcp_Endpoint *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(12, 16)) = value;
}
UPB_INLINE void grpc_gcp_Endpoint_set_port(grpc_gcp_Endpoint *msg, int32_t value) {
  UPB_FIELD_AT(msg, int32_t, UPB_SIZE(8, 8)) = value;
}
UPB_INLINE void grpc_gcp_Endpoint_set_protocol(grpc_gcp_Endpoint *msg, int32_t value) {
  UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)) = value;
}

/* grpc.gcp.Identity */

UPB_INLINE grpc_gcp_Identity *grpc_gcp_Identity_new(upb_arena *arena) {
  return (grpc_gcp_Identity *)upb_msg_new(&grpc_gcp_Identity_msginit, arena);
}
UPB_INLINE grpc_gcp_Identity *grpc_gcp_Identity_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_Identity *ret = grpc_gcp_Identity_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_Identity_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_Identity_serialize(const grpc_gcp_Identity *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_Identity_msginit, arena, len);
}

typedef enum {
  grpc_gcp_Identity_identity_oneof_service_account = 1,
  grpc_gcp_Identity_identity_oneof_hostname = 2,
  grpc_gcp_Identity_identity_oneof_NOT_SET = 0
} grpc_gcp_Identity_identity_oneof_oneofcases;
UPB_INLINE grpc_gcp_Identity_identity_oneof_oneofcases grpc_gcp_Identity_identity_oneof_case(const grpc_gcp_Identity* msg) { return (grpc_gcp_Identity_identity_oneof_oneofcases)UPB_FIELD_AT(msg, int32_t, UPB_SIZE(12, 24)); }

UPB_INLINE bool grpc_gcp_Identity_has_service_account(const grpc_gcp_Identity *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(12, 24), 1); }
UPB_INLINE upb_strview grpc_gcp_Identity_service_account(const grpc_gcp_Identity *msg) { return UPB_READ_ONEOF(msg, upb_strview, UPB_SIZE(4, 8), UPB_SIZE(12, 24), 1, upb_strview_make("", strlen(""))); }
UPB_INLINE bool grpc_gcp_Identity_has_hostname(const grpc_gcp_Identity *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(12, 24), 2); }
UPB_INLINE upb_strview grpc_gcp_Identity_hostname(const grpc_gcp_Identity *msg) { return UPB_READ_ONEOF(msg, upb_strview, UPB_SIZE(4, 8), UPB_SIZE(12, 24), 2, upb_strview_make("", strlen(""))); }
UPB_INLINE const grpc_gcp_Identity_AttributesEntry* const* grpc_gcp_Identity_attributes(const grpc_gcp_Identity *msg, size_t *len) { return (const grpc_gcp_Identity_AttributesEntry* const*)_upb_array_accessor(msg, UPB_SIZE(0, 0), len); }

UPB_INLINE void grpc_gcp_Identity_set_service_account(grpc_gcp_Identity *msg, upb_strview value) {
  UPB_WRITE_ONEOF(msg, upb_strview, UPB_SIZE(4, 8), value, UPB_SIZE(12, 24), 1);
}
UPB_INLINE void grpc_gcp_Identity_set_hostname(grpc_gcp_Identity *msg, upb_strview value) {
  UPB_WRITE_ONEOF(msg, upb_strview, UPB_SIZE(4, 8), value, UPB_SIZE(12, 24), 2);
}
UPB_INLINE grpc_gcp_Identity_AttributesEntry** grpc_gcp_Identity_mutable_attributes(grpc_gcp_Identity *msg, size_t *len) {
  return (grpc_gcp_Identity_AttributesEntry**)_upb_array_mutable_accessor(msg, UPB_SIZE(0, 0), len);
}
UPB_INLINE grpc_gcp_Identity_AttributesEntry** grpc_gcp_Identity_resize_attributes(grpc_gcp_Identity *msg, size_t len, upb_arena *arena) {
  return (grpc_gcp_Identity_AttributesEntry**)_upb_array_resize_accessor(msg, UPB_SIZE(0, 0), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct grpc_gcp_Identity_AttributesEntry* grpc_gcp_Identity_add_attributes(grpc_gcp_Identity *msg, upb_arena *arena) {
  struct grpc_gcp_Identity_AttributesEntry* sub = (struct grpc_gcp_Identity_AttributesEntry*)upb_msg_new(&grpc_gcp_Identity_AttributesEntry_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(0, 0), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}

/* grpc.gcp.Identity.AttributesEntry */

UPB_INLINE grpc_gcp_Identity_AttributesEntry *grpc_gcp_Identity_AttributesEntry_new(upb_arena *arena) {
  return (grpc_gcp_Identity_AttributesEntry *)upb_msg_new(&grpc_gcp_Identity_AttributesEntry_msginit, arena);
}
UPB_INLINE grpc_gcp_Identity_AttributesEntry *grpc_gcp_Identity_AttributesEntry_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_Identity_AttributesEntry *ret = grpc_gcp_Identity_AttributesEntry_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_Identity_AttributesEntry_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_Identity_AttributesEntry_serialize(const grpc_gcp_Identity_AttributesEntry *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_Identity_AttributesEntry_msginit, arena, len);
}

UPB_INLINE upb_strview grpc_gcp_Identity_AttributesEntry_key(const grpc_gcp_Identity_AttributesEntry *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }
UPB_INLINE upb_strview grpc_gcp_Identity_AttributesEntry_value(const grpc_gcp_Identity_AttributesEntry *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 16)); }

UPB_INLINE void grpc_gcp_Identity_AttributesEntry_set_key(grpc_gcp_Identity_AttributesEntry *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void grpc_gcp_Identity_AttributesEntry_set_value(grpc_gcp_Identity_AttributesEntry *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 16)) = value;
}

/* grpc.gcp.StartClientHandshakeReq */

UPB_INLINE grpc_gcp_StartClientHandshakeReq *grpc_gcp_StartClientHandshakeReq_new(upb_arena *arena) {
  return (grpc_gcp_StartClientHandshakeReq *)upb_msg_new(&grpc_gcp_StartClientHandshakeReq_msginit, arena);
}
UPB_INLINE grpc_gcp_StartClientHandshakeReq *grpc_gcp_StartClientHandshakeReq_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_StartClientHandshakeReq *ret = grpc_gcp_StartClientHandshakeReq_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_StartClientHandshakeReq_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_StartClientHandshakeReq_serialize(const grpc_gcp_StartClientHandshakeReq *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_StartClientHandshakeReq_msginit, arena, len);
}

UPB_INLINE int32_t grpc_gcp_StartClientHandshakeReq_handshake_security_protocol(const grpc_gcp_StartClientHandshakeReq *msg) { return UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)); }
UPB_INLINE upb_strview const* grpc_gcp_StartClientHandshakeReq_application_protocols(const grpc_gcp_StartClientHandshakeReq *msg, size_t *len) { return (upb_strview const*)_upb_array_accessor(msg, UPB_SIZE(32, 56), len); }
UPB_INLINE upb_strview const* grpc_gcp_StartClientHandshakeReq_record_protocols(const grpc_gcp_StartClientHandshakeReq *msg, size_t *len) { return (upb_strview const*)_upb_array_accessor(msg, UPB_SIZE(36, 64), len); }
UPB_INLINE const grpc_gcp_Identity* const* grpc_gcp_StartClientHandshakeReq_target_identities(const grpc_gcp_StartClientHandshakeReq *msg, size_t *len) { return (const grpc_gcp_Identity* const*)_upb_array_accessor(msg, UPB_SIZE(40, 72), len); }
UPB_INLINE const grpc_gcp_Identity* grpc_gcp_StartClientHandshakeReq_local_identity(const grpc_gcp_StartClientHandshakeReq *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Identity*, UPB_SIZE(16, 24)); }
UPB_INLINE const grpc_gcp_Endpoint* grpc_gcp_StartClientHandshakeReq_local_endpoint(const grpc_gcp_StartClientHandshakeReq *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Endpoint*, UPB_SIZE(20, 32)); }
UPB_INLINE const grpc_gcp_Endpoint* grpc_gcp_StartClientHandshakeReq_remote_endpoint(const grpc_gcp_StartClientHandshakeReq *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Endpoint*, UPB_SIZE(24, 40)); }
UPB_INLINE upb_strview grpc_gcp_StartClientHandshakeReq_target_name(const grpc_gcp_StartClientHandshakeReq *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 8)); }
UPB_INLINE const struct grpc_gcp_RpcProtocolVersions* grpc_gcp_StartClientHandshakeReq_rpc_versions(const grpc_gcp_StartClientHandshakeReq *msg) { return UPB_FIELD_AT(msg, const struct grpc_gcp_RpcProtocolVersions*, UPB_SIZE(28, 48)); }

UPB_INLINE void grpc_gcp_StartClientHandshakeReq_set_handshake_security_protocol(grpc_gcp_StartClientHandshakeReq *msg, int32_t value) {
  UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE upb_strview* grpc_gcp_StartClientHandshakeReq_mutable_application_protocols(grpc_gcp_StartClientHandshakeReq *msg, size_t *len) {
  return (upb_strview*)_upb_array_mutable_accessor(msg, UPB_SIZE(32, 56), len);
}
UPB_INLINE upb_strview* grpc_gcp_StartClientHandshakeReq_resize_application_protocols(grpc_gcp_StartClientHandshakeReq *msg, size_t len, upb_arena *arena) {
  return (upb_strview*)_upb_array_resize_accessor(msg, UPB_SIZE(32, 56), len, UPB_SIZE(8, 16), UPB_TYPE_STRING, arena);
}
UPB_INLINE bool grpc_gcp_StartClientHandshakeReq_add_application_protocols(grpc_gcp_StartClientHandshakeReq *msg, upb_strview val, upb_arena *arena) {
  return _upb_array_append_accessor(
      msg, UPB_SIZE(32, 56), UPB_SIZE(8, 16), UPB_TYPE_STRING, &val, arena);
}
UPB_INLINE upb_strview* grpc_gcp_StartClientHandshakeReq_mutable_record_protocols(grpc_gcp_StartClientHandshakeReq *msg, size_t *len) {
  return (upb_strview*)_upb_array_mutable_accessor(msg, UPB_SIZE(36, 64), len);
}
UPB_INLINE upb_strview* grpc_gcp_StartClientHandshakeReq_resize_record_protocols(grpc_gcp_StartClientHandshakeReq *msg, size_t len, upb_arena *arena) {
  return (upb_strview*)_upb_array_resize_accessor(msg, UPB_SIZE(36, 64), len, UPB_SIZE(8, 16), UPB_TYPE_STRING, arena);
}
UPB_INLINE bool grpc_gcp_StartClientHandshakeReq_add_record_protocols(grpc_gcp_StartClientHandshakeReq *msg, upb_strview val, upb_arena *arena) {
  return _upb_array_append_accessor(
      msg, UPB_SIZE(36, 64), UPB_SIZE(8, 16), UPB_TYPE_STRING, &val, arena);
}
UPB_INLINE grpc_gcp_Identity** grpc_gcp_StartClientHandshakeReq_mutable_target_identities(grpc_gcp_StartClientHandshakeReq *msg, size_t *len) {
  return (grpc_gcp_Identity**)_upb_array_mutable_accessor(msg, UPB_SIZE(40, 72), len);
}
UPB_INLINE grpc_gcp_Identity** grpc_gcp_StartClientHandshakeReq_resize_target_identities(grpc_gcp_StartClientHandshakeReq *msg, size_t len, upb_arena *arena) {
  return (grpc_gcp_Identity**)_upb_array_resize_accessor(msg, UPB_SIZE(40, 72), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct grpc_gcp_Identity* grpc_gcp_StartClientHandshakeReq_add_target_identities(grpc_gcp_StartClientHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_Identity* sub = (struct grpc_gcp_Identity*)upb_msg_new(&grpc_gcp_Identity_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(40, 72), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}
UPB_INLINE void grpc_gcp_StartClientHandshakeReq_set_local_identity(grpc_gcp_StartClientHandshakeReq *msg, grpc_gcp_Identity* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Identity*, UPB_SIZE(16, 24)) = value;
}
UPB_INLINE struct grpc_gcp_Identity* grpc_gcp_StartClientHandshakeReq_mutable_local_identity(grpc_gcp_StartClientHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_Identity* sub = (struct grpc_gcp_Identity*)grpc_gcp_StartClientHandshakeReq_local_identity(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Identity*)upb_msg_new(&grpc_gcp_Identity_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartClientHandshakeReq_set_local_identity(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_StartClientHandshakeReq_set_local_endpoint(grpc_gcp_StartClientHandshakeReq *msg, grpc_gcp_Endpoint* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Endpoint*, UPB_SIZE(20, 32)) = value;
}
UPB_INLINE struct grpc_gcp_Endpoint* grpc_gcp_StartClientHandshakeReq_mutable_local_endpoint(grpc_gcp_StartClientHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_Endpoint* sub = (struct grpc_gcp_Endpoint*)grpc_gcp_StartClientHandshakeReq_local_endpoint(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Endpoint*)upb_msg_new(&grpc_gcp_Endpoint_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartClientHandshakeReq_set_local_endpoint(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_StartClientHandshakeReq_set_remote_endpoint(grpc_gcp_StartClientHandshakeReq *msg, grpc_gcp_Endpoint* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Endpoint*, UPB_SIZE(24, 40)) = value;
}
UPB_INLINE struct grpc_gcp_Endpoint* grpc_gcp_StartClientHandshakeReq_mutable_remote_endpoint(grpc_gcp_StartClientHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_Endpoint* sub = (struct grpc_gcp_Endpoint*)grpc_gcp_StartClientHandshakeReq_remote_endpoint(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Endpoint*)upb_msg_new(&grpc_gcp_Endpoint_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartClientHandshakeReq_set_remote_endpoint(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_StartClientHandshakeReq_set_target_name(grpc_gcp_StartClientHandshakeReq *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(8, 8)) = value;
}
UPB_INLINE void grpc_gcp_StartClientHandshakeReq_set_rpc_versions(grpc_gcp_StartClientHandshakeReq *msg, struct grpc_gcp_RpcProtocolVersions* value) {
  UPB_FIELD_AT(msg, struct grpc_gcp_RpcProtocolVersions*, UPB_SIZE(28, 48)) = value;
}
UPB_INLINE struct grpc_gcp_RpcProtocolVersions* grpc_gcp_StartClientHandshakeReq_mutable_rpc_versions(grpc_gcp_StartClientHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_RpcProtocolVersions* sub = (struct grpc_gcp_RpcProtocolVersions*)grpc_gcp_StartClientHandshakeReq_rpc_versions(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_RpcProtocolVersions*)upb_msg_new(&grpc_gcp_RpcProtocolVersions_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartClientHandshakeReq_set_rpc_versions(msg, sub);
  }
  return sub;
}

/* grpc.gcp.ServerHandshakeParameters */

UPB_INLINE grpc_gcp_ServerHandshakeParameters *grpc_gcp_ServerHandshakeParameters_new(upb_arena *arena) {
  return (grpc_gcp_ServerHandshakeParameters *)upb_msg_new(&grpc_gcp_ServerHandshakeParameters_msginit, arena);
}
UPB_INLINE grpc_gcp_ServerHandshakeParameters *grpc_gcp_ServerHandshakeParameters_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_ServerHandshakeParameters *ret = grpc_gcp_ServerHandshakeParameters_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_ServerHandshakeParameters_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_ServerHandshakeParameters_serialize(const grpc_gcp_ServerHandshakeParameters *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_ServerHandshakeParameters_msginit, arena, len);
}

UPB_INLINE upb_strview const* grpc_gcp_ServerHandshakeParameters_record_protocols(const grpc_gcp_ServerHandshakeParameters *msg, size_t *len) { return (upb_strview const*)_upb_array_accessor(msg, UPB_SIZE(0, 0), len); }
UPB_INLINE const grpc_gcp_Identity* const* grpc_gcp_ServerHandshakeParameters_local_identities(const grpc_gcp_ServerHandshakeParameters *msg, size_t *len) { return (const grpc_gcp_Identity* const*)_upb_array_accessor(msg, UPB_SIZE(4, 8), len); }

UPB_INLINE upb_strview* grpc_gcp_ServerHandshakeParameters_mutable_record_protocols(grpc_gcp_ServerHandshakeParameters *msg, size_t *len) {
  return (upb_strview*)_upb_array_mutable_accessor(msg, UPB_SIZE(0, 0), len);
}
UPB_INLINE upb_strview* grpc_gcp_ServerHandshakeParameters_resize_record_protocols(grpc_gcp_ServerHandshakeParameters *msg, size_t len, upb_arena *arena) {
  return (upb_strview*)_upb_array_resize_accessor(msg, UPB_SIZE(0, 0), len, UPB_SIZE(8, 16), UPB_TYPE_STRING, arena);
}
UPB_INLINE bool grpc_gcp_ServerHandshakeParameters_add_record_protocols(grpc_gcp_ServerHandshakeParameters *msg, upb_strview val, upb_arena *arena) {
  return _upb_array_append_accessor(
      msg, UPB_SIZE(0, 0), UPB_SIZE(8, 16), UPB_TYPE_STRING, &val, arena);
}
UPB_INLINE grpc_gcp_Identity** grpc_gcp_ServerHandshakeParameters_mutable_local_identities(grpc_gcp_ServerHandshakeParameters *msg, size_t *len) {
  return (grpc_gcp_Identity**)_upb_array_mutable_accessor(msg, UPB_SIZE(4, 8), len);
}
UPB_INLINE grpc_gcp_Identity** grpc_gcp_ServerHandshakeParameters_resize_local_identities(grpc_gcp_ServerHandshakeParameters *msg, size_t len, upb_arena *arena) {
  return (grpc_gcp_Identity**)_upb_array_resize_accessor(msg, UPB_SIZE(4, 8), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct grpc_gcp_Identity* grpc_gcp_ServerHandshakeParameters_add_local_identities(grpc_gcp_ServerHandshakeParameters *msg, upb_arena *arena) {
  struct grpc_gcp_Identity* sub = (struct grpc_gcp_Identity*)upb_msg_new(&grpc_gcp_Identity_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(4, 8), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}

/* grpc.gcp.StartServerHandshakeReq */

UPB_INLINE grpc_gcp_StartServerHandshakeReq *grpc_gcp_StartServerHandshakeReq_new(upb_arena *arena) {
  return (grpc_gcp_StartServerHandshakeReq *)upb_msg_new(&grpc_gcp_StartServerHandshakeReq_msginit, arena);
}
UPB_INLINE grpc_gcp_StartServerHandshakeReq *grpc_gcp_StartServerHandshakeReq_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_StartServerHandshakeReq *ret = grpc_gcp_StartServerHandshakeReq_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_StartServerHandshakeReq_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_StartServerHandshakeReq_serialize(const grpc_gcp_StartServerHandshakeReq *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_StartServerHandshakeReq_msginit, arena, len);
}

UPB_INLINE upb_strview const* grpc_gcp_StartServerHandshakeReq_application_protocols(const grpc_gcp_StartServerHandshakeReq *msg, size_t *len) { return (upb_strview const*)_upb_array_accessor(msg, UPB_SIZE(20, 40), len); }
UPB_INLINE const grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry* const* grpc_gcp_StartServerHandshakeReq_handshake_parameters(const grpc_gcp_StartServerHandshakeReq *msg, size_t *len) { return (const grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry* const*)_upb_array_accessor(msg, UPB_SIZE(24, 48), len); }
UPB_INLINE upb_strview grpc_gcp_StartServerHandshakeReq_in_bytes(const grpc_gcp_StartServerHandshakeReq *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }
UPB_INLINE const grpc_gcp_Endpoint* grpc_gcp_StartServerHandshakeReq_local_endpoint(const grpc_gcp_StartServerHandshakeReq *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Endpoint*, UPB_SIZE(8, 16)); }
UPB_INLINE const grpc_gcp_Endpoint* grpc_gcp_StartServerHandshakeReq_remote_endpoint(const grpc_gcp_StartServerHandshakeReq *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Endpoint*, UPB_SIZE(12, 24)); }
UPB_INLINE const struct grpc_gcp_RpcProtocolVersions* grpc_gcp_StartServerHandshakeReq_rpc_versions(const grpc_gcp_StartServerHandshakeReq *msg) { return UPB_FIELD_AT(msg, const struct grpc_gcp_RpcProtocolVersions*, UPB_SIZE(16, 32)); }

UPB_INLINE upb_strview* grpc_gcp_StartServerHandshakeReq_mutable_application_protocols(grpc_gcp_StartServerHandshakeReq *msg, size_t *len) {
  return (upb_strview*)_upb_array_mutable_accessor(msg, UPB_SIZE(20, 40), len);
}
UPB_INLINE upb_strview* grpc_gcp_StartServerHandshakeReq_resize_application_protocols(grpc_gcp_StartServerHandshakeReq *msg, size_t len, upb_arena *arena) {
  return (upb_strview*)_upb_array_resize_accessor(msg, UPB_SIZE(20, 40), len, UPB_SIZE(8, 16), UPB_TYPE_STRING, arena);
}
UPB_INLINE bool grpc_gcp_StartServerHandshakeReq_add_application_protocols(grpc_gcp_StartServerHandshakeReq *msg, upb_strview val, upb_arena *arena) {
  return _upb_array_append_accessor(
      msg, UPB_SIZE(20, 40), UPB_SIZE(8, 16), UPB_TYPE_STRING, &val, arena);
}
UPB_INLINE grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry** grpc_gcp_StartServerHandshakeReq_mutable_handshake_parameters(grpc_gcp_StartServerHandshakeReq *msg, size_t *len) {
  return (grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry**)_upb_array_mutable_accessor(msg, UPB_SIZE(24, 48), len);
}
UPB_INLINE grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry** grpc_gcp_StartServerHandshakeReq_resize_handshake_parameters(grpc_gcp_StartServerHandshakeReq *msg, size_t len, upb_arena *arena) {
  return (grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry**)_upb_array_resize_accessor(msg, UPB_SIZE(24, 48), len, UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, arena);
}
UPB_INLINE struct grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry* grpc_gcp_StartServerHandshakeReq_add_handshake_parameters(grpc_gcp_StartServerHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry* sub = (struct grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry*)upb_msg_new(&grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_msginit, arena);
  bool ok = _upb_array_append_accessor(
      msg, UPB_SIZE(24, 48), UPB_SIZE(4, 8), UPB_TYPE_MESSAGE, &sub, arena);
  if (!ok) return NULL;
  return sub;
}
UPB_INLINE void grpc_gcp_StartServerHandshakeReq_set_in_bytes(grpc_gcp_StartServerHandshakeReq *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void grpc_gcp_StartServerHandshakeReq_set_local_endpoint(grpc_gcp_StartServerHandshakeReq *msg, grpc_gcp_Endpoint* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Endpoint*, UPB_SIZE(8, 16)) = value;
}
UPB_INLINE struct grpc_gcp_Endpoint* grpc_gcp_StartServerHandshakeReq_mutable_local_endpoint(grpc_gcp_StartServerHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_Endpoint* sub = (struct grpc_gcp_Endpoint*)grpc_gcp_StartServerHandshakeReq_local_endpoint(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Endpoint*)upb_msg_new(&grpc_gcp_Endpoint_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartServerHandshakeReq_set_local_endpoint(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_StartServerHandshakeReq_set_remote_endpoint(grpc_gcp_StartServerHandshakeReq *msg, grpc_gcp_Endpoint* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Endpoint*, UPB_SIZE(12, 24)) = value;
}
UPB_INLINE struct grpc_gcp_Endpoint* grpc_gcp_StartServerHandshakeReq_mutable_remote_endpoint(grpc_gcp_StartServerHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_Endpoint* sub = (struct grpc_gcp_Endpoint*)grpc_gcp_StartServerHandshakeReq_remote_endpoint(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Endpoint*)upb_msg_new(&grpc_gcp_Endpoint_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartServerHandshakeReq_set_remote_endpoint(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_StartServerHandshakeReq_set_rpc_versions(grpc_gcp_StartServerHandshakeReq *msg, struct grpc_gcp_RpcProtocolVersions* value) {
  UPB_FIELD_AT(msg, struct grpc_gcp_RpcProtocolVersions*, UPB_SIZE(16, 32)) = value;
}
UPB_INLINE struct grpc_gcp_RpcProtocolVersions* grpc_gcp_StartServerHandshakeReq_mutable_rpc_versions(grpc_gcp_StartServerHandshakeReq *msg, upb_arena *arena) {
  struct grpc_gcp_RpcProtocolVersions* sub = (struct grpc_gcp_RpcProtocolVersions*)grpc_gcp_StartServerHandshakeReq_rpc_versions(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_RpcProtocolVersions*)upb_msg_new(&grpc_gcp_RpcProtocolVersions_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartServerHandshakeReq_set_rpc_versions(msg, sub);
  }
  return sub;
}

/* grpc.gcp.StartServerHandshakeReq.HandshakeParametersEntry */

UPB_INLINE grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_new(upb_arena *arena) {
  return (grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *)upb_msg_new(&grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_msginit, arena);
}
UPB_INLINE grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *ret = grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_serialize(const grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_msginit, arena, len);
}

UPB_INLINE int32_t grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_key(const grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *msg) { return UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)); }
UPB_INLINE const grpc_gcp_ServerHandshakeParameters* grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_value(const grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_ServerHandshakeParameters*, UPB_SIZE(4, 8)); }

UPB_INLINE void grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_set_key(grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *msg, int32_t value) {
  UPB_FIELD_AT(msg, int32_t, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_set_value(grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *msg, grpc_gcp_ServerHandshakeParameters* value) {
  UPB_FIELD_AT(msg, grpc_gcp_ServerHandshakeParameters*, UPB_SIZE(4, 8)) = value;
}
UPB_INLINE struct grpc_gcp_ServerHandshakeParameters* grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_mutable_value(grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry *msg, upb_arena *arena) {
  struct grpc_gcp_ServerHandshakeParameters* sub = (struct grpc_gcp_ServerHandshakeParameters*)grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_value(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_ServerHandshakeParameters*)upb_msg_new(&grpc_gcp_ServerHandshakeParameters_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_StartServerHandshakeReq_HandshakeParametersEntry_set_value(msg, sub);
  }
  return sub;
}

/* grpc.gcp.NextHandshakeMessageReq */

UPB_INLINE grpc_gcp_NextHandshakeMessageReq *grpc_gcp_NextHandshakeMessageReq_new(upb_arena *arena) {
  return (grpc_gcp_NextHandshakeMessageReq *)upb_msg_new(&grpc_gcp_NextHandshakeMessageReq_msginit, arena);
}
UPB_INLINE grpc_gcp_NextHandshakeMessageReq *grpc_gcp_NextHandshakeMessageReq_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_NextHandshakeMessageReq *ret = grpc_gcp_NextHandshakeMessageReq_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_NextHandshakeMessageReq_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_NextHandshakeMessageReq_serialize(const grpc_gcp_NextHandshakeMessageReq *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_NextHandshakeMessageReq_msginit, arena, len);
}

UPB_INLINE upb_strview grpc_gcp_NextHandshakeMessageReq_in_bytes(const grpc_gcp_NextHandshakeMessageReq *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)); }

UPB_INLINE void grpc_gcp_NextHandshakeMessageReq_set_in_bytes(grpc_gcp_NextHandshakeMessageReq *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(0, 0)) = value;
}

/* grpc.gcp.HandshakerReq */

UPB_INLINE grpc_gcp_HandshakerReq *grpc_gcp_HandshakerReq_new(upb_arena *arena) {
  return (grpc_gcp_HandshakerReq *)upb_msg_new(&grpc_gcp_HandshakerReq_msginit, arena);
}
UPB_INLINE grpc_gcp_HandshakerReq *grpc_gcp_HandshakerReq_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_HandshakerReq *ret = grpc_gcp_HandshakerReq_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_HandshakerReq_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_HandshakerReq_serialize(const grpc_gcp_HandshakerReq *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_HandshakerReq_msginit, arena, len);
}

typedef enum {
  grpc_gcp_HandshakerReq_req_oneof_client_start = 1,
  grpc_gcp_HandshakerReq_req_oneof_server_start = 2,
  grpc_gcp_HandshakerReq_req_oneof_next = 3,
  grpc_gcp_HandshakerReq_req_oneof_NOT_SET = 0
} grpc_gcp_HandshakerReq_req_oneof_oneofcases;
UPB_INLINE grpc_gcp_HandshakerReq_req_oneof_oneofcases grpc_gcp_HandshakerReq_req_oneof_case(const grpc_gcp_HandshakerReq* msg) { return (grpc_gcp_HandshakerReq_req_oneof_oneofcases)UPB_FIELD_AT(msg, int32_t, UPB_SIZE(4, 8)); }

UPB_INLINE bool grpc_gcp_HandshakerReq_has_client_start(const grpc_gcp_HandshakerReq *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(4, 8), 1); }
UPB_INLINE const grpc_gcp_StartClientHandshakeReq* grpc_gcp_HandshakerReq_client_start(const grpc_gcp_HandshakerReq *msg) { return UPB_READ_ONEOF(msg, const grpc_gcp_StartClientHandshakeReq*, UPB_SIZE(0, 0), UPB_SIZE(4, 8), 1, NULL); }
UPB_INLINE bool grpc_gcp_HandshakerReq_has_server_start(const grpc_gcp_HandshakerReq *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(4, 8), 2); }
UPB_INLINE const grpc_gcp_StartServerHandshakeReq* grpc_gcp_HandshakerReq_server_start(const grpc_gcp_HandshakerReq *msg) { return UPB_READ_ONEOF(msg, const grpc_gcp_StartServerHandshakeReq*, UPB_SIZE(0, 0), UPB_SIZE(4, 8), 2, NULL); }
UPB_INLINE bool grpc_gcp_HandshakerReq_has_next(const grpc_gcp_HandshakerReq *msg) { return _upb_has_oneof_field(msg, UPB_SIZE(4, 8), 3); }
UPB_INLINE const grpc_gcp_NextHandshakeMessageReq* grpc_gcp_HandshakerReq_next(const grpc_gcp_HandshakerReq *msg) { return UPB_READ_ONEOF(msg, const grpc_gcp_NextHandshakeMessageReq*, UPB_SIZE(0, 0), UPB_SIZE(4, 8), 3, NULL); }

UPB_INLINE void grpc_gcp_HandshakerReq_set_client_start(grpc_gcp_HandshakerReq *msg, grpc_gcp_StartClientHandshakeReq* value) {
  UPB_WRITE_ONEOF(msg, grpc_gcp_StartClientHandshakeReq*, UPB_SIZE(0, 0), value, UPB_SIZE(4, 8), 1);
}
UPB_INLINE struct grpc_gcp_StartClientHandshakeReq* grpc_gcp_HandshakerReq_mutable_client_start(grpc_gcp_HandshakerReq *msg, upb_arena *arena) {
  struct grpc_gcp_StartClientHandshakeReq* sub = (struct grpc_gcp_StartClientHandshakeReq*)grpc_gcp_HandshakerReq_client_start(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_StartClientHandshakeReq*)upb_msg_new(&grpc_gcp_StartClientHandshakeReq_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerReq_set_client_start(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_HandshakerReq_set_server_start(grpc_gcp_HandshakerReq *msg, grpc_gcp_StartServerHandshakeReq* value) {
  UPB_WRITE_ONEOF(msg, grpc_gcp_StartServerHandshakeReq*, UPB_SIZE(0, 0), value, UPB_SIZE(4, 8), 2);
}
UPB_INLINE struct grpc_gcp_StartServerHandshakeReq* grpc_gcp_HandshakerReq_mutable_server_start(grpc_gcp_HandshakerReq *msg, upb_arena *arena) {
  struct grpc_gcp_StartServerHandshakeReq* sub = (struct grpc_gcp_StartServerHandshakeReq*)grpc_gcp_HandshakerReq_server_start(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_StartServerHandshakeReq*)upb_msg_new(&grpc_gcp_StartServerHandshakeReq_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerReq_set_server_start(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_HandshakerReq_set_next(grpc_gcp_HandshakerReq *msg, grpc_gcp_NextHandshakeMessageReq* value) {
  UPB_WRITE_ONEOF(msg, grpc_gcp_NextHandshakeMessageReq*, UPB_SIZE(0, 0), value, UPB_SIZE(4, 8), 3);
}
UPB_INLINE struct grpc_gcp_NextHandshakeMessageReq* grpc_gcp_HandshakerReq_mutable_next(grpc_gcp_HandshakerReq *msg, upb_arena *arena) {
  struct grpc_gcp_NextHandshakeMessageReq* sub = (struct grpc_gcp_NextHandshakeMessageReq*)grpc_gcp_HandshakerReq_next(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_NextHandshakeMessageReq*)upb_msg_new(&grpc_gcp_NextHandshakeMessageReq_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerReq_set_next(msg, sub);
  }
  return sub;
}

/* grpc.gcp.HandshakerResult */

UPB_INLINE grpc_gcp_HandshakerResult *grpc_gcp_HandshakerResult_new(upb_arena *arena) {
  return (grpc_gcp_HandshakerResult *)upb_msg_new(&grpc_gcp_HandshakerResult_msginit, arena);
}
UPB_INLINE grpc_gcp_HandshakerResult *grpc_gcp_HandshakerResult_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_HandshakerResult *ret = grpc_gcp_HandshakerResult_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_HandshakerResult_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_HandshakerResult_serialize(const grpc_gcp_HandshakerResult *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_HandshakerResult_msginit, arena, len);
}

UPB_INLINE upb_strview grpc_gcp_HandshakerResult_application_protocol(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(4, 8)); }
UPB_INLINE upb_strview grpc_gcp_HandshakerResult_record_protocol(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(12, 24)); }
UPB_INLINE upb_strview grpc_gcp_HandshakerResult_key_data(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(20, 40)); }
UPB_INLINE const grpc_gcp_Identity* grpc_gcp_HandshakerResult_peer_identity(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Identity*, UPB_SIZE(28, 56)); }
UPB_INLINE const grpc_gcp_Identity* grpc_gcp_HandshakerResult_local_identity(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_Identity*, UPB_SIZE(32, 64)); }
UPB_INLINE bool grpc_gcp_HandshakerResult_keep_channel_open(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, bool, UPB_SIZE(0, 0)); }
UPB_INLINE const struct grpc_gcp_RpcProtocolVersions* grpc_gcp_HandshakerResult_peer_rpc_versions(const grpc_gcp_HandshakerResult *msg) { return UPB_FIELD_AT(msg, const struct grpc_gcp_RpcProtocolVersions*, UPB_SIZE(36, 72)); }

UPB_INLINE void grpc_gcp_HandshakerResult_set_application_protocol(grpc_gcp_HandshakerResult *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(4, 8)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerResult_set_record_protocol(grpc_gcp_HandshakerResult *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(12, 24)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerResult_set_key_data(grpc_gcp_HandshakerResult *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(20, 40)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerResult_set_peer_identity(grpc_gcp_HandshakerResult *msg, grpc_gcp_Identity* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Identity*, UPB_SIZE(28, 56)) = value;
}
UPB_INLINE struct grpc_gcp_Identity* grpc_gcp_HandshakerResult_mutable_peer_identity(grpc_gcp_HandshakerResult *msg, upb_arena *arena) {
  struct grpc_gcp_Identity* sub = (struct grpc_gcp_Identity*)grpc_gcp_HandshakerResult_peer_identity(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Identity*)upb_msg_new(&grpc_gcp_Identity_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerResult_set_peer_identity(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_HandshakerResult_set_local_identity(grpc_gcp_HandshakerResult *msg, grpc_gcp_Identity* value) {
  UPB_FIELD_AT(msg, grpc_gcp_Identity*, UPB_SIZE(32, 64)) = value;
}
UPB_INLINE struct grpc_gcp_Identity* grpc_gcp_HandshakerResult_mutable_local_identity(grpc_gcp_HandshakerResult *msg, upb_arena *arena) {
  struct grpc_gcp_Identity* sub = (struct grpc_gcp_Identity*)grpc_gcp_HandshakerResult_local_identity(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_Identity*)upb_msg_new(&grpc_gcp_Identity_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerResult_set_local_identity(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_HandshakerResult_set_keep_channel_open(grpc_gcp_HandshakerResult *msg, bool value) {
  UPB_FIELD_AT(msg, bool, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerResult_set_peer_rpc_versions(grpc_gcp_HandshakerResult *msg, struct grpc_gcp_RpcProtocolVersions* value) {
  UPB_FIELD_AT(msg, struct grpc_gcp_RpcProtocolVersions*, UPB_SIZE(36, 72)) = value;
}
UPB_INLINE struct grpc_gcp_RpcProtocolVersions* grpc_gcp_HandshakerResult_mutable_peer_rpc_versions(grpc_gcp_HandshakerResult *msg, upb_arena *arena) {
  struct grpc_gcp_RpcProtocolVersions* sub = (struct grpc_gcp_RpcProtocolVersions*)grpc_gcp_HandshakerResult_peer_rpc_versions(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_RpcProtocolVersions*)upb_msg_new(&grpc_gcp_RpcProtocolVersions_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerResult_set_peer_rpc_versions(msg, sub);
  }
  return sub;
}

/* grpc.gcp.HandshakerStatus */

UPB_INLINE grpc_gcp_HandshakerStatus *grpc_gcp_HandshakerStatus_new(upb_arena *arena) {
  return (grpc_gcp_HandshakerStatus *)upb_msg_new(&grpc_gcp_HandshakerStatus_msginit, arena);
}
UPB_INLINE grpc_gcp_HandshakerStatus *grpc_gcp_HandshakerStatus_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_HandshakerStatus *ret = grpc_gcp_HandshakerStatus_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_HandshakerStatus_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_HandshakerStatus_serialize(const grpc_gcp_HandshakerStatus *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_HandshakerStatus_msginit, arena, len);
}

UPB_INLINE uint32_t grpc_gcp_HandshakerStatus_code(const grpc_gcp_HandshakerStatus *msg) { return UPB_FIELD_AT(msg, uint32_t, UPB_SIZE(0, 0)); }
UPB_INLINE upb_strview grpc_gcp_HandshakerStatus_details(const grpc_gcp_HandshakerStatus *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(4, 8)); }

UPB_INLINE void grpc_gcp_HandshakerStatus_set_code(grpc_gcp_HandshakerStatus *msg, uint32_t value) {
  UPB_FIELD_AT(msg, uint32_t, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerStatus_set_details(grpc_gcp_HandshakerStatus *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(4, 8)) = value;
}

/* grpc.gcp.HandshakerResp */

UPB_INLINE grpc_gcp_HandshakerResp *grpc_gcp_HandshakerResp_new(upb_arena *arena) {
  return (grpc_gcp_HandshakerResp *)upb_msg_new(&grpc_gcp_HandshakerResp_msginit, arena);
}
UPB_INLINE grpc_gcp_HandshakerResp *grpc_gcp_HandshakerResp_parse(const char *buf, size_t size,
                        upb_arena *arena) {
  grpc_gcp_HandshakerResp *ret = grpc_gcp_HandshakerResp_new(arena);
  return (ret && upb_decode(buf, size, ret, &grpc_gcp_HandshakerResp_msginit, arena)) ? ret : NULL;
}
UPB_INLINE char *grpc_gcp_HandshakerResp_serialize(const grpc_gcp_HandshakerResp *msg, upb_arena *arena, size_t *len) {
  return upb_encode(msg, &grpc_gcp_HandshakerResp_msginit, arena, len);
}

UPB_INLINE upb_strview grpc_gcp_HandshakerResp_out_frames(const grpc_gcp_HandshakerResp *msg) { return UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(4, 8)); }
UPB_INLINE uint32_t grpc_gcp_HandshakerResp_bytes_consumed(const grpc_gcp_HandshakerResp *msg) { return UPB_FIELD_AT(msg, uint32_t, UPB_SIZE(0, 0)); }
UPB_INLINE const grpc_gcp_HandshakerResult* grpc_gcp_HandshakerResp_result(const grpc_gcp_HandshakerResp *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_HandshakerResult*, UPB_SIZE(12, 24)); }
UPB_INLINE const grpc_gcp_HandshakerStatus* grpc_gcp_HandshakerResp_status(const grpc_gcp_HandshakerResp *msg) { return UPB_FIELD_AT(msg, const grpc_gcp_HandshakerStatus*, UPB_SIZE(16, 32)); }

UPB_INLINE void grpc_gcp_HandshakerResp_set_out_frames(grpc_gcp_HandshakerResp *msg, upb_strview value) {
  UPB_FIELD_AT(msg, upb_strview, UPB_SIZE(4, 8)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerResp_set_bytes_consumed(grpc_gcp_HandshakerResp *msg, uint32_t value) {
  UPB_FIELD_AT(msg, uint32_t, UPB_SIZE(0, 0)) = value;
}
UPB_INLINE void grpc_gcp_HandshakerResp_set_result(grpc_gcp_HandshakerResp *msg, grpc_gcp_HandshakerResult* value) {
  UPB_FIELD_AT(msg, grpc_gcp_HandshakerResult*, UPB_SIZE(12, 24)) = value;
}
UPB_INLINE struct grpc_gcp_HandshakerResult* grpc_gcp_HandshakerResp_mutable_result(grpc_gcp_HandshakerResp *msg, upb_arena *arena) {
  struct grpc_gcp_HandshakerResult* sub = (struct grpc_gcp_HandshakerResult*)grpc_gcp_HandshakerResp_result(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_HandshakerResult*)upb_msg_new(&grpc_gcp_HandshakerResult_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerResp_set_result(msg, sub);
  }
  return sub;
}
UPB_INLINE void grpc_gcp_HandshakerResp_set_status(grpc_gcp_HandshakerResp *msg, grpc_gcp_HandshakerStatus* value) {
  UPB_FIELD_AT(msg, grpc_gcp_HandshakerStatus*, UPB_SIZE(16, 32)) = value;
}
UPB_INLINE struct grpc_gcp_HandshakerStatus* grpc_gcp_HandshakerResp_mutable_status(grpc_gcp_HandshakerResp *msg, upb_arena *arena) {
  struct grpc_gcp_HandshakerStatus* sub = (struct grpc_gcp_HandshakerStatus*)grpc_gcp_HandshakerResp_status(msg);
  if (sub == NULL) {
    sub = (struct grpc_gcp_HandshakerStatus*)upb_msg_new(&grpc_gcp_HandshakerStatus_msginit, arena);
    if (!sub) return NULL;
    grpc_gcp_HandshakerResp_set_status(msg, sub);
  }
  return sub;
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port_undef.inc"

#endif  /* SRC_PROTO_GRPC_GCP_HANDSHAKER_PROTO_UPB_H_ */
