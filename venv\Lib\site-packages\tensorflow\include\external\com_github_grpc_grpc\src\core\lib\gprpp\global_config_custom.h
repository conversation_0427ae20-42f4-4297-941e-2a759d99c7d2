/*
 *
 * Copyright 2019 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#ifndef GRPC_CORE_LIB_GPRPP_GLOBAL_CONFIG_CUSTOM_H
#define GRPC_CORE_LIB_GPRPP_GLOBAL_CONFIG_CUSTOM_H

// This is a placeholder for custom global configuration implementation.
// To use the custom one, please define following macros here.
//
//   GPR_GLOBAL_CONFIG_DEFINE_BOOL
//   GPR_GLOBAL_CONFIG_DEFINE_INT32
//   GPR_GLOBAL_CONFIG_DEFINE_STRING

#endif /* GRPC_CORE_LIB_GPRPP_GLOBAL_CONFIG_CUSTOM_H */
