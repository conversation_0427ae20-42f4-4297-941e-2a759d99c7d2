// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/cpu/xla_framework.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fxla_5fframework_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fxla_5fframework_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fcpu_2fxla_5fframework_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fcpu_2fxla_5fframework_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fcpu_2fxla_5fframework_2eproto;
namespace xla {
namespace cpu {
class XlaFrameworkMappingProto;
struct XlaFrameworkMappingProtoDefaultTypeInternal;
extern XlaFrameworkMappingProtoDefaultTypeInternal _XlaFrameworkMappingProto_default_instance_;
}  // namespace cpu
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::cpu::XlaFrameworkMappingProto* Arena::CreateMaybeMessage<::xla::cpu::XlaFrameworkMappingProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace cpu {

// ===================================================================

class XlaFrameworkMappingProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.XlaFrameworkMappingProto) */ {
 public:
  inline XlaFrameworkMappingProto() : XlaFrameworkMappingProto(nullptr) {}
  ~XlaFrameworkMappingProto() override;
  explicit PROTOBUF_CONSTEXPR XlaFrameworkMappingProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaFrameworkMappingProto(const XlaFrameworkMappingProto& from);
  XlaFrameworkMappingProto(XlaFrameworkMappingProto&& from) noexcept
    : XlaFrameworkMappingProto() {
    *this = ::std::move(from);
  }

  inline XlaFrameworkMappingProto& operator=(const XlaFrameworkMappingProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaFrameworkMappingProto& operator=(XlaFrameworkMappingProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaFrameworkMappingProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaFrameworkMappingProto* internal_default_instance() {
    return reinterpret_cast<const XlaFrameworkMappingProto*>(
               &_XlaFrameworkMappingProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(XlaFrameworkMappingProto& a, XlaFrameworkMappingProto& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaFrameworkMappingProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaFrameworkMappingProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaFrameworkMappingProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaFrameworkMappingProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaFrameworkMappingProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaFrameworkMappingProto& from) {
    XlaFrameworkMappingProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaFrameworkMappingProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.XlaFrameworkMappingProto";
  }
  protected:
  explicit XlaFrameworkMappingProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputsFieldNumber = 1,
    kFlattenedOutputsFieldNumber = 2,
    kOutputIsTupleFieldNumber = 4,
    kResultFieldNumber = 3,
  };
  // repeated int64 inputs = 1 [packed = true];
  int inputs_size() const;
  private:
  int _internal_inputs_size() const;
  public:
  void clear_inputs();
  private:
  int64_t _internal_inputs(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_inputs() const;
  void _internal_add_inputs(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_inputs();
  public:
  int64_t inputs(int index) const;
  void set_inputs(int index, int64_t value);
  void add_inputs(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      inputs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_inputs();

  // repeated int64 flattened_outputs = 2 [packed = true];
  int flattened_outputs_size() const;
  private:
  int _internal_flattened_outputs_size() const;
  public:
  void clear_flattened_outputs();
  private:
  int64_t _internal_flattened_outputs(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_flattened_outputs() const;
  void _internal_add_flattened_outputs(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_flattened_outputs();
  public:
  int64_t flattened_outputs(int index) const;
  void set_flattened_outputs(int index, int64_t value);
  void add_flattened_outputs(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      flattened_outputs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_flattened_outputs();

  // optional bool output_is_tuple = 4;
  bool has_output_is_tuple() const;
  private:
  bool _internal_has_output_is_tuple() const;
  public:
  void clear_output_is_tuple();
  bool output_is_tuple() const;
  void set_output_is_tuple(bool value);
  private:
  bool _internal_output_is_tuple() const;
  void _internal_set_output_is_tuple(bool value);
  public:

  // optional int64 result = 3 [default = -1];
  bool has_result() const;
  private:
  bool _internal_has_result() const;
  public:
  void clear_result();
  int64_t result() const;
  void set_result(int64_t value);
  private:
  int64_t _internal_result() const;
  void _internal_set_result(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.XlaFrameworkMappingProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > inputs_;
    mutable std::atomic<int> _inputs_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > flattened_outputs_;
    mutable std::atomic<int> _flattened_outputs_cached_byte_size_;
    bool output_is_tuple_;
    int64_t result_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fxla_5fframework_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// XlaFrameworkMappingProto

// repeated int64 inputs = 1 [packed = true];
inline int XlaFrameworkMappingProto::_internal_inputs_size() const {
  return _impl_.inputs_.size();
}
inline int XlaFrameworkMappingProto::inputs_size() const {
  return _internal_inputs_size();
}
inline void XlaFrameworkMappingProto::clear_inputs() {
  _impl_.inputs_.Clear();
}
inline int64_t XlaFrameworkMappingProto::_internal_inputs(int index) const {
  return _impl_.inputs_.Get(index);
}
inline int64_t XlaFrameworkMappingProto::inputs(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.XlaFrameworkMappingProto.inputs)
  return _internal_inputs(index);
}
inline void XlaFrameworkMappingProto::set_inputs(int index, int64_t value) {
  _impl_.inputs_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.XlaFrameworkMappingProto.inputs)
}
inline void XlaFrameworkMappingProto::_internal_add_inputs(int64_t value) {
  _impl_.inputs_.Add(value);
}
inline void XlaFrameworkMappingProto::add_inputs(int64_t value) {
  _internal_add_inputs(value);
  // @@protoc_insertion_point(field_add:xla.cpu.XlaFrameworkMappingProto.inputs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
XlaFrameworkMappingProto::_internal_inputs() const {
  return _impl_.inputs_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
XlaFrameworkMappingProto::inputs() const {
  // @@protoc_insertion_point(field_list:xla.cpu.XlaFrameworkMappingProto.inputs)
  return _internal_inputs();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
XlaFrameworkMappingProto::_internal_mutable_inputs() {
  return &_impl_.inputs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
XlaFrameworkMappingProto::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.XlaFrameworkMappingProto.inputs)
  return _internal_mutable_inputs();
}

// repeated int64 flattened_outputs = 2 [packed = true];
inline int XlaFrameworkMappingProto::_internal_flattened_outputs_size() const {
  return _impl_.flattened_outputs_.size();
}
inline int XlaFrameworkMappingProto::flattened_outputs_size() const {
  return _internal_flattened_outputs_size();
}
inline void XlaFrameworkMappingProto::clear_flattened_outputs() {
  _impl_.flattened_outputs_.Clear();
}
inline int64_t XlaFrameworkMappingProto::_internal_flattened_outputs(int index) const {
  return _impl_.flattened_outputs_.Get(index);
}
inline int64_t XlaFrameworkMappingProto::flattened_outputs(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.XlaFrameworkMappingProto.flattened_outputs)
  return _internal_flattened_outputs(index);
}
inline void XlaFrameworkMappingProto::set_flattened_outputs(int index, int64_t value) {
  _impl_.flattened_outputs_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.XlaFrameworkMappingProto.flattened_outputs)
}
inline void XlaFrameworkMappingProto::_internal_add_flattened_outputs(int64_t value) {
  _impl_.flattened_outputs_.Add(value);
}
inline void XlaFrameworkMappingProto::add_flattened_outputs(int64_t value) {
  _internal_add_flattened_outputs(value);
  // @@protoc_insertion_point(field_add:xla.cpu.XlaFrameworkMappingProto.flattened_outputs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
XlaFrameworkMappingProto::_internal_flattened_outputs() const {
  return _impl_.flattened_outputs_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
XlaFrameworkMappingProto::flattened_outputs() const {
  // @@protoc_insertion_point(field_list:xla.cpu.XlaFrameworkMappingProto.flattened_outputs)
  return _internal_flattened_outputs();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
XlaFrameworkMappingProto::_internal_mutable_flattened_outputs() {
  return &_impl_.flattened_outputs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
XlaFrameworkMappingProto::mutable_flattened_outputs() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.XlaFrameworkMappingProto.flattened_outputs)
  return _internal_mutable_flattened_outputs();
}

// optional int64 result = 3 [default = -1];
inline bool XlaFrameworkMappingProto::_internal_has_result() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool XlaFrameworkMappingProto::has_result() const {
  return _internal_has_result();
}
inline void XlaFrameworkMappingProto::clear_result() {
  _impl_.result_ = int64_t{-1};
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline int64_t XlaFrameworkMappingProto::_internal_result() const {
  return _impl_.result_;
}
inline int64_t XlaFrameworkMappingProto::result() const {
  // @@protoc_insertion_point(field_get:xla.cpu.XlaFrameworkMappingProto.result)
  return _internal_result();
}
inline void XlaFrameworkMappingProto::_internal_set_result(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.result_ = value;
}
inline void XlaFrameworkMappingProto::set_result(int64_t value) {
  _internal_set_result(value);
  // @@protoc_insertion_point(field_set:xla.cpu.XlaFrameworkMappingProto.result)
}

// optional bool output_is_tuple = 4;
inline bool XlaFrameworkMappingProto::_internal_has_output_is_tuple() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool XlaFrameworkMappingProto::has_output_is_tuple() const {
  return _internal_has_output_is_tuple();
}
inline void XlaFrameworkMappingProto::clear_output_is_tuple() {
  _impl_.output_is_tuple_ = false;
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline bool XlaFrameworkMappingProto::_internal_output_is_tuple() const {
  return _impl_.output_is_tuple_;
}
inline bool XlaFrameworkMappingProto::output_is_tuple() const {
  // @@protoc_insertion_point(field_get:xla.cpu.XlaFrameworkMappingProto.output_is_tuple)
  return _internal_output_is_tuple();
}
inline void XlaFrameworkMappingProto::_internal_set_output_is_tuple(bool value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.output_is_tuple_ = value;
}
inline void XlaFrameworkMappingProto::set_output_is_tuple(bool value) {
  _internal_set_output_is_tuple(value);
  // @@protoc_insertion_point(field_set:xla.cpu.XlaFrameworkMappingProto.output_is_tuple)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace cpu
}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fxla_5fframework_2eproto
