// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/quantization/tensorflow/calibrator/calibration_statistics.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
namespace tensorflow {
namespace calibrator {
class CalibrationStatistics;
struct CalibrationStatisticsDefaultTypeInternal;
extern CalibrationStatisticsDefaultTypeInternal _CalibrationStatistics_default_instance_;
class CalibrationStatisticsMap;
struct CalibrationStatisticsMapDefaultTypeInternal;
extern CalibrationStatisticsMapDefaultTypeInternal _CalibrationStatisticsMap_default_instance_;
class CalibrationStatisticsMap_StatisticsEntry_DoNotUse;
struct CalibrationStatisticsMap_StatisticsEntry_DoNotUseDefaultTypeInternal;
extern CalibrationStatisticsMap_StatisticsEntry_DoNotUseDefaultTypeInternal _CalibrationStatisticsMap_StatisticsEntry_DoNotUse_default_instance_;
class CalibrationStatistics_AverageMinMaxStatistics;
struct CalibrationStatistics_AverageMinMaxStatisticsDefaultTypeInternal;
extern CalibrationStatistics_AverageMinMaxStatisticsDefaultTypeInternal _CalibrationStatistics_AverageMinMaxStatistics_default_instance_;
class CalibrationStatistics_HistogramStatistics;
struct CalibrationStatistics_HistogramStatisticsDefaultTypeInternal;
extern CalibrationStatistics_HistogramStatisticsDefaultTypeInternal _CalibrationStatistics_HistogramStatistics_default_instance_;
class CalibrationStatistics_MinMaxStatistics;
struct CalibrationStatistics_MinMaxStatisticsDefaultTypeInternal;
extern CalibrationStatistics_MinMaxStatisticsDefaultTypeInternal _CalibrationStatistics_MinMaxStatistics_default_instance_;
}  // namespace calibrator
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::calibrator::CalibrationStatistics* Arena::CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics>(Arena*);
template<> ::tensorflow::calibrator::CalibrationStatisticsMap* Arena::CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatisticsMap>(Arena*);
template<> ::tensorflow::calibrator::CalibrationStatisticsMap_StatisticsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatisticsMap_StatisticsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* Arena::CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics>(Arena*);
template<> ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* Arena::CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics>(Arena*);
template<> ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* Arena::CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace calibrator {

// ===================================================================

class CalibrationStatistics_MinMaxStatistics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics) */ {
 public:
  inline CalibrationStatistics_MinMaxStatistics() : CalibrationStatistics_MinMaxStatistics(nullptr) {}
  ~CalibrationStatistics_MinMaxStatistics() override;
  explicit PROTOBUF_CONSTEXPR CalibrationStatistics_MinMaxStatistics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationStatistics_MinMaxStatistics(const CalibrationStatistics_MinMaxStatistics& from);
  CalibrationStatistics_MinMaxStatistics(CalibrationStatistics_MinMaxStatistics&& from) noexcept
    : CalibrationStatistics_MinMaxStatistics() {
    *this = ::std::move(from);
  }

  inline CalibrationStatistics_MinMaxStatistics& operator=(const CalibrationStatistics_MinMaxStatistics& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationStatistics_MinMaxStatistics& operator=(CalibrationStatistics_MinMaxStatistics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationStatistics_MinMaxStatistics& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationStatistics_MinMaxStatistics* internal_default_instance() {
    return reinterpret_cast<const CalibrationStatistics_MinMaxStatistics*>(
               &_CalibrationStatistics_MinMaxStatistics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CalibrationStatistics_MinMaxStatistics& a, CalibrationStatistics_MinMaxStatistics& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationStatistics_MinMaxStatistics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationStatistics_MinMaxStatistics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationStatistics_MinMaxStatistics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationStatistics_MinMaxStatistics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationStatistics_MinMaxStatistics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationStatistics_MinMaxStatistics& from) {
    CalibrationStatistics_MinMaxStatistics::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationStatistics_MinMaxStatistics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics";
  }
  protected:
  explicit CalibrationStatistics_MinMaxStatistics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGlobalMinFieldNumber = 1,
    kGlobalMaxFieldNumber = 2,
  };
  // float global_min = 1;
  void clear_global_min();
  float global_min() const;
  void set_global_min(float value);
  private:
  float _internal_global_min() const;
  void _internal_set_global_min(float value);
  public:

  // float global_max = 2;
  void clear_global_max();
  float global_max() const;
  void set_global_max(float value);
  private:
  float _internal_global_max() const;
  void _internal_set_global_max(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float global_min_;
    float global_max_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
};
// -------------------------------------------------------------------

class CalibrationStatistics_AverageMinMaxStatistics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics) */ {
 public:
  inline CalibrationStatistics_AverageMinMaxStatistics() : CalibrationStatistics_AverageMinMaxStatistics(nullptr) {}
  ~CalibrationStatistics_AverageMinMaxStatistics() override;
  explicit PROTOBUF_CONSTEXPR CalibrationStatistics_AverageMinMaxStatistics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationStatistics_AverageMinMaxStatistics(const CalibrationStatistics_AverageMinMaxStatistics& from);
  CalibrationStatistics_AverageMinMaxStatistics(CalibrationStatistics_AverageMinMaxStatistics&& from) noexcept
    : CalibrationStatistics_AverageMinMaxStatistics() {
    *this = ::std::move(from);
  }

  inline CalibrationStatistics_AverageMinMaxStatistics& operator=(const CalibrationStatistics_AverageMinMaxStatistics& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationStatistics_AverageMinMaxStatistics& operator=(CalibrationStatistics_AverageMinMaxStatistics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationStatistics_AverageMinMaxStatistics& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationStatistics_AverageMinMaxStatistics* internal_default_instance() {
    return reinterpret_cast<const CalibrationStatistics_AverageMinMaxStatistics*>(
               &_CalibrationStatistics_AverageMinMaxStatistics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CalibrationStatistics_AverageMinMaxStatistics& a, CalibrationStatistics_AverageMinMaxStatistics& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationStatistics_AverageMinMaxStatistics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationStatistics_AverageMinMaxStatistics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationStatistics_AverageMinMaxStatistics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationStatistics_AverageMinMaxStatistics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationStatistics_AverageMinMaxStatistics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationStatistics_AverageMinMaxStatistics& from) {
    CalibrationStatistics_AverageMinMaxStatistics::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationStatistics_AverageMinMaxStatistics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics";
  }
  protected:
  explicit CalibrationStatistics_AverageMinMaxStatistics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinSumFieldNumber = 1,
    kMaxSumFieldNumber = 2,
    kNumSamplesFieldNumber = 3,
  };
  // float min_sum = 1;
  void clear_min_sum();
  float min_sum() const;
  void set_min_sum(float value);
  private:
  float _internal_min_sum() const;
  void _internal_set_min_sum(float value);
  public:

  // float max_sum = 2;
  void clear_max_sum();
  float max_sum() const;
  void set_max_sum(float value);
  private:
  float _internal_max_sum() const;
  void _internal_set_max_sum(float value);
  public:

  // int32 num_samples = 3;
  void clear_num_samples();
  int32_t num_samples() const;
  void set_num_samples(int32_t value);
  private:
  int32_t _internal_num_samples() const;
  void _internal_set_num_samples(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float min_sum_;
    float max_sum_;
    int32_t num_samples_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
};
// -------------------------------------------------------------------

class CalibrationStatistics_HistogramStatistics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics) */ {
 public:
  inline CalibrationStatistics_HistogramStatistics() : CalibrationStatistics_HistogramStatistics(nullptr) {}
  ~CalibrationStatistics_HistogramStatistics() override;
  explicit PROTOBUF_CONSTEXPR CalibrationStatistics_HistogramStatistics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationStatistics_HistogramStatistics(const CalibrationStatistics_HistogramStatistics& from);
  CalibrationStatistics_HistogramStatistics(CalibrationStatistics_HistogramStatistics&& from) noexcept
    : CalibrationStatistics_HistogramStatistics() {
    *this = ::std::move(from);
  }

  inline CalibrationStatistics_HistogramStatistics& operator=(const CalibrationStatistics_HistogramStatistics& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationStatistics_HistogramStatistics& operator=(CalibrationStatistics_HistogramStatistics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationStatistics_HistogramStatistics& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationStatistics_HistogramStatistics* internal_default_instance() {
    return reinterpret_cast<const CalibrationStatistics_HistogramStatistics*>(
               &_CalibrationStatistics_HistogramStatistics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CalibrationStatistics_HistogramStatistics& a, CalibrationStatistics_HistogramStatistics& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationStatistics_HistogramStatistics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationStatistics_HistogramStatistics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationStatistics_HistogramStatistics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationStatistics_HistogramStatistics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationStatistics_HistogramStatistics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationStatistics_HistogramStatistics& from) {
    CalibrationStatistics_HistogramStatistics::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationStatistics_HistogramStatistics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.calibrator.CalibrationStatistics.HistogramStatistics";
  }
  protected:
  explicit CalibrationStatistics_HistogramStatistics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHistFreqFieldNumber = 3,
    kBinWidthFieldNumber = 1,
    kLowerBoundFieldNumber = 2,
  };
  // repeated float hist_freq = 3;
  int hist_freq_size() const;
  private:
  int _internal_hist_freq_size() const;
  public:
  void clear_hist_freq();
  private:
  float _internal_hist_freq(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_hist_freq() const;
  void _internal_add_hist_freq(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_hist_freq();
  public:
  float hist_freq(int index) const;
  void set_hist_freq(int index, float value);
  void add_hist_freq(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      hist_freq() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_hist_freq();

  // float bin_width = 1;
  void clear_bin_width();
  float bin_width() const;
  void set_bin_width(float value);
  private:
  float _internal_bin_width() const;
  void _internal_set_bin_width(float value);
  public:

  // float lower_bound = 2;
  void clear_lower_bound();
  float lower_bound() const;
  void set_lower_bound(float value);
  private:
  float _internal_lower_bound() const;
  void _internal_set_lower_bound(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > hist_freq_;
    float bin_width_;
    float lower_bound_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
};
// -------------------------------------------------------------------

class CalibrationStatistics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.calibrator.CalibrationStatistics) */ {
 public:
  inline CalibrationStatistics() : CalibrationStatistics(nullptr) {}
  ~CalibrationStatistics() override;
  explicit PROTOBUF_CONSTEXPR CalibrationStatistics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationStatistics(const CalibrationStatistics& from);
  CalibrationStatistics(CalibrationStatistics&& from) noexcept
    : CalibrationStatistics() {
    *this = ::std::move(from);
  }

  inline CalibrationStatistics& operator=(const CalibrationStatistics& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationStatistics& operator=(CalibrationStatistics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationStatistics& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationStatistics* internal_default_instance() {
    return reinterpret_cast<const CalibrationStatistics*>(
               &_CalibrationStatistics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CalibrationStatistics& a, CalibrationStatistics& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationStatistics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationStatistics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationStatistics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationStatistics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationStatistics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationStatistics& from) {
    CalibrationStatistics::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationStatistics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.calibrator.CalibrationStatistics";
  }
  protected:
  explicit CalibrationStatistics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CalibrationStatistics_MinMaxStatistics MinMaxStatistics;
  typedef CalibrationStatistics_AverageMinMaxStatistics AverageMinMaxStatistics;
  typedef CalibrationStatistics_HistogramStatistics HistogramStatistics;

  // accessors -------------------------------------------------------

  enum : int {
    kMinMaxStatisticsFieldNumber = 1,
    kAverageMinMaxStatisticsFieldNumber = 2,
    kHistogramStatisticsFieldNumber = 3,
  };
  // .tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics min_max_statistics = 1;
  bool has_min_max_statistics() const;
  private:
  bool _internal_has_min_max_statistics() const;
  public:
  void clear_min_max_statistics();
  const ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics& min_max_statistics() const;
  PROTOBUF_NODISCARD ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* release_min_max_statistics();
  ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* mutable_min_max_statistics();
  void set_allocated_min_max_statistics(::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* min_max_statistics);
  private:
  const ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics& _internal_min_max_statistics() const;
  ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* _internal_mutable_min_max_statistics();
  public:
  void unsafe_arena_set_allocated_min_max_statistics(
      ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* min_max_statistics);
  ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* unsafe_arena_release_min_max_statistics();

  // .tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics average_min_max_statistics = 2;
  bool has_average_min_max_statistics() const;
  private:
  bool _internal_has_average_min_max_statistics() const;
  public:
  void clear_average_min_max_statistics();
  const ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics& average_min_max_statistics() const;
  PROTOBUF_NODISCARD ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* release_average_min_max_statistics();
  ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* mutable_average_min_max_statistics();
  void set_allocated_average_min_max_statistics(::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* average_min_max_statistics);
  private:
  const ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics& _internal_average_min_max_statistics() const;
  ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* _internal_mutable_average_min_max_statistics();
  public:
  void unsafe_arena_set_allocated_average_min_max_statistics(
      ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* average_min_max_statistics);
  ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* unsafe_arena_release_average_min_max_statistics();

  // .tensorflow.calibrator.CalibrationStatistics.HistogramStatistics histogram_statistics = 3;
  bool has_histogram_statistics() const;
  private:
  bool _internal_has_histogram_statistics() const;
  public:
  void clear_histogram_statistics();
  const ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics& histogram_statistics() const;
  PROTOBUF_NODISCARD ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* release_histogram_statistics();
  ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* mutable_histogram_statistics();
  void set_allocated_histogram_statistics(::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* histogram_statistics);
  private:
  const ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics& _internal_histogram_statistics() const;
  ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* _internal_mutable_histogram_statistics();
  public:
  void unsafe_arena_set_allocated_histogram_statistics(
      ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* histogram_statistics);
  ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* unsafe_arena_release_histogram_statistics();

  // @@protoc_insertion_point(class_scope:tensorflow.calibrator.CalibrationStatistics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* min_max_statistics_;
    ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* average_min_max_statistics_;
    ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* histogram_statistics_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
};
// -------------------------------------------------------------------

class CalibrationStatisticsMap_StatisticsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CalibrationStatisticsMap_StatisticsEntry_DoNotUse, 
    std::string, ::tensorflow::calibrator::CalibrationStatistics,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CalibrationStatisticsMap_StatisticsEntry_DoNotUse, 
    std::string, ::tensorflow::calibrator::CalibrationStatistics,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  CalibrationStatisticsMap_StatisticsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR CalibrationStatisticsMap_StatisticsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit CalibrationStatisticsMap_StatisticsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CalibrationStatisticsMap_StatisticsEntry_DoNotUse& other);
  static const CalibrationStatisticsMap_StatisticsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CalibrationStatisticsMap_StatisticsEntry_DoNotUse*>(&_CalibrationStatisticsMap_StatisticsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.calibrator.CalibrationStatisticsMap.StatisticsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
};

// -------------------------------------------------------------------

class CalibrationStatisticsMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.calibrator.CalibrationStatisticsMap) */ {
 public:
  inline CalibrationStatisticsMap() : CalibrationStatisticsMap(nullptr) {}
  ~CalibrationStatisticsMap() override;
  explicit PROTOBUF_CONSTEXPR CalibrationStatisticsMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationStatisticsMap(const CalibrationStatisticsMap& from);
  CalibrationStatisticsMap(CalibrationStatisticsMap&& from) noexcept
    : CalibrationStatisticsMap() {
    *this = ::std::move(from);
  }

  inline CalibrationStatisticsMap& operator=(const CalibrationStatisticsMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationStatisticsMap& operator=(CalibrationStatisticsMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationStatisticsMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationStatisticsMap* internal_default_instance() {
    return reinterpret_cast<const CalibrationStatisticsMap*>(
               &_CalibrationStatisticsMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CalibrationStatisticsMap& a, CalibrationStatisticsMap& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationStatisticsMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationStatisticsMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationStatisticsMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationStatisticsMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationStatisticsMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationStatisticsMap& from) {
    CalibrationStatisticsMap::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationStatisticsMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.calibrator.CalibrationStatisticsMap";
  }
  protected:
  explicit CalibrationStatisticsMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kStatisticsFieldNumber = 1,
  };
  // map<string, .tensorflow.calibrator.CalibrationStatistics> statistics = 1;
  int statistics_size() const;
  private:
  int _internal_statistics_size() const;
  public:
  void clear_statistics();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >&
      _internal_statistics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >*
      _internal_mutable_statistics();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >&
      statistics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >*
      mutable_statistics();

  // @@protoc_insertion_point(class_scope:tensorflow.calibrator.CalibrationStatisticsMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        CalibrationStatisticsMap_StatisticsEntry_DoNotUse,
        std::string, ::tensorflow::calibrator::CalibrationStatistics,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> statistics_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CalibrationStatistics_MinMaxStatistics

// float global_min = 1;
inline void CalibrationStatistics_MinMaxStatistics::clear_global_min() {
  _impl_.global_min_ = 0;
}
inline float CalibrationStatistics_MinMaxStatistics::_internal_global_min() const {
  return _impl_.global_min_;
}
inline float CalibrationStatistics_MinMaxStatistics::global_min() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics.global_min)
  return _internal_global_min();
}
inline void CalibrationStatistics_MinMaxStatistics::_internal_set_global_min(float value) {
  
  _impl_.global_min_ = value;
}
inline void CalibrationStatistics_MinMaxStatistics::set_global_min(float value) {
  _internal_set_global_min(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics.global_min)
}

// float global_max = 2;
inline void CalibrationStatistics_MinMaxStatistics::clear_global_max() {
  _impl_.global_max_ = 0;
}
inline float CalibrationStatistics_MinMaxStatistics::_internal_global_max() const {
  return _impl_.global_max_;
}
inline float CalibrationStatistics_MinMaxStatistics::global_max() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics.global_max)
  return _internal_global_max();
}
inline void CalibrationStatistics_MinMaxStatistics::_internal_set_global_max(float value) {
  
  _impl_.global_max_ = value;
}
inline void CalibrationStatistics_MinMaxStatistics::set_global_max(float value) {
  _internal_set_global_max(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics.global_max)
}

// -------------------------------------------------------------------

// CalibrationStatistics_AverageMinMaxStatistics

// float min_sum = 1;
inline void CalibrationStatistics_AverageMinMaxStatistics::clear_min_sum() {
  _impl_.min_sum_ = 0;
}
inline float CalibrationStatistics_AverageMinMaxStatistics::_internal_min_sum() const {
  return _impl_.min_sum_;
}
inline float CalibrationStatistics_AverageMinMaxStatistics::min_sum() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics.min_sum)
  return _internal_min_sum();
}
inline void CalibrationStatistics_AverageMinMaxStatistics::_internal_set_min_sum(float value) {
  
  _impl_.min_sum_ = value;
}
inline void CalibrationStatistics_AverageMinMaxStatistics::set_min_sum(float value) {
  _internal_set_min_sum(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics.min_sum)
}

// float max_sum = 2;
inline void CalibrationStatistics_AverageMinMaxStatistics::clear_max_sum() {
  _impl_.max_sum_ = 0;
}
inline float CalibrationStatistics_AverageMinMaxStatistics::_internal_max_sum() const {
  return _impl_.max_sum_;
}
inline float CalibrationStatistics_AverageMinMaxStatistics::max_sum() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics.max_sum)
  return _internal_max_sum();
}
inline void CalibrationStatistics_AverageMinMaxStatistics::_internal_set_max_sum(float value) {
  
  _impl_.max_sum_ = value;
}
inline void CalibrationStatistics_AverageMinMaxStatistics::set_max_sum(float value) {
  _internal_set_max_sum(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics.max_sum)
}

// int32 num_samples = 3;
inline void CalibrationStatistics_AverageMinMaxStatistics::clear_num_samples() {
  _impl_.num_samples_ = 0;
}
inline int32_t CalibrationStatistics_AverageMinMaxStatistics::_internal_num_samples() const {
  return _impl_.num_samples_;
}
inline int32_t CalibrationStatistics_AverageMinMaxStatistics::num_samples() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics.num_samples)
  return _internal_num_samples();
}
inline void CalibrationStatistics_AverageMinMaxStatistics::_internal_set_num_samples(int32_t value) {
  
  _impl_.num_samples_ = value;
}
inline void CalibrationStatistics_AverageMinMaxStatistics::set_num_samples(int32_t value) {
  _internal_set_num_samples(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics.num_samples)
}

// -------------------------------------------------------------------

// CalibrationStatistics_HistogramStatistics

// float bin_width = 1;
inline void CalibrationStatistics_HistogramStatistics::clear_bin_width() {
  _impl_.bin_width_ = 0;
}
inline float CalibrationStatistics_HistogramStatistics::_internal_bin_width() const {
  return _impl_.bin_width_;
}
inline float CalibrationStatistics_HistogramStatistics::bin_width() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.bin_width)
  return _internal_bin_width();
}
inline void CalibrationStatistics_HistogramStatistics::_internal_set_bin_width(float value) {
  
  _impl_.bin_width_ = value;
}
inline void CalibrationStatistics_HistogramStatistics::set_bin_width(float value) {
  _internal_set_bin_width(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.bin_width)
}

// float lower_bound = 2;
inline void CalibrationStatistics_HistogramStatistics::clear_lower_bound() {
  _impl_.lower_bound_ = 0;
}
inline float CalibrationStatistics_HistogramStatistics::_internal_lower_bound() const {
  return _impl_.lower_bound_;
}
inline float CalibrationStatistics_HistogramStatistics::lower_bound() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.lower_bound)
  return _internal_lower_bound();
}
inline void CalibrationStatistics_HistogramStatistics::_internal_set_lower_bound(float value) {
  
  _impl_.lower_bound_ = value;
}
inline void CalibrationStatistics_HistogramStatistics::set_lower_bound(float value) {
  _internal_set_lower_bound(value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.lower_bound)
}

// repeated float hist_freq = 3;
inline int CalibrationStatistics_HistogramStatistics::_internal_hist_freq_size() const {
  return _impl_.hist_freq_.size();
}
inline int CalibrationStatistics_HistogramStatistics::hist_freq_size() const {
  return _internal_hist_freq_size();
}
inline void CalibrationStatistics_HistogramStatistics::clear_hist_freq() {
  _impl_.hist_freq_.Clear();
}
inline float CalibrationStatistics_HistogramStatistics::_internal_hist_freq(int index) const {
  return _impl_.hist_freq_.Get(index);
}
inline float CalibrationStatistics_HistogramStatistics::hist_freq(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.hist_freq)
  return _internal_hist_freq(index);
}
inline void CalibrationStatistics_HistogramStatistics::set_hist_freq(int index, float value) {
  _impl_.hist_freq_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.hist_freq)
}
inline void CalibrationStatistics_HistogramStatistics::_internal_add_hist_freq(float value) {
  _impl_.hist_freq_.Add(value);
}
inline void CalibrationStatistics_HistogramStatistics::add_hist_freq(float value) {
  _internal_add_hist_freq(value);
  // @@protoc_insertion_point(field_add:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.hist_freq)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
CalibrationStatistics_HistogramStatistics::_internal_hist_freq() const {
  return _impl_.hist_freq_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
CalibrationStatistics_HistogramStatistics::hist_freq() const {
  // @@protoc_insertion_point(field_list:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.hist_freq)
  return _internal_hist_freq();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
CalibrationStatistics_HistogramStatistics::_internal_mutable_hist_freq() {
  return &_impl_.hist_freq_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
CalibrationStatistics_HistogramStatistics::mutable_hist_freq() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.calibrator.CalibrationStatistics.HistogramStatistics.hist_freq)
  return _internal_mutable_hist_freq();
}

// -------------------------------------------------------------------

// CalibrationStatistics

// .tensorflow.calibrator.CalibrationStatistics.MinMaxStatistics min_max_statistics = 1;
inline bool CalibrationStatistics::_internal_has_min_max_statistics() const {
  return this != internal_default_instance() && _impl_.min_max_statistics_ != nullptr;
}
inline bool CalibrationStatistics::has_min_max_statistics() const {
  return _internal_has_min_max_statistics();
}
inline void CalibrationStatistics::clear_min_max_statistics() {
  if (GetArenaForAllocation() == nullptr && _impl_.min_max_statistics_ != nullptr) {
    delete _impl_.min_max_statistics_;
  }
  _impl_.min_max_statistics_ = nullptr;
}
inline const ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics& CalibrationStatistics::_internal_min_max_statistics() const {
  const ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* p = _impl_.min_max_statistics_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics&>(
      ::tensorflow::calibrator::_CalibrationStatistics_MinMaxStatistics_default_instance_);
}
inline const ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics& CalibrationStatistics::min_max_statistics() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.min_max_statistics)
  return _internal_min_max_statistics();
}
inline void CalibrationStatistics::unsafe_arena_set_allocated_min_max_statistics(
    ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* min_max_statistics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.min_max_statistics_);
  }
  _impl_.min_max_statistics_ = min_max_statistics;
  if (min_max_statistics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.calibrator.CalibrationStatistics.min_max_statistics)
}
inline ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* CalibrationStatistics::release_min_max_statistics() {
  
  ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* temp = _impl_.min_max_statistics_;
  _impl_.min_max_statistics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* CalibrationStatistics::unsafe_arena_release_min_max_statistics() {
  // @@protoc_insertion_point(field_release:tensorflow.calibrator.CalibrationStatistics.min_max_statistics)
  
  ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* temp = _impl_.min_max_statistics_;
  _impl_.min_max_statistics_ = nullptr;
  return temp;
}
inline ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* CalibrationStatistics::_internal_mutable_min_max_statistics() {
  
  if (_impl_.min_max_statistics_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics>(GetArenaForAllocation());
    _impl_.min_max_statistics_ = p;
  }
  return _impl_.min_max_statistics_;
}
inline ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* CalibrationStatistics::mutable_min_max_statistics() {
  ::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* _msg = _internal_mutable_min_max_statistics();
  // @@protoc_insertion_point(field_mutable:tensorflow.calibrator.CalibrationStatistics.min_max_statistics)
  return _msg;
}
inline void CalibrationStatistics::set_allocated_min_max_statistics(::tensorflow::calibrator::CalibrationStatistics_MinMaxStatistics* min_max_statistics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.min_max_statistics_;
  }
  if (min_max_statistics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(min_max_statistics);
    if (message_arena != submessage_arena) {
      min_max_statistics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, min_max_statistics, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.min_max_statistics_ = min_max_statistics;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.calibrator.CalibrationStatistics.min_max_statistics)
}

// .tensorflow.calibrator.CalibrationStatistics.AverageMinMaxStatistics average_min_max_statistics = 2;
inline bool CalibrationStatistics::_internal_has_average_min_max_statistics() const {
  return this != internal_default_instance() && _impl_.average_min_max_statistics_ != nullptr;
}
inline bool CalibrationStatistics::has_average_min_max_statistics() const {
  return _internal_has_average_min_max_statistics();
}
inline void CalibrationStatistics::clear_average_min_max_statistics() {
  if (GetArenaForAllocation() == nullptr && _impl_.average_min_max_statistics_ != nullptr) {
    delete _impl_.average_min_max_statistics_;
  }
  _impl_.average_min_max_statistics_ = nullptr;
}
inline const ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics& CalibrationStatistics::_internal_average_min_max_statistics() const {
  const ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* p = _impl_.average_min_max_statistics_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics&>(
      ::tensorflow::calibrator::_CalibrationStatistics_AverageMinMaxStatistics_default_instance_);
}
inline const ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics& CalibrationStatistics::average_min_max_statistics() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.average_min_max_statistics)
  return _internal_average_min_max_statistics();
}
inline void CalibrationStatistics::unsafe_arena_set_allocated_average_min_max_statistics(
    ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* average_min_max_statistics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.average_min_max_statistics_);
  }
  _impl_.average_min_max_statistics_ = average_min_max_statistics;
  if (average_min_max_statistics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.calibrator.CalibrationStatistics.average_min_max_statistics)
}
inline ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* CalibrationStatistics::release_average_min_max_statistics() {
  
  ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* temp = _impl_.average_min_max_statistics_;
  _impl_.average_min_max_statistics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* CalibrationStatistics::unsafe_arena_release_average_min_max_statistics() {
  // @@protoc_insertion_point(field_release:tensorflow.calibrator.CalibrationStatistics.average_min_max_statistics)
  
  ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* temp = _impl_.average_min_max_statistics_;
  _impl_.average_min_max_statistics_ = nullptr;
  return temp;
}
inline ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* CalibrationStatistics::_internal_mutable_average_min_max_statistics() {
  
  if (_impl_.average_min_max_statistics_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics>(GetArenaForAllocation());
    _impl_.average_min_max_statistics_ = p;
  }
  return _impl_.average_min_max_statistics_;
}
inline ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* CalibrationStatistics::mutable_average_min_max_statistics() {
  ::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* _msg = _internal_mutable_average_min_max_statistics();
  // @@protoc_insertion_point(field_mutable:tensorflow.calibrator.CalibrationStatistics.average_min_max_statistics)
  return _msg;
}
inline void CalibrationStatistics::set_allocated_average_min_max_statistics(::tensorflow::calibrator::CalibrationStatistics_AverageMinMaxStatistics* average_min_max_statistics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.average_min_max_statistics_;
  }
  if (average_min_max_statistics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(average_min_max_statistics);
    if (message_arena != submessage_arena) {
      average_min_max_statistics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, average_min_max_statistics, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.average_min_max_statistics_ = average_min_max_statistics;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.calibrator.CalibrationStatistics.average_min_max_statistics)
}

// .tensorflow.calibrator.CalibrationStatistics.HistogramStatistics histogram_statistics = 3;
inline bool CalibrationStatistics::_internal_has_histogram_statistics() const {
  return this != internal_default_instance() && _impl_.histogram_statistics_ != nullptr;
}
inline bool CalibrationStatistics::has_histogram_statistics() const {
  return _internal_has_histogram_statistics();
}
inline void CalibrationStatistics::clear_histogram_statistics() {
  if (GetArenaForAllocation() == nullptr && _impl_.histogram_statistics_ != nullptr) {
    delete _impl_.histogram_statistics_;
  }
  _impl_.histogram_statistics_ = nullptr;
}
inline const ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics& CalibrationStatistics::_internal_histogram_statistics() const {
  const ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* p = _impl_.histogram_statistics_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics&>(
      ::tensorflow::calibrator::_CalibrationStatistics_HistogramStatistics_default_instance_);
}
inline const ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics& CalibrationStatistics::histogram_statistics() const {
  // @@protoc_insertion_point(field_get:tensorflow.calibrator.CalibrationStatistics.histogram_statistics)
  return _internal_histogram_statistics();
}
inline void CalibrationStatistics::unsafe_arena_set_allocated_histogram_statistics(
    ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* histogram_statistics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.histogram_statistics_);
  }
  _impl_.histogram_statistics_ = histogram_statistics;
  if (histogram_statistics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.calibrator.CalibrationStatistics.histogram_statistics)
}
inline ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* CalibrationStatistics::release_histogram_statistics() {
  
  ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* temp = _impl_.histogram_statistics_;
  _impl_.histogram_statistics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* CalibrationStatistics::unsafe_arena_release_histogram_statistics() {
  // @@protoc_insertion_point(field_release:tensorflow.calibrator.CalibrationStatistics.histogram_statistics)
  
  ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* temp = _impl_.histogram_statistics_;
  _impl_.histogram_statistics_ = nullptr;
  return temp;
}
inline ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* CalibrationStatistics::_internal_mutable_histogram_statistics() {
  
  if (_impl_.histogram_statistics_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics>(GetArenaForAllocation());
    _impl_.histogram_statistics_ = p;
  }
  return _impl_.histogram_statistics_;
}
inline ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* CalibrationStatistics::mutable_histogram_statistics() {
  ::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* _msg = _internal_mutable_histogram_statistics();
  // @@protoc_insertion_point(field_mutable:tensorflow.calibrator.CalibrationStatistics.histogram_statistics)
  return _msg;
}
inline void CalibrationStatistics::set_allocated_histogram_statistics(::tensorflow::calibrator::CalibrationStatistics_HistogramStatistics* histogram_statistics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.histogram_statistics_;
  }
  if (histogram_statistics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(histogram_statistics);
    if (message_arena != submessage_arena) {
      histogram_statistics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, histogram_statistics, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.histogram_statistics_ = histogram_statistics;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.calibrator.CalibrationStatistics.histogram_statistics)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CalibrationStatisticsMap

// map<string, .tensorflow.calibrator.CalibrationStatistics> statistics = 1;
inline int CalibrationStatisticsMap::_internal_statistics_size() const {
  return _impl_.statistics_.size();
}
inline int CalibrationStatisticsMap::statistics_size() const {
  return _internal_statistics_size();
}
inline void CalibrationStatisticsMap::clear_statistics() {
  _impl_.statistics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >&
CalibrationStatisticsMap::_internal_statistics() const {
  return _impl_.statistics_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >&
CalibrationStatisticsMap::statistics() const {
  // @@protoc_insertion_point(field_map:tensorflow.calibrator.CalibrationStatisticsMap.statistics)
  return _internal_statistics();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >*
CalibrationStatisticsMap::_internal_mutable_statistics() {
  return _impl_.statistics_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::calibrator::CalibrationStatistics >*
CalibrationStatisticsMap::mutable_statistics() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.calibrator.CalibrationStatisticsMap.statistics)
  return _internal_mutable_statistics();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace calibrator
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fcalibrator_2fcalibration_5fstatistics_2eproto
