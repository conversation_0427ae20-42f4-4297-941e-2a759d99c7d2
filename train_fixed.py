import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
import keras
from keras.metrics import R2Score
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {tf.config.list_physical_devices('GPU')}")

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)

# 加载数据
train_validate_examples = np.load('inputs/train_validate_data.npy')
train_validate_labels = np.load('inputs/train_validate_label.npy')

print(f"Original data shapes - Examples: {train_validate_examples.shape}, Labels: {train_validate_labels.shape}")
print(f"Examples range: {np.min(train_validate_examples):.2e} to {np.max(train_validate_examples):.2e}")
print(f"Labels range: {np.min(train_validate_labels):.2e} to {np.max(train_validate_labels):.2e}")

# 分析每个标签参数的分布
print("\nAnalyzing label distributions:")
for i in range(train_validate_labels.shape[1]):
    col = train_validate_labels[:, i]
    print(f"Parameter {i}: min={np.min(col):.2e}, max={np.max(col):.2e}, "
          f"range_ratio={np.max(col)/np.min(col):.2e}")

# 数据预处理
print("\nApplying preprocessing...")

# 1. 对输入数据使用log10变换
train_validate_examples_safe = np.where(train_validate_examples <= 0, 1e-20, train_validate_examples)
train_validate_examples_log = np.log10(train_validate_examples_safe)

# 2. 对标签数据使用log变换（针对范围很大的参数）
train_validate_labels_processed = np.copy(train_validate_labels)
label_transforms = []

for i in range(train_validate_labels.shape[1]):
    col = train_validate_labels[:, i]
    range_ratio = np.max(col) / np.min(col) if np.min(col) > 0 else float('inf')
    
    if np.min(col) > 0 and range_ratio > 100:
        train_validate_labels_processed[:, i] = np.log10(col)
        label_transforms.append('log10')
        print(f"Parameter {i}: Applied log10 transform (range ratio: {range_ratio:.2e})")
    else:
        train_validate_labels_processed[:, i] = col
        label_transforms.append('none')
        print(f"Parameter {i}: No transform (range ratio: {range_ratio:.2e})")

# 3. 数据分割
train_examples, validate_examples, train_labels, validate_labels = train_test_split(
    train_validate_examples_log, train_validate_labels_processed,
    test_size=0.2, random_state=42, shuffle=True
)

print(f"\nAfter split:")
print(f"Train: {train_examples.shape}, {train_labels.shape}")
print(f"Validation: {validate_examples.shape}, {validate_labels.shape}")

# 4. 标准化
examples_scaler = StandardScaler()
labels_scaler = RobustScaler()

train_examples_scaled = examples_scaler.fit_transform(train_examples)
validate_examples_scaled = examples_scaler.transform(validate_examples)

train_labels_scaled = labels_scaler.fit_transform(train_labels)
validate_labels_scaled = labels_scaler.transform(validate_labels)

print(f"\nAfter scaling:")
print(f"Train examples: mean={np.mean(train_examples_scaled):.3f}, std={np.std(train_examples_scaled):.3f}")
print(f"Train labels: mean={np.mean(train_labels_scaled):.3f}, std={np.std(train_labels_scaled):.3f}")

# 5. 创建数据集
BATCH_SIZE = 32
train_dataset = tf.data.Dataset.from_tensor_slices((train_examples_scaled, train_labels_scaled))
validate_dataset = tf.data.Dataset.from_tensor_slices((validate_examples_scaled, validate_labels_scaled))

train_dataset = train_dataset.shuffle(1000).batch(BATCH_SIZE)
validate_dataset = validate_dataset.batch(BATCH_SIZE)

# 6. 创建改进的模型
def create_improved_model():
    model = keras.Sequential([
        keras.Input(shape=(51,)),
        
        # 更深的网络，使用批标准化和dropout
        keras.layers.Dense(512, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.3),
        
        keras.layers.Dense(256, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.3),
        
        keras.layers.Dense(128, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.2),
        
        keras.layers.Dense(64, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.2),
        
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dropout(0.1),
        
        # 输出层
        keras.layers.Dense(6)
    ])
    return model

model = create_improved_model()

# 7. 编译模型
model.compile(
    optimizer=keras.optimizers.Adam(learning_rate=0.001),
    loss='mse',
    metrics=[R2Score(class_aggregation='uniform_average'), 'mae']
)

model.summary()

# 8. 自定义回调函数
class DetailedCallback(keras.callbacks.Callback):
    def __init__(self, validate_dataset, examples_scaler, labels_scaler, label_transforms, interval=100):
        super().__init__()
        self.validate_dataset = validate_dataset
        self.examples_scaler = examples_scaler
        self.labels_scaler = labels_scaler
        self.label_transforms = label_transforms
        self.interval = interval
        self.history = {'loss': [], 'val_loss': [], 'r2': [], 'val_r2': []}

    def on_epoch_end(self, epoch, logs=None):
        self.history['loss'].append(logs.get("loss"))
        self.history['val_loss'].append(logs.get("val_loss"))
        self.history['r2'].append(logs.get("r2_score"))
        self.history['val_r2'].append(logs.get("val_r2_score"))

        if (epoch + 1) % self.interval == 0:
            # 获取验证数据进行详细分析
            for val_x, val_y in self.validate_dataset.take(1):
                pred_scaled = self.model(val_x, training=False).numpy()
                true_scaled = val_y.numpy()
                
                # 反标准化
                pred_unscaled = self.labels_scaler.inverse_transform(pred_scaled)
                true_unscaled = self.labels_scaler.inverse_transform(true_scaled)
                
                # 反变换
                pred_original = np.copy(pred_unscaled)
                true_original = np.copy(true_unscaled)
                
                for i, transform in enumerate(self.label_transforms):
                    if transform == 'log10':
                        pred_original[:, i] = 10 ** pred_unscaled[:, i]
                        true_original[:, i] = 10 ** true_unscaled[:, i]
                
                # 计算每个参数的R2分数
                r2_scores = []
                for i in range(6):
                    r2 = r2_score(true_original[:, i], pred_original[:, i])
                    r2_scores.append(r2)
                
                print(f"\n{'='*60}")
                print(f"Epoch {epoch + 1} Detailed Results:")
                print(f"Overall - Loss: {logs.get('loss'):.4f}, Val Loss: {logs.get('val_loss'):.4f}")
                print(f"Overall - R2: {logs.get('r2_score'):.4f}, Val R2: {logs.get('val_r2_score'):.4f}")
                print(f"Individual R2 scores: {[f'{r2:.3f}' for r2 in r2_scores]}")
                print("\nFirst sample comparison (original scale):")
                for i in range(6):
                    error_pct = abs(pred_original[0, i] - true_original[0, i]) / true_original[0, i] * 100
                    print(f"  Param {i}: pred={pred_original[0, i]:.2e}, true={true_original[0, i]:.2e}, error={error_pct:.1f}%")
                print(f"{'='*60}")
                break

    def on_train_end(self, logs=None):
        # 绘制训练历史
        fig, ((ax1, ax2)) = plt.subplots(1, 2, figsize=(15, 5))
        
        epochs = range(1, len(self.history['loss']) + 1)
        
        # Loss plot
        ax1.plot(epochs, self.history['loss'], 'b-', label="Training Loss")
        ax1.plot(epochs, self.history['val_loss'], 'r-', label="Validation Loss")
        ax1.set_xlabel("Epoch")
        ax1.set_ylabel("Loss (MSE)")
        ax1.set_title("Training vs Validation Loss")
        ax1.legend()
        ax1.grid(True)
        ax1.set_yscale('log')
        
        # R2 plot
        ax2.plot(epochs, self.history['r2'], 'b-', label="Training R2")
        ax2.plot(epochs, self.history['val_r2'], 'r-', label="Validation R2")
        ax2.set_xlabel("Epoch")
        ax2.set_ylabel("R2 Score")
        ax2.set_title("Training vs Validation R2")
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=150, bbox_inches='tight')
        plt.show()

# 9. 设置回调函数
callbacks = [
    DetailedCallback(validate_dataset, examples_scaler, labels_scaler, label_transforms, interval=200),
    keras.callbacks.EarlyStopping(monitor='val_loss', patience=300, restore_best_weights=True, verbose=1),
    keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=150, min_lr=1e-6, verbose=1),
    keras.callbacks.ModelCheckpoint('best_model.h5', monitor='val_loss', save_best_only=True, verbose=1)
]

# 10. 训练模型
EPOCHS = 2000
print(f"\nStarting training for {EPOCHS} epochs...")
print("This may take a while. The model will automatically stop early if no improvement is seen.")

history = model.fit(
    train_dataset,
    epochs=EPOCHS,
    validation_data=validate_dataset,
    callbacks=callbacks,
    verbose=1
)

print("\nTraining completed!")

# 11. 最终评估
print("\nFinal evaluation on validation set:")
val_loss, val_r2, val_mae = model.evaluate(validate_dataset, verbose=0)
print(f"Final validation loss: {val_loss:.4f}")
print(f"Final validation R2: {val_r2:.4f}")
print(f"Final validation MAE: {val_mae:.4f}")

# 保存模型和预处理器
model.save('final_model.h5')
np.save('examples_scaler_mean.npy', examples_scaler.mean_)
np.save('examples_scaler_scale.npy', examples_scaler.scale_)
np.save('labels_scaler_center.npy', labels_scaler.center_)
np.save('labels_scaler_scale.npy', labels_scaler.scale_)
np.save('label_transforms.npy', label_transforms)

print("\nModel and preprocessing parameters saved!")
print("Files saved: final_model.h5, *_scaler_*.npy, label_transforms.npy")
