/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#ifndef GRPC_CORE_EXT_TRANSPORT_CHTTP2_TRANSPORT_FRAME_GOAWAY_H
#define GRPC_CORE_EXT_TRANSPORT_CHTTP2_TRANSPORT_FRAME_GOAWAY_H

#include <grpc/support/port_platform.h>

#include <grpc/slice.h>
#include <grpc/slice_buffer.h>
#include "src/core/ext/transport/chttp2/transport/frame.h"

typedef enum {
  GRPC_CHTTP2_GOAWAY_LSI0,
  G<PERSON>C_CHTTP2_GOAWAY_LSI1,
  G<PERSON><PERSON>_CHTTP2_GOAWAY_LSI2,
  GRPC_CHTTP2_GOAWAY_LSI3,
  GRPC_CHTTP2_GOAWAY_ERR0,
  GRPC_CHTTP2_GOAWAY_ERR1,
  GRPC_CHTTP2_GOAWAY_ERR2,
  GRPC_CHTTP2_GOAWAY_ERR3,
  GRPC_CHTTP2_GOAWAY_DEBUG
} grpc_chttp2_goaway_parse_state;

typedef struct {
  grpc_chttp2_goaway_parse_state state;
  uint32_t last_stream_id;
  uint32_t error_code;
  char* debug_data;
  uint32_t debug_length;
  uint32_t debug_pos;
} grpc_chttp2_goaway_parser;

void grpc_chttp2_goaway_parser_init(grpc_chttp2_goaway_parser* p);
void grpc_chttp2_goaway_parser_destroy(grpc_chttp2_goaway_parser* p);
grpc_error* grpc_chttp2_goaway_parser_begin_frame(
    grpc_chttp2_goaway_parser* parser, uint32_t length, uint8_t flags);
grpc_error* grpc_chttp2_goaway_parser_parse(void* parser,
                                            grpc_chttp2_transport* t,
                                            grpc_chttp2_stream* s,
                                            const grpc_slice& slice,
                                            int is_last);

void grpc_chttp2_goaway_append(uint32_t last_stream_id, uint32_t error_code,
                               const grpc_slice& debug_data,
                               grpc_slice_buffer* slice_buffer);

#endif /* GRPC_CORE_EXT_TRANSPORT_CHTTP2_TRANSPORT_FRAME_GOAWAY_H */
