// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/cpu/onednn_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
namespace xla {
namespace cpu {
class OneDnnConvolutionConfig;
struct OneDnnConvolutionConfigDefaultTypeInternal;
extern OneDnnConvolutionConfigDefaultTypeInternal _OneDnnConvolutionConfig_default_instance_;
class OneDnnDataLayoutProto;
struct OneDnnDataLayoutProtoDefaultTypeInternal;
extern OneDnnDataLayoutProtoDefaultTypeInternal _OneDnnDataLayoutProto_default_instance_;
class OneDnnFactorLayoutProto;
struct OneDnnFactorLayoutProtoDefaultTypeInternal;
extern OneDnnFactorLayoutProtoDefaultTypeInternal _OneDnnFactorLayoutProto_default_instance_;
class OneDnnFilterLayoutProto;
struct OneDnnFilterLayoutProtoDefaultTypeInternal;
extern OneDnnFilterLayoutProtoDefaultTypeInternal _OneDnnFilterLayoutProto_default_instance_;
class OneDnnFusionConfig;
struct OneDnnFusionConfigDefaultTypeInternal;
extern OneDnnFusionConfigDefaultTypeInternal _OneDnnFusionConfig_default_instance_;
class OneDnnMatMulConfig;
struct OneDnnMatMulConfigDefaultTypeInternal;
extern OneDnnMatMulConfigDefaultTypeInternal _OneDnnMatMulConfig_default_instance_;
class OneDnnNormConfig;
struct OneDnnNormConfigDefaultTypeInternal;
extern OneDnnNormConfigDefaultTypeInternal _OneDnnNormConfig_default_instance_;
class OneDnnOptimizationConfig;
struct OneDnnOptimizationConfigDefaultTypeInternal;
extern OneDnnOptimizationConfigDefaultTypeInternal _OneDnnOptimizationConfig_default_instance_;
class OneDnnSoftmaxConfig;
struct OneDnnSoftmaxConfigDefaultTypeInternal;
extern OneDnnSoftmaxConfigDefaultTypeInternal _OneDnnSoftmaxConfig_default_instance_;
class OneDnnTensorLayoutProto;
struct OneDnnTensorLayoutProtoDefaultTypeInternal;
extern OneDnnTensorLayoutProtoDefaultTypeInternal _OneDnnTensorLayoutProto_default_instance_;
class OneDnnWindowProto;
struct OneDnnWindowProtoDefaultTypeInternal;
extern OneDnnWindowProtoDefaultTypeInternal _OneDnnWindowProto_default_instance_;
}  // namespace cpu
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::cpu::OneDnnConvolutionConfig* Arena::CreateMaybeMessage<::xla::cpu::OneDnnConvolutionConfig>(Arena*);
template<> ::xla::cpu::OneDnnDataLayoutProto* Arena::CreateMaybeMessage<::xla::cpu::OneDnnDataLayoutProto>(Arena*);
template<> ::xla::cpu::OneDnnFactorLayoutProto* Arena::CreateMaybeMessage<::xla::cpu::OneDnnFactorLayoutProto>(Arena*);
template<> ::xla::cpu::OneDnnFilterLayoutProto* Arena::CreateMaybeMessage<::xla::cpu::OneDnnFilterLayoutProto>(Arena*);
template<> ::xla::cpu::OneDnnFusionConfig* Arena::CreateMaybeMessage<::xla::cpu::OneDnnFusionConfig>(Arena*);
template<> ::xla::cpu::OneDnnMatMulConfig* Arena::CreateMaybeMessage<::xla::cpu::OneDnnMatMulConfig>(Arena*);
template<> ::xla::cpu::OneDnnNormConfig* Arena::CreateMaybeMessage<::xla::cpu::OneDnnNormConfig>(Arena*);
template<> ::xla::cpu::OneDnnOptimizationConfig* Arena::CreateMaybeMessage<::xla::cpu::OneDnnOptimizationConfig>(Arena*);
template<> ::xla::cpu::OneDnnSoftmaxConfig* Arena::CreateMaybeMessage<::xla::cpu::OneDnnSoftmaxConfig>(Arena*);
template<> ::xla::cpu::OneDnnTensorLayoutProto* Arena::CreateMaybeMessage<::xla::cpu::OneDnnTensorLayoutProto>(Arena*);
template<> ::xla::cpu::OneDnnWindowProto* Arena::CreateMaybeMessage<::xla::cpu::OneDnnWindowProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace cpu {

enum OneDnnFusionConfig_FusionKind : int {
  OneDnnFusionConfig_FusionKind_UNDEFINED = 0,
  OneDnnFusionConfig_FusionKind_BIAS = 1,
  OneDnnFusionConfig_FusionKind_RELU = 2,
  OneDnnFusionConfig_FusionKind_TANH = 3,
  OneDnnFusionConfig_FusionKind_GELU_ERF = 4,
  OneDnnFusionConfig_FusionKind_GELU_TANH = 5,
  OneDnnFusionConfig_FusionKind_BINARY_ADD = 6,
  OneDnnFusionConfig_FusionKind_LINEAR = 7,
  OneDnnFusionConfig_FusionKind_ELU = 8,
  OneDnnFusionConfig_FusionKind_RELU6 = 9,
  OneDnnFusionConfig_FusionKind_SIGMOID = 10,
  OneDnnFusionConfig_FusionKind_OneDnnFusionConfig_FusionKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OneDnnFusionConfig_FusionKind_OneDnnFusionConfig_FusionKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OneDnnFusionConfig_FusionKind_IsValid(int value);
constexpr OneDnnFusionConfig_FusionKind OneDnnFusionConfig_FusionKind_FusionKind_MIN = OneDnnFusionConfig_FusionKind_UNDEFINED;
constexpr OneDnnFusionConfig_FusionKind OneDnnFusionConfig_FusionKind_FusionKind_MAX = OneDnnFusionConfig_FusionKind_SIGMOID;
constexpr int OneDnnFusionConfig_FusionKind_FusionKind_ARRAYSIZE = OneDnnFusionConfig_FusionKind_FusionKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OneDnnFusionConfig_FusionKind_descriptor();
template<typename T>
inline const std::string& OneDnnFusionConfig_FusionKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OneDnnFusionConfig_FusionKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OneDnnFusionConfig_FusionKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OneDnnFusionConfig_FusionKind_descriptor(), enum_t_value);
}
inline bool OneDnnFusionConfig_FusionKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OneDnnFusionConfig_FusionKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OneDnnFusionConfig_FusionKind>(
    OneDnnFusionConfig_FusionKind_descriptor(), name, value);
}
enum OneDnnNormConfig_ScaleAndShift : int {
  OneDnnNormConfig_ScaleAndShift_UNDEFINED = 0,
  OneDnnNormConfig_ScaleAndShift_SCALE = 1,
  OneDnnNormConfig_ScaleAndShift_SHIFT = 2,
  OneDnnNormConfig_ScaleAndShift_SCALE_AND_SHIFT = 3,
  OneDnnNormConfig_ScaleAndShift_OneDnnNormConfig_ScaleAndShift_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OneDnnNormConfig_ScaleAndShift_OneDnnNormConfig_ScaleAndShift_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OneDnnNormConfig_ScaleAndShift_IsValid(int value);
constexpr OneDnnNormConfig_ScaleAndShift OneDnnNormConfig_ScaleAndShift_ScaleAndShift_MIN = OneDnnNormConfig_ScaleAndShift_UNDEFINED;
constexpr OneDnnNormConfig_ScaleAndShift OneDnnNormConfig_ScaleAndShift_ScaleAndShift_MAX = OneDnnNormConfig_ScaleAndShift_SCALE_AND_SHIFT;
constexpr int OneDnnNormConfig_ScaleAndShift_ScaleAndShift_ARRAYSIZE = OneDnnNormConfig_ScaleAndShift_ScaleAndShift_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OneDnnNormConfig_ScaleAndShift_descriptor();
template<typename T>
inline const std::string& OneDnnNormConfig_ScaleAndShift_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OneDnnNormConfig_ScaleAndShift>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OneDnnNormConfig_ScaleAndShift_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OneDnnNormConfig_ScaleAndShift_descriptor(), enum_t_value);
}
inline bool OneDnnNormConfig_ScaleAndShift_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OneDnnNormConfig_ScaleAndShift* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OneDnnNormConfig_ScaleAndShift>(
    OneDnnNormConfig_ScaleAndShift_descriptor(), name, value);
}
// ===================================================================

class OneDnnDataLayoutProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnDataLayoutProto) */ {
 public:
  inline OneDnnDataLayoutProto() : OneDnnDataLayoutProto(nullptr) {}
  ~OneDnnDataLayoutProto() override;
  explicit PROTOBUF_CONSTEXPR OneDnnDataLayoutProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnDataLayoutProto(const OneDnnDataLayoutProto& from);
  OneDnnDataLayoutProto(OneDnnDataLayoutProto&& from) noexcept
    : OneDnnDataLayoutProto() {
    *this = ::std::move(from);
  }

  inline OneDnnDataLayoutProto& operator=(const OneDnnDataLayoutProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnDataLayoutProto& operator=(OneDnnDataLayoutProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnDataLayoutProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnDataLayoutProto* internal_default_instance() {
    return reinterpret_cast<const OneDnnDataLayoutProto*>(
               &_OneDnnDataLayoutProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(OneDnnDataLayoutProto& a, OneDnnDataLayoutProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnDataLayoutProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnDataLayoutProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnDataLayoutProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnDataLayoutProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnDataLayoutProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnDataLayoutProto& from) {
    OneDnnDataLayoutProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnDataLayoutProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnDataLayoutProto";
  }
  protected:
  explicit OneDnnDataLayoutProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSpatialDimsFieldNumber = 3,
    kBatchDimFieldNumber = 1,
    kFeatureDimFieldNumber = 2,
  };
  // repeated uint64 spatial_dims = 3;
  int spatial_dims_size() const;
  private:
  int _internal_spatial_dims_size() const;
  public:
  void clear_spatial_dims();
  private:
  uint64_t _internal_spatial_dims(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_spatial_dims() const;
  void _internal_add_spatial_dims(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_spatial_dims();
  public:
  uint64_t spatial_dims(int index) const;
  void set_spatial_dims(int index, uint64_t value);
  void add_spatial_dims(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      spatial_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_spatial_dims();

  // uint64 batch_dim = 1;
  void clear_batch_dim();
  uint64_t batch_dim() const;
  void set_batch_dim(uint64_t value);
  private:
  uint64_t _internal_batch_dim() const;
  void _internal_set_batch_dim(uint64_t value);
  public:

  // uint64 feature_dim = 2;
  void clear_feature_dim();
  uint64_t feature_dim() const;
  void set_feature_dim(uint64_t value);
  private:
  uint64_t _internal_feature_dim() const;
  void _internal_set_feature_dim(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnDataLayoutProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > spatial_dims_;
    mutable std::atomic<int> _spatial_dims_cached_byte_size_;
    uint64_t batch_dim_;
    uint64_t feature_dim_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnFilterLayoutProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnFilterLayoutProto) */ {
 public:
  inline OneDnnFilterLayoutProto() : OneDnnFilterLayoutProto(nullptr) {}
  ~OneDnnFilterLayoutProto() override;
  explicit PROTOBUF_CONSTEXPR OneDnnFilterLayoutProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnFilterLayoutProto(const OneDnnFilterLayoutProto& from);
  OneDnnFilterLayoutProto(OneDnnFilterLayoutProto&& from) noexcept
    : OneDnnFilterLayoutProto() {
    *this = ::std::move(from);
  }

  inline OneDnnFilterLayoutProto& operator=(const OneDnnFilterLayoutProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnFilterLayoutProto& operator=(OneDnnFilterLayoutProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnFilterLayoutProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnFilterLayoutProto* internal_default_instance() {
    return reinterpret_cast<const OneDnnFilterLayoutProto*>(
               &_OneDnnFilterLayoutProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OneDnnFilterLayoutProto& a, OneDnnFilterLayoutProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnFilterLayoutProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnFilterLayoutProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnFilterLayoutProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnFilterLayoutProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnFilterLayoutProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnFilterLayoutProto& from) {
    OneDnnFilterLayoutProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnFilterLayoutProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnFilterLayoutProto";
  }
  protected:
  explicit OneDnnFilterLayoutProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSpatialDimsFieldNumber = 3,
    kShapeFieldNumber = 4,
    kInputFeatureDimFieldNumber = 1,
    kOutputFeatureDimFieldNumber = 2,
  };
  // repeated uint64 spatial_dims = 3;
  int spatial_dims_size() const;
  private:
  int _internal_spatial_dims_size() const;
  public:
  void clear_spatial_dims();
  private:
  uint64_t _internal_spatial_dims(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_spatial_dims() const;
  void _internal_add_spatial_dims(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_spatial_dims();
  public:
  uint64_t spatial_dims(int index) const;
  void set_spatial_dims(int index, uint64_t value);
  void add_spatial_dims(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      spatial_dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_spatial_dims();

  // repeated uint64 shape = 4;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  private:
  uint64_t _internal_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_shape() const;
  void _internal_add_shape(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_shape();
  public:
  uint64_t shape(int index) const;
  void set_shape(int index, uint64_t value);
  void add_shape(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_shape();

  // uint64 input_feature_dim = 1;
  void clear_input_feature_dim();
  uint64_t input_feature_dim() const;
  void set_input_feature_dim(uint64_t value);
  private:
  uint64_t _internal_input_feature_dim() const;
  void _internal_set_input_feature_dim(uint64_t value);
  public:

  // uint64 output_feature_dim = 2;
  void clear_output_feature_dim();
  uint64_t output_feature_dim() const;
  void set_output_feature_dim(uint64_t value);
  private:
  uint64_t _internal_output_feature_dim() const;
  void _internal_set_output_feature_dim(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnFilterLayoutProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > spatial_dims_;
    mutable std::atomic<int> _spatial_dims_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > shape_;
    mutable std::atomic<int> _shape_cached_byte_size_;
    uint64_t input_feature_dim_;
    uint64_t output_feature_dim_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnFactorLayoutProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnFactorLayoutProto) */ {
 public:
  inline OneDnnFactorLayoutProto() : OneDnnFactorLayoutProto(nullptr) {}
  ~OneDnnFactorLayoutProto() override;
  explicit PROTOBUF_CONSTEXPR OneDnnFactorLayoutProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnFactorLayoutProto(const OneDnnFactorLayoutProto& from);
  OneDnnFactorLayoutProto(OneDnnFactorLayoutProto&& from) noexcept
    : OneDnnFactorLayoutProto() {
    *this = ::std::move(from);
  }

  inline OneDnnFactorLayoutProto& operator=(const OneDnnFactorLayoutProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnFactorLayoutProto& operator=(OneDnnFactorLayoutProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnFactorLayoutProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnFactorLayoutProto* internal_default_instance() {
    return reinterpret_cast<const OneDnnFactorLayoutProto*>(
               &_OneDnnFactorLayoutProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OneDnnFactorLayoutProto& a, OneDnnFactorLayoutProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnFactorLayoutProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnFactorLayoutProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnFactorLayoutProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnFactorLayoutProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnFactorLayoutProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnFactorLayoutProto& from) {
    OneDnnFactorLayoutProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnFactorLayoutProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnFactorLayoutProto";
  }
  protected:
  explicit OneDnnFactorLayoutProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 1,
    kShapeFieldNumber = 2,
  };
  // repeated uint64 dimensions = 1;
  int dimensions_size() const;
  private:
  int _internal_dimensions_size() const;
  public:
  void clear_dimensions();
  private:
  uint64_t _internal_dimensions(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_dimensions() const;
  void _internal_add_dimensions(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_dimensions();
  public:
  uint64_t dimensions(int index) const;
  void set_dimensions(int index, uint64_t value);
  void add_dimensions(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_dimensions();

  // repeated uint64 shape = 2;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  private:
  uint64_t _internal_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_shape() const;
  void _internal_add_shape(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_shape();
  public:
  uint64_t shape(int index) const;
  void set_shape(int index, uint64_t value);
  void add_shape(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_shape();

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnFactorLayoutProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > dimensions_;
    mutable std::atomic<int> _dimensions_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > shape_;
    mutable std::atomic<int> _shape_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnOptimizationConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnOptimizationConfig) */ {
 public:
  inline OneDnnOptimizationConfig() : OneDnnOptimizationConfig(nullptr) {}
  ~OneDnnOptimizationConfig() override;
  explicit PROTOBUF_CONSTEXPR OneDnnOptimizationConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnOptimizationConfig(const OneDnnOptimizationConfig& from);
  OneDnnOptimizationConfig(OneDnnOptimizationConfig&& from) noexcept
    : OneDnnOptimizationConfig() {
    *this = ::std::move(from);
  }

  inline OneDnnOptimizationConfig& operator=(const OneDnnOptimizationConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnOptimizationConfig& operator=(OneDnnOptimizationConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnOptimizationConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnOptimizationConfig* internal_default_instance() {
    return reinterpret_cast<const OneDnnOptimizationConfig*>(
               &_OneDnnOptimizationConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OneDnnOptimizationConfig& a, OneDnnOptimizationConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnOptimizationConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnOptimizationConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnOptimizationConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnOptimizationConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnOptimizationConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnOptimizationConfig& from) {
    OneDnnOptimizationConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnOptimizationConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnOptimizationConfig";
  }
  protected:
  explicit OneDnnOptimizationConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWeightsPrepackedFieldNumber = 1,
    kUserScratchpadFieldNumber = 2,
    kBiasBroadcastFieldNumber = 3,
  };
  // bool weights_prepacked = 1;
  void clear_weights_prepacked();
  bool weights_prepacked() const;
  void set_weights_prepacked(bool value);
  private:
  bool _internal_weights_prepacked() const;
  void _internal_set_weights_prepacked(bool value);
  public:

  // bool user_scratchpad = 2;
  void clear_user_scratchpad();
  bool user_scratchpad() const;
  void set_user_scratchpad(bool value);
  private:
  bool _internal_user_scratchpad() const;
  void _internal_set_user_scratchpad(bool value);
  public:

  // bool bias_broadcast = 3;
  void clear_bias_broadcast();
  bool bias_broadcast() const;
  void set_bias_broadcast(bool value);
  private:
  bool _internal_bias_broadcast() const;
  void _internal_set_bias_broadcast(bool value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnOptimizationConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool weights_prepacked_;
    bool user_scratchpad_;
    bool bias_broadcast_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnFusionConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnFusionConfig) */ {
 public:
  inline OneDnnFusionConfig() : OneDnnFusionConfig(nullptr) {}
  ~OneDnnFusionConfig() override;
  explicit PROTOBUF_CONSTEXPR OneDnnFusionConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnFusionConfig(const OneDnnFusionConfig& from);
  OneDnnFusionConfig(OneDnnFusionConfig&& from) noexcept
    : OneDnnFusionConfig() {
    *this = ::std::move(from);
  }

  inline OneDnnFusionConfig& operator=(const OneDnnFusionConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnFusionConfig& operator=(OneDnnFusionConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnFusionConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnFusionConfig* internal_default_instance() {
    return reinterpret_cast<const OneDnnFusionConfig*>(
               &_OneDnnFusionConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(OneDnnFusionConfig& a, OneDnnFusionConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnFusionConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnFusionConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnFusionConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnFusionConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnFusionConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnFusionConfig& from) {
    OneDnnFusionConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnFusionConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnFusionConfig";
  }
  protected:
  explicit OneDnnFusionConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OneDnnFusionConfig_FusionKind FusionKind;
  static constexpr FusionKind UNDEFINED =
    OneDnnFusionConfig_FusionKind_UNDEFINED;
  static constexpr FusionKind BIAS =
    OneDnnFusionConfig_FusionKind_BIAS;
  static constexpr FusionKind RELU =
    OneDnnFusionConfig_FusionKind_RELU;
  static constexpr FusionKind TANH =
    OneDnnFusionConfig_FusionKind_TANH;
  static constexpr FusionKind GELU_ERF =
    OneDnnFusionConfig_FusionKind_GELU_ERF;
  static constexpr FusionKind GELU_TANH =
    OneDnnFusionConfig_FusionKind_GELU_TANH;
  static constexpr FusionKind BINARY_ADD =
    OneDnnFusionConfig_FusionKind_BINARY_ADD;
  static constexpr FusionKind LINEAR =
    OneDnnFusionConfig_FusionKind_LINEAR;
  static constexpr FusionKind ELU =
    OneDnnFusionConfig_FusionKind_ELU;
  static constexpr FusionKind RELU6 =
    OneDnnFusionConfig_FusionKind_RELU6;
  static constexpr FusionKind SIGMOID =
    OneDnnFusionConfig_FusionKind_SIGMOID;
  static inline bool FusionKind_IsValid(int value) {
    return OneDnnFusionConfig_FusionKind_IsValid(value);
  }
  static constexpr FusionKind FusionKind_MIN =
    OneDnnFusionConfig_FusionKind_FusionKind_MIN;
  static constexpr FusionKind FusionKind_MAX =
    OneDnnFusionConfig_FusionKind_FusionKind_MAX;
  static constexpr int FusionKind_ARRAYSIZE =
    OneDnnFusionConfig_FusionKind_FusionKind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  FusionKind_descriptor() {
    return OneDnnFusionConfig_FusionKind_descriptor();
  }
  template<typename T>
  static inline const std::string& FusionKind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, FusionKind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function FusionKind_Name.");
    return OneDnnFusionConfig_FusionKind_Name(enum_t_value);
  }
  static inline bool FusionKind_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      FusionKind* value) {
    return OneDnnFusionConfig_FusionKind_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOpsFieldNumber = 1,
    kAlphaTypecastFieldNumber = 2,
  };
  // repeated .xla.cpu.OneDnnFusionConfig.FusionKind ops = 1;
  int ops_size() const;
  private:
  int _internal_ops_size() const;
  public:
  void clear_ops();
  private:
  ::xla::cpu::OneDnnFusionConfig_FusionKind _internal_ops(int index) const;
  void _internal_add_ops(::xla::cpu::OneDnnFusionConfig_FusionKind value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_ops();
  public:
  ::xla::cpu::OneDnnFusionConfig_FusionKind ops(int index) const;
  void set_ops(int index, ::xla::cpu::OneDnnFusionConfig_FusionKind value);
  void add_ops(::xla::cpu::OneDnnFusionConfig_FusionKind value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& ops() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_ops();

  // int32 alpha_typecast = 2;
  void clear_alpha_typecast();
  int32_t alpha_typecast() const;
  void set_alpha_typecast(int32_t value);
  private:
  int32_t _internal_alpha_typecast() const;
  void _internal_set_alpha_typecast(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnFusionConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> ops_;
    mutable std::atomic<int> _ops_cached_byte_size_;
    int32_t alpha_typecast_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnTensorLayoutProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnTensorLayoutProto) */ {
 public:
  inline OneDnnTensorLayoutProto() : OneDnnTensorLayoutProto(nullptr) {}
  ~OneDnnTensorLayoutProto() override;
  explicit PROTOBUF_CONSTEXPR OneDnnTensorLayoutProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnTensorLayoutProto(const OneDnnTensorLayoutProto& from);
  OneDnnTensorLayoutProto(OneDnnTensorLayoutProto&& from) noexcept
    : OneDnnTensorLayoutProto() {
    *this = ::std::move(from);
  }

  inline OneDnnTensorLayoutProto& operator=(const OneDnnTensorLayoutProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnTensorLayoutProto& operator=(OneDnnTensorLayoutProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnTensorLayoutProto& default_instance() {
    return *internal_default_instance();
  }
  enum LayoutCase {
    kData = 2,
    kFilter = 3,
    kTensor = 4,
    LAYOUT_NOT_SET = 0,
  };

  static inline const OneDnnTensorLayoutProto* internal_default_instance() {
    return reinterpret_cast<const OneDnnTensorLayoutProto*>(
               &_OneDnnTensorLayoutProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(OneDnnTensorLayoutProto& a, OneDnnTensorLayoutProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnTensorLayoutProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnTensorLayoutProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnTensorLayoutProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnTensorLayoutProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnTensorLayoutProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnTensorLayoutProto& from) {
    OneDnnTensorLayoutProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnTensorLayoutProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnTensorLayoutProto";
  }
  protected:
  explicit OneDnnTensorLayoutProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimsFieldNumber = 1,
    kDataFieldNumber = 2,
    kFilterFieldNumber = 3,
    kTensorFieldNumber = 4,
  };
  // uint64 dims = 1;
  void clear_dims();
  uint64_t dims() const;
  void set_dims(uint64_t value);
  private:
  uint64_t _internal_dims() const;
  void _internal_set_dims(uint64_t value);
  public:

  // .xla.cpu.OneDnnDataLayoutProto data = 2;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const ::xla::cpu::OneDnnDataLayoutProto& data() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnDataLayoutProto* release_data();
  ::xla::cpu::OneDnnDataLayoutProto* mutable_data();
  void set_allocated_data(::xla::cpu::OneDnnDataLayoutProto* data);
  private:
  const ::xla::cpu::OneDnnDataLayoutProto& _internal_data() const;
  ::xla::cpu::OneDnnDataLayoutProto* _internal_mutable_data();
  public:
  void unsafe_arena_set_allocated_data(
      ::xla::cpu::OneDnnDataLayoutProto* data);
  ::xla::cpu::OneDnnDataLayoutProto* unsafe_arena_release_data();

  // .xla.cpu.OneDnnFilterLayoutProto filter = 3;
  bool has_filter() const;
  private:
  bool _internal_has_filter() const;
  public:
  void clear_filter();
  const ::xla::cpu::OneDnnFilterLayoutProto& filter() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnFilterLayoutProto* release_filter();
  ::xla::cpu::OneDnnFilterLayoutProto* mutable_filter();
  void set_allocated_filter(::xla::cpu::OneDnnFilterLayoutProto* filter);
  private:
  const ::xla::cpu::OneDnnFilterLayoutProto& _internal_filter() const;
  ::xla::cpu::OneDnnFilterLayoutProto* _internal_mutable_filter();
  public:
  void unsafe_arena_set_allocated_filter(
      ::xla::cpu::OneDnnFilterLayoutProto* filter);
  ::xla::cpu::OneDnnFilterLayoutProto* unsafe_arena_release_filter();

  // .xla.cpu.OneDnnFactorLayoutProto tensor = 4;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::xla::cpu::OneDnnFactorLayoutProto& tensor() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnFactorLayoutProto* release_tensor();
  ::xla::cpu::OneDnnFactorLayoutProto* mutable_tensor();
  void set_allocated_tensor(::xla::cpu::OneDnnFactorLayoutProto* tensor);
  private:
  const ::xla::cpu::OneDnnFactorLayoutProto& _internal_tensor() const;
  ::xla::cpu::OneDnnFactorLayoutProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::xla::cpu::OneDnnFactorLayoutProto* tensor);
  ::xla::cpu::OneDnnFactorLayoutProto* unsafe_arena_release_tensor();

  void clear_layout();
  LayoutCase layout_case() const;
  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnTensorLayoutProto)
 private:
  class _Internal;
  void set_has_data();
  void set_has_filter();
  void set_has_tensor();

  inline bool has_layout() const;
  inline void clear_has_layout();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t dims_;
    union LayoutUnion {
      constexpr LayoutUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::xla::cpu::OneDnnDataLayoutProto* data_;
      ::xla::cpu::OneDnnFilterLayoutProto* filter_;
      ::xla::cpu::OneDnnFactorLayoutProto* tensor_;
    } layout_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnSoftmaxConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnSoftmaxConfig) */ {
 public:
  inline OneDnnSoftmaxConfig() : OneDnnSoftmaxConfig(nullptr) {}
  ~OneDnnSoftmaxConfig() override;
  explicit PROTOBUF_CONSTEXPR OneDnnSoftmaxConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnSoftmaxConfig(const OneDnnSoftmaxConfig& from);
  OneDnnSoftmaxConfig(OneDnnSoftmaxConfig&& from) noexcept
    : OneDnnSoftmaxConfig() {
    *this = ::std::move(from);
  }

  inline OneDnnSoftmaxConfig& operator=(const OneDnnSoftmaxConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnSoftmaxConfig& operator=(OneDnnSoftmaxConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnSoftmaxConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnSoftmaxConfig* internal_default_instance() {
    return reinterpret_cast<const OneDnnSoftmaxConfig*>(
               &_OneDnnSoftmaxConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(OneDnnSoftmaxConfig& a, OneDnnSoftmaxConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnSoftmaxConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnSoftmaxConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnSoftmaxConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnSoftmaxConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnSoftmaxConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnSoftmaxConfig& from) {
    OneDnnSoftmaxConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnSoftmaxConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnSoftmaxConfig";
  }
  protected:
  explicit OneDnnSoftmaxConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSoftmaxAxisFieldNumber = 1,
  };
  // int32 softmax_axis = 1;
  void clear_softmax_axis();
  int32_t softmax_axis() const;
  void set_softmax_axis(int32_t value);
  private:
  int32_t _internal_softmax_axis() const;
  void _internal_set_softmax_axis(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnSoftmaxConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t softmax_axis_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnMatMulConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnMatMulConfig) */ {
 public:
  inline OneDnnMatMulConfig() : OneDnnMatMulConfig(nullptr) {}
  ~OneDnnMatMulConfig() override;
  explicit PROTOBUF_CONSTEXPR OneDnnMatMulConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnMatMulConfig(const OneDnnMatMulConfig& from);
  OneDnnMatMulConfig(OneDnnMatMulConfig&& from) noexcept
    : OneDnnMatMulConfig() {
    *this = ::std::move(from);
  }

  inline OneDnnMatMulConfig& operator=(const OneDnnMatMulConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnMatMulConfig& operator=(OneDnnMatMulConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnMatMulConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnMatMulConfig* internal_default_instance() {
    return reinterpret_cast<const OneDnnMatMulConfig*>(
               &_OneDnnMatMulConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(OneDnnMatMulConfig& a, OneDnnMatMulConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnMatMulConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnMatMulConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnMatMulConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnMatMulConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnMatMulConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnMatMulConfig& from) {
    OneDnnMatMulConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnMatMulConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnMatMulConfig";
  }
  protected:
  explicit OneDnnMatMulConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFusionsFieldNumber = 3,
    kOptimizationConfigFieldNumber = 8,
    kTransposeAFieldNumber = 1,
    kTransposeBFieldNumber = 2,
  };
  // .xla.cpu.OneDnnFusionConfig fusions = 3;
  bool has_fusions() const;
  private:
  bool _internal_has_fusions() const;
  public:
  void clear_fusions();
  const ::xla::cpu::OneDnnFusionConfig& fusions() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnFusionConfig* release_fusions();
  ::xla::cpu::OneDnnFusionConfig* mutable_fusions();
  void set_allocated_fusions(::xla::cpu::OneDnnFusionConfig* fusions);
  private:
  const ::xla::cpu::OneDnnFusionConfig& _internal_fusions() const;
  ::xla::cpu::OneDnnFusionConfig* _internal_mutable_fusions();
  public:
  void unsafe_arena_set_allocated_fusions(
      ::xla::cpu::OneDnnFusionConfig* fusions);
  ::xla::cpu::OneDnnFusionConfig* unsafe_arena_release_fusions();

  // .xla.cpu.OneDnnOptimizationConfig optimization_config = 8;
  bool has_optimization_config() const;
  private:
  bool _internal_has_optimization_config() const;
  public:
  void clear_optimization_config();
  const ::xla::cpu::OneDnnOptimizationConfig& optimization_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnOptimizationConfig* release_optimization_config();
  ::xla::cpu::OneDnnOptimizationConfig* mutable_optimization_config();
  void set_allocated_optimization_config(::xla::cpu::OneDnnOptimizationConfig* optimization_config);
  private:
  const ::xla::cpu::OneDnnOptimizationConfig& _internal_optimization_config() const;
  ::xla::cpu::OneDnnOptimizationConfig* _internal_mutable_optimization_config();
  public:
  void unsafe_arena_set_allocated_optimization_config(
      ::xla::cpu::OneDnnOptimizationConfig* optimization_config);
  ::xla::cpu::OneDnnOptimizationConfig* unsafe_arena_release_optimization_config();

  // bool transpose_a = 1;
  void clear_transpose_a();
  bool transpose_a() const;
  void set_transpose_a(bool value);
  private:
  bool _internal_transpose_a() const;
  void _internal_set_transpose_a(bool value);
  public:

  // bool transpose_b = 2;
  void clear_transpose_b();
  bool transpose_b() const;
  void set_transpose_b(bool value);
  private:
  bool _internal_transpose_b() const;
  void _internal_set_transpose_b(bool value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnMatMulConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::cpu::OneDnnFusionConfig* fusions_;
    ::xla::cpu::OneDnnOptimizationConfig* optimization_config_;
    bool transpose_a_;
    bool transpose_b_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnWindowProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnWindowProto) */ {
 public:
  inline OneDnnWindowProto() : OneDnnWindowProto(nullptr) {}
  ~OneDnnWindowProto() override;
  explicit PROTOBUF_CONSTEXPR OneDnnWindowProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnWindowProto(const OneDnnWindowProto& from);
  OneDnnWindowProto(OneDnnWindowProto&& from) noexcept
    : OneDnnWindowProto() {
    *this = ::std::move(from);
  }

  inline OneDnnWindowProto& operator=(const OneDnnWindowProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnWindowProto& operator=(OneDnnWindowProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnWindowProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnWindowProto* internal_default_instance() {
    return reinterpret_cast<const OneDnnWindowProto*>(
               &_OneDnnWindowProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(OneDnnWindowProto& a, OneDnnWindowProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnWindowProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnWindowProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnWindowProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnWindowProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnWindowProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnWindowProto& from) {
    OneDnnWindowProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnWindowProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnWindowProto";
  }
  protected:
  explicit OneDnnWindowProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSizeFieldNumber = 1,
    kPadLeftFieldNumber = 2,
    kPadRightFieldNumber = 3,
    kStridesFieldNumber = 4,
    kWindowDilationsFieldNumber = 5,
  };
  // repeated uint64 size = 1;
  int size_size() const;
  private:
  int _internal_size_size() const;
  public:
  void clear_size();
  private:
  uint64_t _internal_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_size() const;
  void _internal_add_size(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_size();
  public:
  uint64_t size(int index) const;
  void set_size(int index, uint64_t value);
  void add_size(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_size();

  // repeated uint64 pad_left = 2;
  int pad_left_size() const;
  private:
  int _internal_pad_left_size() const;
  public:
  void clear_pad_left();
  private:
  uint64_t _internal_pad_left(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_pad_left() const;
  void _internal_add_pad_left(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_pad_left();
  public:
  uint64_t pad_left(int index) const;
  void set_pad_left(int index, uint64_t value);
  void add_pad_left(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      pad_left() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_pad_left();

  // repeated uint64 pad_right = 3;
  int pad_right_size() const;
  private:
  int _internal_pad_right_size() const;
  public:
  void clear_pad_right();
  private:
  uint64_t _internal_pad_right(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_pad_right() const;
  void _internal_add_pad_right(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_pad_right();
  public:
  uint64_t pad_right(int index) const;
  void set_pad_right(int index, uint64_t value);
  void add_pad_right(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      pad_right() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_pad_right();

  // repeated uint64 strides = 4;
  int strides_size() const;
  private:
  int _internal_strides_size() const;
  public:
  void clear_strides();
  private:
  uint64_t _internal_strides(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_strides() const;
  void _internal_add_strides(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_strides();
  public:
  uint64_t strides(int index) const;
  void set_strides(int index, uint64_t value);
  void add_strides(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      strides() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_strides();

  // repeated uint64 window_dilations = 5;
  int window_dilations_size() const;
  private:
  int _internal_window_dilations_size() const;
  public:
  void clear_window_dilations();
  private:
  uint64_t _internal_window_dilations(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_window_dilations() const;
  void _internal_add_window_dilations(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_window_dilations();
  public:
  uint64_t window_dilations(int index) const;
  void set_window_dilations(int index, uint64_t value);
  void add_window_dilations(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      window_dilations() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_window_dilations();

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnWindowProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > size_;
    mutable std::atomic<int> _size_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > pad_left_;
    mutable std::atomic<int> _pad_left_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > pad_right_;
    mutable std::atomic<int> _pad_right_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > strides_;
    mutable std::atomic<int> _strides_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > window_dilations_;
    mutable std::atomic<int> _window_dilations_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnNormConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnNormConfig) */ {
 public:
  inline OneDnnNormConfig() : OneDnnNormConfig(nullptr) {}
  ~OneDnnNormConfig() override;
  explicit PROTOBUF_CONSTEXPR OneDnnNormConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnNormConfig(const OneDnnNormConfig& from);
  OneDnnNormConfig(OneDnnNormConfig&& from) noexcept
    : OneDnnNormConfig() {
    *this = ::std::move(from);
  }

  inline OneDnnNormConfig& operator=(const OneDnnNormConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnNormConfig& operator=(OneDnnNormConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnNormConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnNormConfig* internal_default_instance() {
    return reinterpret_cast<const OneDnnNormConfig*>(
               &_OneDnnNormConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(OneDnnNormConfig& a, OneDnnNormConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnNormConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnNormConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnNormConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnNormConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnNormConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnNormConfig& from) {
    OneDnnNormConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnNormConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnNormConfig";
  }
  protected:
  explicit OneDnnNormConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OneDnnNormConfig_ScaleAndShift ScaleAndShift;
  static constexpr ScaleAndShift UNDEFINED =
    OneDnnNormConfig_ScaleAndShift_UNDEFINED;
  static constexpr ScaleAndShift SCALE =
    OneDnnNormConfig_ScaleAndShift_SCALE;
  static constexpr ScaleAndShift SHIFT =
    OneDnnNormConfig_ScaleAndShift_SHIFT;
  static constexpr ScaleAndShift SCALE_AND_SHIFT =
    OneDnnNormConfig_ScaleAndShift_SCALE_AND_SHIFT;
  static inline bool ScaleAndShift_IsValid(int value) {
    return OneDnnNormConfig_ScaleAndShift_IsValid(value);
  }
  static constexpr ScaleAndShift ScaleAndShift_MIN =
    OneDnnNormConfig_ScaleAndShift_ScaleAndShift_MIN;
  static constexpr ScaleAndShift ScaleAndShift_MAX =
    OneDnnNormConfig_ScaleAndShift_ScaleAndShift_MAX;
  static constexpr int ScaleAndShift_ARRAYSIZE =
    OneDnnNormConfig_ScaleAndShift_ScaleAndShift_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ScaleAndShift_descriptor() {
    return OneDnnNormConfig_ScaleAndShift_descriptor();
  }
  template<typename T>
  static inline const std::string& ScaleAndShift_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ScaleAndShift>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ScaleAndShift_Name.");
    return OneDnnNormConfig_ScaleAndShift_Name(enum_t_value);
  }
  static inline bool ScaleAndShift_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ScaleAndShift* value) {
    return OneDnnNormConfig_ScaleAndShift_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFusionsFieldNumber = 3,
    kRescaleFieldNumber = 1,
    kEpsilonTypecastFieldNumber = 2,
  };
  // .xla.cpu.OneDnnFusionConfig fusions = 3;
  bool has_fusions() const;
  private:
  bool _internal_has_fusions() const;
  public:
  void clear_fusions();
  const ::xla::cpu::OneDnnFusionConfig& fusions() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnFusionConfig* release_fusions();
  ::xla::cpu::OneDnnFusionConfig* mutable_fusions();
  void set_allocated_fusions(::xla::cpu::OneDnnFusionConfig* fusions);
  private:
  const ::xla::cpu::OneDnnFusionConfig& _internal_fusions() const;
  ::xla::cpu::OneDnnFusionConfig* _internal_mutable_fusions();
  public:
  void unsafe_arena_set_allocated_fusions(
      ::xla::cpu::OneDnnFusionConfig* fusions);
  ::xla::cpu::OneDnnFusionConfig* unsafe_arena_release_fusions();

  // .xla.cpu.OneDnnNormConfig.ScaleAndShift rescale = 1;
  void clear_rescale();
  ::xla::cpu::OneDnnNormConfig_ScaleAndShift rescale() const;
  void set_rescale(::xla::cpu::OneDnnNormConfig_ScaleAndShift value);
  private:
  ::xla::cpu::OneDnnNormConfig_ScaleAndShift _internal_rescale() const;
  void _internal_set_rescale(::xla::cpu::OneDnnNormConfig_ScaleAndShift value);
  public:

  // int32 epsilon_typecast = 2;
  void clear_epsilon_typecast();
  int32_t epsilon_typecast() const;
  void set_epsilon_typecast(int32_t value);
  private:
  int32_t _internal_epsilon_typecast() const;
  void _internal_set_epsilon_typecast(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnNormConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::cpu::OneDnnFusionConfig* fusions_;
    int rescale_;
    int32_t epsilon_typecast_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class OneDnnConvolutionConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.OneDnnConvolutionConfig) */ {
 public:
  inline OneDnnConvolutionConfig() : OneDnnConvolutionConfig(nullptr) {}
  ~OneDnnConvolutionConfig() override;
  explicit PROTOBUF_CONSTEXPR OneDnnConvolutionConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OneDnnConvolutionConfig(const OneDnnConvolutionConfig& from);
  OneDnnConvolutionConfig(OneDnnConvolutionConfig&& from) noexcept
    : OneDnnConvolutionConfig() {
    *this = ::std::move(from);
  }

  inline OneDnnConvolutionConfig& operator=(const OneDnnConvolutionConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline OneDnnConvolutionConfig& operator=(OneDnnConvolutionConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OneDnnConvolutionConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const OneDnnConvolutionConfig* internal_default_instance() {
    return reinterpret_cast<const OneDnnConvolutionConfig*>(
               &_OneDnnConvolutionConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(OneDnnConvolutionConfig& a, OneDnnConvolutionConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(OneDnnConvolutionConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OneDnnConvolutionConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OneDnnConvolutionConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OneDnnConvolutionConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OneDnnConvolutionConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OneDnnConvolutionConfig& from) {
    OneDnnConvolutionConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OneDnnConvolutionConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.OneDnnConvolutionConfig";
  }
  protected:
  explicit OneDnnConvolutionConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputFieldNumber = 2,
    kKernelFieldNumber = 3,
    kOutputFieldNumber = 4,
    kWindowFieldNumber = 5,
    kFusionsFieldNumber = 6,
    kOptimizationConfigFieldNumber = 8,
    kDimsFieldNumber = 1,
    kFeatureGroupsFieldNumber = 7,
  };
  // .xla.cpu.OneDnnTensorLayoutProto input = 2;
  bool has_input() const;
  private:
  bool _internal_has_input() const;
  public:
  void clear_input();
  const ::xla::cpu::OneDnnTensorLayoutProto& input() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnTensorLayoutProto* release_input();
  ::xla::cpu::OneDnnTensorLayoutProto* mutable_input();
  void set_allocated_input(::xla::cpu::OneDnnTensorLayoutProto* input);
  private:
  const ::xla::cpu::OneDnnTensorLayoutProto& _internal_input() const;
  ::xla::cpu::OneDnnTensorLayoutProto* _internal_mutable_input();
  public:
  void unsafe_arena_set_allocated_input(
      ::xla::cpu::OneDnnTensorLayoutProto* input);
  ::xla::cpu::OneDnnTensorLayoutProto* unsafe_arena_release_input();

  // .xla.cpu.OneDnnTensorLayoutProto kernel = 3;
  bool has_kernel() const;
  private:
  bool _internal_has_kernel() const;
  public:
  void clear_kernel();
  const ::xla::cpu::OneDnnTensorLayoutProto& kernel() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnTensorLayoutProto* release_kernel();
  ::xla::cpu::OneDnnTensorLayoutProto* mutable_kernel();
  void set_allocated_kernel(::xla::cpu::OneDnnTensorLayoutProto* kernel);
  private:
  const ::xla::cpu::OneDnnTensorLayoutProto& _internal_kernel() const;
  ::xla::cpu::OneDnnTensorLayoutProto* _internal_mutable_kernel();
  public:
  void unsafe_arena_set_allocated_kernel(
      ::xla::cpu::OneDnnTensorLayoutProto* kernel);
  ::xla::cpu::OneDnnTensorLayoutProto* unsafe_arena_release_kernel();

  // .xla.cpu.OneDnnTensorLayoutProto output = 4;
  bool has_output() const;
  private:
  bool _internal_has_output() const;
  public:
  void clear_output();
  const ::xla::cpu::OneDnnTensorLayoutProto& output() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnTensorLayoutProto* release_output();
  ::xla::cpu::OneDnnTensorLayoutProto* mutable_output();
  void set_allocated_output(::xla::cpu::OneDnnTensorLayoutProto* output);
  private:
  const ::xla::cpu::OneDnnTensorLayoutProto& _internal_output() const;
  ::xla::cpu::OneDnnTensorLayoutProto* _internal_mutable_output();
  public:
  void unsafe_arena_set_allocated_output(
      ::xla::cpu::OneDnnTensorLayoutProto* output);
  ::xla::cpu::OneDnnTensorLayoutProto* unsafe_arena_release_output();

  // .xla.cpu.OneDnnWindowProto window = 5;
  bool has_window() const;
  private:
  bool _internal_has_window() const;
  public:
  void clear_window();
  const ::xla::cpu::OneDnnWindowProto& window() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnWindowProto* release_window();
  ::xla::cpu::OneDnnWindowProto* mutable_window();
  void set_allocated_window(::xla::cpu::OneDnnWindowProto* window);
  private:
  const ::xla::cpu::OneDnnWindowProto& _internal_window() const;
  ::xla::cpu::OneDnnWindowProto* _internal_mutable_window();
  public:
  void unsafe_arena_set_allocated_window(
      ::xla::cpu::OneDnnWindowProto* window);
  ::xla::cpu::OneDnnWindowProto* unsafe_arena_release_window();

  // .xla.cpu.OneDnnFusionConfig fusions = 6;
  bool has_fusions() const;
  private:
  bool _internal_has_fusions() const;
  public:
  void clear_fusions();
  const ::xla::cpu::OneDnnFusionConfig& fusions() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnFusionConfig* release_fusions();
  ::xla::cpu::OneDnnFusionConfig* mutable_fusions();
  void set_allocated_fusions(::xla::cpu::OneDnnFusionConfig* fusions);
  private:
  const ::xla::cpu::OneDnnFusionConfig& _internal_fusions() const;
  ::xla::cpu::OneDnnFusionConfig* _internal_mutable_fusions();
  public:
  void unsafe_arena_set_allocated_fusions(
      ::xla::cpu::OneDnnFusionConfig* fusions);
  ::xla::cpu::OneDnnFusionConfig* unsafe_arena_release_fusions();

  // .xla.cpu.OneDnnOptimizationConfig optimization_config = 8;
  bool has_optimization_config() const;
  private:
  bool _internal_has_optimization_config() const;
  public:
  void clear_optimization_config();
  const ::xla::cpu::OneDnnOptimizationConfig& optimization_config() const;
  PROTOBUF_NODISCARD ::xla::cpu::OneDnnOptimizationConfig* release_optimization_config();
  ::xla::cpu::OneDnnOptimizationConfig* mutable_optimization_config();
  void set_allocated_optimization_config(::xla::cpu::OneDnnOptimizationConfig* optimization_config);
  private:
  const ::xla::cpu::OneDnnOptimizationConfig& _internal_optimization_config() const;
  ::xla::cpu::OneDnnOptimizationConfig* _internal_mutable_optimization_config();
  public:
  void unsafe_arena_set_allocated_optimization_config(
      ::xla::cpu::OneDnnOptimizationConfig* optimization_config);
  ::xla::cpu::OneDnnOptimizationConfig* unsafe_arena_release_optimization_config();

  // uint64 dims = 1;
  void clear_dims();
  uint64_t dims() const;
  void set_dims(uint64_t value);
  private:
  uint64_t _internal_dims() const;
  void _internal_set_dims(uint64_t value);
  public:

  // uint64 feature_groups = 7;
  void clear_feature_groups();
  uint64_t feature_groups() const;
  void set_feature_groups(uint64_t value);
  private:
  uint64_t _internal_feature_groups() const;
  void _internal_set_feature_groups(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.OneDnnConvolutionConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::cpu::OneDnnTensorLayoutProto* input_;
    ::xla::cpu::OneDnnTensorLayoutProto* kernel_;
    ::xla::cpu::OneDnnTensorLayoutProto* output_;
    ::xla::cpu::OneDnnWindowProto* window_;
    ::xla::cpu::OneDnnFusionConfig* fusions_;
    ::xla::cpu::OneDnnOptimizationConfig* optimization_config_;
    uint64_t dims_;
    uint64_t feature_groups_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OneDnnDataLayoutProto

// uint64 batch_dim = 1;
inline void OneDnnDataLayoutProto::clear_batch_dim() {
  _impl_.batch_dim_ = uint64_t{0u};
}
inline uint64_t OneDnnDataLayoutProto::_internal_batch_dim() const {
  return _impl_.batch_dim_;
}
inline uint64_t OneDnnDataLayoutProto::batch_dim() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnDataLayoutProto.batch_dim)
  return _internal_batch_dim();
}
inline void OneDnnDataLayoutProto::_internal_set_batch_dim(uint64_t value) {
  
  _impl_.batch_dim_ = value;
}
inline void OneDnnDataLayoutProto::set_batch_dim(uint64_t value) {
  _internal_set_batch_dim(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnDataLayoutProto.batch_dim)
}

// uint64 feature_dim = 2;
inline void OneDnnDataLayoutProto::clear_feature_dim() {
  _impl_.feature_dim_ = uint64_t{0u};
}
inline uint64_t OneDnnDataLayoutProto::_internal_feature_dim() const {
  return _impl_.feature_dim_;
}
inline uint64_t OneDnnDataLayoutProto::feature_dim() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnDataLayoutProto.feature_dim)
  return _internal_feature_dim();
}
inline void OneDnnDataLayoutProto::_internal_set_feature_dim(uint64_t value) {
  
  _impl_.feature_dim_ = value;
}
inline void OneDnnDataLayoutProto::set_feature_dim(uint64_t value) {
  _internal_set_feature_dim(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnDataLayoutProto.feature_dim)
}

// repeated uint64 spatial_dims = 3;
inline int OneDnnDataLayoutProto::_internal_spatial_dims_size() const {
  return _impl_.spatial_dims_.size();
}
inline int OneDnnDataLayoutProto::spatial_dims_size() const {
  return _internal_spatial_dims_size();
}
inline void OneDnnDataLayoutProto::clear_spatial_dims() {
  _impl_.spatial_dims_.Clear();
}
inline uint64_t OneDnnDataLayoutProto::_internal_spatial_dims(int index) const {
  return _impl_.spatial_dims_.Get(index);
}
inline uint64_t OneDnnDataLayoutProto::spatial_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnDataLayoutProto.spatial_dims)
  return _internal_spatial_dims(index);
}
inline void OneDnnDataLayoutProto::set_spatial_dims(int index, uint64_t value) {
  _impl_.spatial_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnDataLayoutProto.spatial_dims)
}
inline void OneDnnDataLayoutProto::_internal_add_spatial_dims(uint64_t value) {
  _impl_.spatial_dims_.Add(value);
}
inline void OneDnnDataLayoutProto::add_spatial_dims(uint64_t value) {
  _internal_add_spatial_dims(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnDataLayoutProto.spatial_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnDataLayoutProto::_internal_spatial_dims() const {
  return _impl_.spatial_dims_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnDataLayoutProto::spatial_dims() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnDataLayoutProto.spatial_dims)
  return _internal_spatial_dims();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnDataLayoutProto::_internal_mutable_spatial_dims() {
  return &_impl_.spatial_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnDataLayoutProto::mutable_spatial_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnDataLayoutProto.spatial_dims)
  return _internal_mutable_spatial_dims();
}

// -------------------------------------------------------------------

// OneDnnFilterLayoutProto

// uint64 input_feature_dim = 1;
inline void OneDnnFilterLayoutProto::clear_input_feature_dim() {
  _impl_.input_feature_dim_ = uint64_t{0u};
}
inline uint64_t OneDnnFilterLayoutProto::_internal_input_feature_dim() const {
  return _impl_.input_feature_dim_;
}
inline uint64_t OneDnnFilterLayoutProto::input_feature_dim() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFilterLayoutProto.input_feature_dim)
  return _internal_input_feature_dim();
}
inline void OneDnnFilterLayoutProto::_internal_set_input_feature_dim(uint64_t value) {
  
  _impl_.input_feature_dim_ = value;
}
inline void OneDnnFilterLayoutProto::set_input_feature_dim(uint64_t value) {
  _internal_set_input_feature_dim(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFilterLayoutProto.input_feature_dim)
}

// uint64 output_feature_dim = 2;
inline void OneDnnFilterLayoutProto::clear_output_feature_dim() {
  _impl_.output_feature_dim_ = uint64_t{0u};
}
inline uint64_t OneDnnFilterLayoutProto::_internal_output_feature_dim() const {
  return _impl_.output_feature_dim_;
}
inline uint64_t OneDnnFilterLayoutProto::output_feature_dim() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFilterLayoutProto.output_feature_dim)
  return _internal_output_feature_dim();
}
inline void OneDnnFilterLayoutProto::_internal_set_output_feature_dim(uint64_t value) {
  
  _impl_.output_feature_dim_ = value;
}
inline void OneDnnFilterLayoutProto::set_output_feature_dim(uint64_t value) {
  _internal_set_output_feature_dim(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFilterLayoutProto.output_feature_dim)
}

// repeated uint64 spatial_dims = 3;
inline int OneDnnFilterLayoutProto::_internal_spatial_dims_size() const {
  return _impl_.spatial_dims_.size();
}
inline int OneDnnFilterLayoutProto::spatial_dims_size() const {
  return _internal_spatial_dims_size();
}
inline void OneDnnFilterLayoutProto::clear_spatial_dims() {
  _impl_.spatial_dims_.Clear();
}
inline uint64_t OneDnnFilterLayoutProto::_internal_spatial_dims(int index) const {
  return _impl_.spatial_dims_.Get(index);
}
inline uint64_t OneDnnFilterLayoutProto::spatial_dims(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFilterLayoutProto.spatial_dims)
  return _internal_spatial_dims(index);
}
inline void OneDnnFilterLayoutProto::set_spatial_dims(int index, uint64_t value) {
  _impl_.spatial_dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFilterLayoutProto.spatial_dims)
}
inline void OneDnnFilterLayoutProto::_internal_add_spatial_dims(uint64_t value) {
  _impl_.spatial_dims_.Add(value);
}
inline void OneDnnFilterLayoutProto::add_spatial_dims(uint64_t value) {
  _internal_add_spatial_dims(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnFilterLayoutProto.spatial_dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFilterLayoutProto::_internal_spatial_dims() const {
  return _impl_.spatial_dims_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFilterLayoutProto::spatial_dims() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnFilterLayoutProto.spatial_dims)
  return _internal_spatial_dims();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFilterLayoutProto::_internal_mutable_spatial_dims() {
  return &_impl_.spatial_dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFilterLayoutProto::mutable_spatial_dims() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnFilterLayoutProto.spatial_dims)
  return _internal_mutable_spatial_dims();
}

// repeated uint64 shape = 4;
inline int OneDnnFilterLayoutProto::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int OneDnnFilterLayoutProto::shape_size() const {
  return _internal_shape_size();
}
inline void OneDnnFilterLayoutProto::clear_shape() {
  _impl_.shape_.Clear();
}
inline uint64_t OneDnnFilterLayoutProto::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline uint64_t OneDnnFilterLayoutProto::shape(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFilterLayoutProto.shape)
  return _internal_shape(index);
}
inline void OneDnnFilterLayoutProto::set_shape(int index, uint64_t value) {
  _impl_.shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFilterLayoutProto.shape)
}
inline void OneDnnFilterLayoutProto::_internal_add_shape(uint64_t value) {
  _impl_.shape_.Add(value);
}
inline void OneDnnFilterLayoutProto::add_shape(uint64_t value) {
  _internal_add_shape(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnFilterLayoutProto.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFilterLayoutProto::_internal_shape() const {
  return _impl_.shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFilterLayoutProto::shape() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnFilterLayoutProto.shape)
  return _internal_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFilterLayoutProto::_internal_mutable_shape() {
  return &_impl_.shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFilterLayoutProto::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnFilterLayoutProto.shape)
  return _internal_mutable_shape();
}

// -------------------------------------------------------------------

// OneDnnFactorLayoutProto

// repeated uint64 dimensions = 1;
inline int OneDnnFactorLayoutProto::_internal_dimensions_size() const {
  return _impl_.dimensions_.size();
}
inline int OneDnnFactorLayoutProto::dimensions_size() const {
  return _internal_dimensions_size();
}
inline void OneDnnFactorLayoutProto::clear_dimensions() {
  _impl_.dimensions_.Clear();
}
inline uint64_t OneDnnFactorLayoutProto::_internal_dimensions(int index) const {
  return _impl_.dimensions_.Get(index);
}
inline uint64_t OneDnnFactorLayoutProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFactorLayoutProto.dimensions)
  return _internal_dimensions(index);
}
inline void OneDnnFactorLayoutProto::set_dimensions(int index, uint64_t value) {
  _impl_.dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFactorLayoutProto.dimensions)
}
inline void OneDnnFactorLayoutProto::_internal_add_dimensions(uint64_t value) {
  _impl_.dimensions_.Add(value);
}
inline void OneDnnFactorLayoutProto::add_dimensions(uint64_t value) {
  _internal_add_dimensions(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnFactorLayoutProto.dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFactorLayoutProto::_internal_dimensions() const {
  return _impl_.dimensions_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFactorLayoutProto::dimensions() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnFactorLayoutProto.dimensions)
  return _internal_dimensions();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFactorLayoutProto::_internal_mutable_dimensions() {
  return &_impl_.dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFactorLayoutProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnFactorLayoutProto.dimensions)
  return _internal_mutable_dimensions();
}

// repeated uint64 shape = 2;
inline int OneDnnFactorLayoutProto::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int OneDnnFactorLayoutProto::shape_size() const {
  return _internal_shape_size();
}
inline void OneDnnFactorLayoutProto::clear_shape() {
  _impl_.shape_.Clear();
}
inline uint64_t OneDnnFactorLayoutProto::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline uint64_t OneDnnFactorLayoutProto::shape(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFactorLayoutProto.shape)
  return _internal_shape(index);
}
inline void OneDnnFactorLayoutProto::set_shape(int index, uint64_t value) {
  _impl_.shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFactorLayoutProto.shape)
}
inline void OneDnnFactorLayoutProto::_internal_add_shape(uint64_t value) {
  _impl_.shape_.Add(value);
}
inline void OneDnnFactorLayoutProto::add_shape(uint64_t value) {
  _internal_add_shape(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnFactorLayoutProto.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFactorLayoutProto::_internal_shape() const {
  return _impl_.shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnFactorLayoutProto::shape() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnFactorLayoutProto.shape)
  return _internal_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFactorLayoutProto::_internal_mutable_shape() {
  return &_impl_.shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnFactorLayoutProto::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnFactorLayoutProto.shape)
  return _internal_mutable_shape();
}

// -------------------------------------------------------------------

// OneDnnOptimizationConfig

// bool weights_prepacked = 1;
inline void OneDnnOptimizationConfig::clear_weights_prepacked() {
  _impl_.weights_prepacked_ = false;
}
inline bool OneDnnOptimizationConfig::_internal_weights_prepacked() const {
  return _impl_.weights_prepacked_;
}
inline bool OneDnnOptimizationConfig::weights_prepacked() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnOptimizationConfig.weights_prepacked)
  return _internal_weights_prepacked();
}
inline void OneDnnOptimizationConfig::_internal_set_weights_prepacked(bool value) {
  
  _impl_.weights_prepacked_ = value;
}
inline void OneDnnOptimizationConfig::set_weights_prepacked(bool value) {
  _internal_set_weights_prepacked(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnOptimizationConfig.weights_prepacked)
}

// bool user_scratchpad = 2;
inline void OneDnnOptimizationConfig::clear_user_scratchpad() {
  _impl_.user_scratchpad_ = false;
}
inline bool OneDnnOptimizationConfig::_internal_user_scratchpad() const {
  return _impl_.user_scratchpad_;
}
inline bool OneDnnOptimizationConfig::user_scratchpad() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnOptimizationConfig.user_scratchpad)
  return _internal_user_scratchpad();
}
inline void OneDnnOptimizationConfig::_internal_set_user_scratchpad(bool value) {
  
  _impl_.user_scratchpad_ = value;
}
inline void OneDnnOptimizationConfig::set_user_scratchpad(bool value) {
  _internal_set_user_scratchpad(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnOptimizationConfig.user_scratchpad)
}

// bool bias_broadcast = 3;
inline void OneDnnOptimizationConfig::clear_bias_broadcast() {
  _impl_.bias_broadcast_ = false;
}
inline bool OneDnnOptimizationConfig::_internal_bias_broadcast() const {
  return _impl_.bias_broadcast_;
}
inline bool OneDnnOptimizationConfig::bias_broadcast() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnOptimizationConfig.bias_broadcast)
  return _internal_bias_broadcast();
}
inline void OneDnnOptimizationConfig::_internal_set_bias_broadcast(bool value) {
  
  _impl_.bias_broadcast_ = value;
}
inline void OneDnnOptimizationConfig::set_bias_broadcast(bool value) {
  _internal_set_bias_broadcast(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnOptimizationConfig.bias_broadcast)
}

// -------------------------------------------------------------------

// OneDnnFusionConfig

// repeated .xla.cpu.OneDnnFusionConfig.FusionKind ops = 1;
inline int OneDnnFusionConfig::_internal_ops_size() const {
  return _impl_.ops_.size();
}
inline int OneDnnFusionConfig::ops_size() const {
  return _internal_ops_size();
}
inline void OneDnnFusionConfig::clear_ops() {
  _impl_.ops_.Clear();
}
inline ::xla::cpu::OneDnnFusionConfig_FusionKind OneDnnFusionConfig::_internal_ops(int index) const {
  return static_cast< ::xla::cpu::OneDnnFusionConfig_FusionKind >(_impl_.ops_.Get(index));
}
inline ::xla::cpu::OneDnnFusionConfig_FusionKind OneDnnFusionConfig::ops(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFusionConfig.ops)
  return _internal_ops(index);
}
inline void OneDnnFusionConfig::set_ops(int index, ::xla::cpu::OneDnnFusionConfig_FusionKind value) {
  _impl_.ops_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFusionConfig.ops)
}
inline void OneDnnFusionConfig::_internal_add_ops(::xla::cpu::OneDnnFusionConfig_FusionKind value) {
  _impl_.ops_.Add(value);
}
inline void OneDnnFusionConfig::add_ops(::xla::cpu::OneDnnFusionConfig_FusionKind value) {
  _internal_add_ops(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnFusionConfig.ops)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
OneDnnFusionConfig::ops() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnFusionConfig.ops)
  return _impl_.ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
OneDnnFusionConfig::_internal_mutable_ops() {
  return &_impl_.ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
OneDnnFusionConfig::mutable_ops() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnFusionConfig.ops)
  return _internal_mutable_ops();
}

// int32 alpha_typecast = 2;
inline void OneDnnFusionConfig::clear_alpha_typecast() {
  _impl_.alpha_typecast_ = 0;
}
inline int32_t OneDnnFusionConfig::_internal_alpha_typecast() const {
  return _impl_.alpha_typecast_;
}
inline int32_t OneDnnFusionConfig::alpha_typecast() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnFusionConfig.alpha_typecast)
  return _internal_alpha_typecast();
}
inline void OneDnnFusionConfig::_internal_set_alpha_typecast(int32_t value) {
  
  _impl_.alpha_typecast_ = value;
}
inline void OneDnnFusionConfig::set_alpha_typecast(int32_t value) {
  _internal_set_alpha_typecast(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnFusionConfig.alpha_typecast)
}

// -------------------------------------------------------------------

// OneDnnTensorLayoutProto

// uint64 dims = 1;
inline void OneDnnTensorLayoutProto::clear_dims() {
  _impl_.dims_ = uint64_t{0u};
}
inline uint64_t OneDnnTensorLayoutProto::_internal_dims() const {
  return _impl_.dims_;
}
inline uint64_t OneDnnTensorLayoutProto::dims() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnTensorLayoutProto.dims)
  return _internal_dims();
}
inline void OneDnnTensorLayoutProto::_internal_set_dims(uint64_t value) {
  
  _impl_.dims_ = value;
}
inline void OneDnnTensorLayoutProto::set_dims(uint64_t value) {
  _internal_set_dims(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnTensorLayoutProto.dims)
}

// .xla.cpu.OneDnnDataLayoutProto data = 2;
inline bool OneDnnTensorLayoutProto::_internal_has_data() const {
  return layout_case() == kData;
}
inline bool OneDnnTensorLayoutProto::has_data() const {
  return _internal_has_data();
}
inline void OneDnnTensorLayoutProto::set_has_data() {
  _impl_._oneof_case_[0] = kData;
}
inline void OneDnnTensorLayoutProto::clear_data() {
  if (_internal_has_data()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.layout_.data_;
    }
    clear_has_layout();
  }
}
inline ::xla::cpu::OneDnnDataLayoutProto* OneDnnTensorLayoutProto::release_data() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnTensorLayoutProto.data)
  if (_internal_has_data()) {
    clear_has_layout();
    ::xla::cpu::OneDnnDataLayoutProto* temp = _impl_.layout_.data_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.layout_.data_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnDataLayoutProto& OneDnnTensorLayoutProto::_internal_data() const {
  return _internal_has_data()
      ? *_impl_.layout_.data_
      : reinterpret_cast< ::xla::cpu::OneDnnDataLayoutProto&>(::xla::cpu::_OneDnnDataLayoutProto_default_instance_);
}
inline const ::xla::cpu::OneDnnDataLayoutProto& OneDnnTensorLayoutProto::data() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnTensorLayoutProto.data)
  return _internal_data();
}
inline ::xla::cpu::OneDnnDataLayoutProto* OneDnnTensorLayoutProto::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.OneDnnTensorLayoutProto.data)
  if (_internal_has_data()) {
    clear_has_layout();
    ::xla::cpu::OneDnnDataLayoutProto* temp = _impl_.layout_.data_;
    _impl_.layout_.data_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OneDnnTensorLayoutProto::unsafe_arena_set_allocated_data(::xla::cpu::OneDnnDataLayoutProto* data) {
  clear_layout();
  if (data) {
    set_has_data();
    _impl_.layout_.data_ = data;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnTensorLayoutProto.data)
}
inline ::xla::cpu::OneDnnDataLayoutProto* OneDnnTensorLayoutProto::_internal_mutable_data() {
  if (!_internal_has_data()) {
    clear_layout();
    set_has_data();
    _impl_.layout_.data_ = CreateMaybeMessage< ::xla::cpu::OneDnnDataLayoutProto >(GetArenaForAllocation());
  }
  return _impl_.layout_.data_;
}
inline ::xla::cpu::OneDnnDataLayoutProto* OneDnnTensorLayoutProto::mutable_data() {
  ::xla::cpu::OneDnnDataLayoutProto* _msg = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnTensorLayoutProto.data)
  return _msg;
}

// .xla.cpu.OneDnnFilterLayoutProto filter = 3;
inline bool OneDnnTensorLayoutProto::_internal_has_filter() const {
  return layout_case() == kFilter;
}
inline bool OneDnnTensorLayoutProto::has_filter() const {
  return _internal_has_filter();
}
inline void OneDnnTensorLayoutProto::set_has_filter() {
  _impl_._oneof_case_[0] = kFilter;
}
inline void OneDnnTensorLayoutProto::clear_filter() {
  if (_internal_has_filter()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.layout_.filter_;
    }
    clear_has_layout();
  }
}
inline ::xla::cpu::OneDnnFilterLayoutProto* OneDnnTensorLayoutProto::release_filter() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnTensorLayoutProto.filter)
  if (_internal_has_filter()) {
    clear_has_layout();
    ::xla::cpu::OneDnnFilterLayoutProto* temp = _impl_.layout_.filter_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.layout_.filter_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnFilterLayoutProto& OneDnnTensorLayoutProto::_internal_filter() const {
  return _internal_has_filter()
      ? *_impl_.layout_.filter_
      : reinterpret_cast< ::xla::cpu::OneDnnFilterLayoutProto&>(::xla::cpu::_OneDnnFilterLayoutProto_default_instance_);
}
inline const ::xla::cpu::OneDnnFilterLayoutProto& OneDnnTensorLayoutProto::filter() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnTensorLayoutProto.filter)
  return _internal_filter();
}
inline ::xla::cpu::OneDnnFilterLayoutProto* OneDnnTensorLayoutProto::unsafe_arena_release_filter() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.OneDnnTensorLayoutProto.filter)
  if (_internal_has_filter()) {
    clear_has_layout();
    ::xla::cpu::OneDnnFilterLayoutProto* temp = _impl_.layout_.filter_;
    _impl_.layout_.filter_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OneDnnTensorLayoutProto::unsafe_arena_set_allocated_filter(::xla::cpu::OneDnnFilterLayoutProto* filter) {
  clear_layout();
  if (filter) {
    set_has_filter();
    _impl_.layout_.filter_ = filter;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnTensorLayoutProto.filter)
}
inline ::xla::cpu::OneDnnFilterLayoutProto* OneDnnTensorLayoutProto::_internal_mutable_filter() {
  if (!_internal_has_filter()) {
    clear_layout();
    set_has_filter();
    _impl_.layout_.filter_ = CreateMaybeMessage< ::xla::cpu::OneDnnFilterLayoutProto >(GetArenaForAllocation());
  }
  return _impl_.layout_.filter_;
}
inline ::xla::cpu::OneDnnFilterLayoutProto* OneDnnTensorLayoutProto::mutable_filter() {
  ::xla::cpu::OneDnnFilterLayoutProto* _msg = _internal_mutable_filter();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnTensorLayoutProto.filter)
  return _msg;
}

// .xla.cpu.OneDnnFactorLayoutProto tensor = 4;
inline bool OneDnnTensorLayoutProto::_internal_has_tensor() const {
  return layout_case() == kTensor;
}
inline bool OneDnnTensorLayoutProto::has_tensor() const {
  return _internal_has_tensor();
}
inline void OneDnnTensorLayoutProto::set_has_tensor() {
  _impl_._oneof_case_[0] = kTensor;
}
inline void OneDnnTensorLayoutProto::clear_tensor() {
  if (_internal_has_tensor()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.layout_.tensor_;
    }
    clear_has_layout();
  }
}
inline ::xla::cpu::OneDnnFactorLayoutProto* OneDnnTensorLayoutProto::release_tensor() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnTensorLayoutProto.tensor)
  if (_internal_has_tensor()) {
    clear_has_layout();
    ::xla::cpu::OneDnnFactorLayoutProto* temp = _impl_.layout_.tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.layout_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::cpu::OneDnnFactorLayoutProto& OneDnnTensorLayoutProto::_internal_tensor() const {
  return _internal_has_tensor()
      ? *_impl_.layout_.tensor_
      : reinterpret_cast< ::xla::cpu::OneDnnFactorLayoutProto&>(::xla::cpu::_OneDnnFactorLayoutProto_default_instance_);
}
inline const ::xla::cpu::OneDnnFactorLayoutProto& OneDnnTensorLayoutProto::tensor() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnTensorLayoutProto.tensor)
  return _internal_tensor();
}
inline ::xla::cpu::OneDnnFactorLayoutProto* OneDnnTensorLayoutProto::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.cpu.OneDnnTensorLayoutProto.tensor)
  if (_internal_has_tensor()) {
    clear_has_layout();
    ::xla::cpu::OneDnnFactorLayoutProto* temp = _impl_.layout_.tensor_;
    _impl_.layout_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OneDnnTensorLayoutProto::unsafe_arena_set_allocated_tensor(::xla::cpu::OneDnnFactorLayoutProto* tensor) {
  clear_layout();
  if (tensor) {
    set_has_tensor();
    _impl_.layout_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnTensorLayoutProto.tensor)
}
inline ::xla::cpu::OneDnnFactorLayoutProto* OneDnnTensorLayoutProto::_internal_mutable_tensor() {
  if (!_internal_has_tensor()) {
    clear_layout();
    set_has_tensor();
    _impl_.layout_.tensor_ = CreateMaybeMessage< ::xla::cpu::OneDnnFactorLayoutProto >(GetArenaForAllocation());
  }
  return _impl_.layout_.tensor_;
}
inline ::xla::cpu::OneDnnFactorLayoutProto* OneDnnTensorLayoutProto::mutable_tensor() {
  ::xla::cpu::OneDnnFactorLayoutProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnTensorLayoutProto.tensor)
  return _msg;
}

inline bool OneDnnTensorLayoutProto::has_layout() const {
  return layout_case() != LAYOUT_NOT_SET;
}
inline void OneDnnTensorLayoutProto::clear_has_layout() {
  _impl_._oneof_case_[0] = LAYOUT_NOT_SET;
}
inline OneDnnTensorLayoutProto::LayoutCase OneDnnTensorLayoutProto::layout_case() const {
  return OneDnnTensorLayoutProto::LayoutCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// OneDnnSoftmaxConfig

// int32 softmax_axis = 1;
inline void OneDnnSoftmaxConfig::clear_softmax_axis() {
  _impl_.softmax_axis_ = 0;
}
inline int32_t OneDnnSoftmaxConfig::_internal_softmax_axis() const {
  return _impl_.softmax_axis_;
}
inline int32_t OneDnnSoftmaxConfig::softmax_axis() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnSoftmaxConfig.softmax_axis)
  return _internal_softmax_axis();
}
inline void OneDnnSoftmaxConfig::_internal_set_softmax_axis(int32_t value) {
  
  _impl_.softmax_axis_ = value;
}
inline void OneDnnSoftmaxConfig::set_softmax_axis(int32_t value) {
  _internal_set_softmax_axis(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnSoftmaxConfig.softmax_axis)
}

// -------------------------------------------------------------------

// OneDnnMatMulConfig

// bool transpose_a = 1;
inline void OneDnnMatMulConfig::clear_transpose_a() {
  _impl_.transpose_a_ = false;
}
inline bool OneDnnMatMulConfig::_internal_transpose_a() const {
  return _impl_.transpose_a_;
}
inline bool OneDnnMatMulConfig::transpose_a() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnMatMulConfig.transpose_a)
  return _internal_transpose_a();
}
inline void OneDnnMatMulConfig::_internal_set_transpose_a(bool value) {
  
  _impl_.transpose_a_ = value;
}
inline void OneDnnMatMulConfig::set_transpose_a(bool value) {
  _internal_set_transpose_a(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnMatMulConfig.transpose_a)
}

// bool transpose_b = 2;
inline void OneDnnMatMulConfig::clear_transpose_b() {
  _impl_.transpose_b_ = false;
}
inline bool OneDnnMatMulConfig::_internal_transpose_b() const {
  return _impl_.transpose_b_;
}
inline bool OneDnnMatMulConfig::transpose_b() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnMatMulConfig.transpose_b)
  return _internal_transpose_b();
}
inline void OneDnnMatMulConfig::_internal_set_transpose_b(bool value) {
  
  _impl_.transpose_b_ = value;
}
inline void OneDnnMatMulConfig::set_transpose_b(bool value) {
  _internal_set_transpose_b(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnMatMulConfig.transpose_b)
}

// .xla.cpu.OneDnnFusionConfig fusions = 3;
inline bool OneDnnMatMulConfig::_internal_has_fusions() const {
  return this != internal_default_instance() && _impl_.fusions_ != nullptr;
}
inline bool OneDnnMatMulConfig::has_fusions() const {
  return _internal_has_fusions();
}
inline void OneDnnMatMulConfig::clear_fusions() {
  if (GetArenaForAllocation() == nullptr && _impl_.fusions_ != nullptr) {
    delete _impl_.fusions_;
  }
  _impl_.fusions_ = nullptr;
}
inline const ::xla::cpu::OneDnnFusionConfig& OneDnnMatMulConfig::_internal_fusions() const {
  const ::xla::cpu::OneDnnFusionConfig* p = _impl_.fusions_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnFusionConfig&>(
      ::xla::cpu::_OneDnnFusionConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnFusionConfig& OneDnnMatMulConfig::fusions() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnMatMulConfig.fusions)
  return _internal_fusions();
}
inline void OneDnnMatMulConfig::unsafe_arena_set_allocated_fusions(
    ::xla::cpu::OneDnnFusionConfig* fusions) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.fusions_);
  }
  _impl_.fusions_ = fusions;
  if (fusions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnMatMulConfig.fusions)
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnMatMulConfig::release_fusions() {
  
  ::xla::cpu::OneDnnFusionConfig* temp = _impl_.fusions_;
  _impl_.fusions_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnMatMulConfig::unsafe_arena_release_fusions() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnMatMulConfig.fusions)
  
  ::xla::cpu::OneDnnFusionConfig* temp = _impl_.fusions_;
  _impl_.fusions_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnMatMulConfig::_internal_mutable_fusions() {
  
  if (_impl_.fusions_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnFusionConfig>(GetArenaForAllocation());
    _impl_.fusions_ = p;
  }
  return _impl_.fusions_;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnMatMulConfig::mutable_fusions() {
  ::xla::cpu::OneDnnFusionConfig* _msg = _internal_mutable_fusions();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnMatMulConfig.fusions)
  return _msg;
}
inline void OneDnnMatMulConfig::set_allocated_fusions(::xla::cpu::OneDnnFusionConfig* fusions) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.fusions_;
  }
  if (fusions) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(fusions);
    if (message_arena != submessage_arena) {
      fusions = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fusions, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.fusions_ = fusions;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnMatMulConfig.fusions)
}

// .xla.cpu.OneDnnOptimizationConfig optimization_config = 8;
inline bool OneDnnMatMulConfig::_internal_has_optimization_config() const {
  return this != internal_default_instance() && _impl_.optimization_config_ != nullptr;
}
inline bool OneDnnMatMulConfig::has_optimization_config() const {
  return _internal_has_optimization_config();
}
inline void OneDnnMatMulConfig::clear_optimization_config() {
  if (GetArenaForAllocation() == nullptr && _impl_.optimization_config_ != nullptr) {
    delete _impl_.optimization_config_;
  }
  _impl_.optimization_config_ = nullptr;
}
inline const ::xla::cpu::OneDnnOptimizationConfig& OneDnnMatMulConfig::_internal_optimization_config() const {
  const ::xla::cpu::OneDnnOptimizationConfig* p = _impl_.optimization_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnOptimizationConfig&>(
      ::xla::cpu::_OneDnnOptimizationConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnOptimizationConfig& OneDnnMatMulConfig::optimization_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnMatMulConfig.optimization_config)
  return _internal_optimization_config();
}
inline void OneDnnMatMulConfig::unsafe_arena_set_allocated_optimization_config(
    ::xla::cpu::OneDnnOptimizationConfig* optimization_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimization_config_);
  }
  _impl_.optimization_config_ = optimization_config;
  if (optimization_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnMatMulConfig.optimization_config)
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnMatMulConfig::release_optimization_config() {
  
  ::xla::cpu::OneDnnOptimizationConfig* temp = _impl_.optimization_config_;
  _impl_.optimization_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnMatMulConfig::unsafe_arena_release_optimization_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnMatMulConfig.optimization_config)
  
  ::xla::cpu::OneDnnOptimizationConfig* temp = _impl_.optimization_config_;
  _impl_.optimization_config_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnMatMulConfig::_internal_mutable_optimization_config() {
  
  if (_impl_.optimization_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnOptimizationConfig>(GetArenaForAllocation());
    _impl_.optimization_config_ = p;
  }
  return _impl_.optimization_config_;
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnMatMulConfig::mutable_optimization_config() {
  ::xla::cpu::OneDnnOptimizationConfig* _msg = _internal_mutable_optimization_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnMatMulConfig.optimization_config)
  return _msg;
}
inline void OneDnnMatMulConfig::set_allocated_optimization_config(::xla::cpu::OneDnnOptimizationConfig* optimization_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.optimization_config_;
  }
  if (optimization_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(optimization_config);
    if (message_arena != submessage_arena) {
      optimization_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.optimization_config_ = optimization_config;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnMatMulConfig.optimization_config)
}

// -------------------------------------------------------------------

// OneDnnWindowProto

// repeated uint64 size = 1;
inline int OneDnnWindowProto::_internal_size_size() const {
  return _impl_.size_.size();
}
inline int OneDnnWindowProto::size_size() const {
  return _internal_size_size();
}
inline void OneDnnWindowProto::clear_size() {
  _impl_.size_.Clear();
}
inline uint64_t OneDnnWindowProto::_internal_size(int index) const {
  return _impl_.size_.Get(index);
}
inline uint64_t OneDnnWindowProto::size(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnWindowProto.size)
  return _internal_size(index);
}
inline void OneDnnWindowProto::set_size(int index, uint64_t value) {
  _impl_.size_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnWindowProto.size)
}
inline void OneDnnWindowProto::_internal_add_size(uint64_t value) {
  _impl_.size_.Add(value);
}
inline void OneDnnWindowProto::add_size(uint64_t value) {
  _internal_add_size(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnWindowProto.size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::_internal_size() const {
  return _impl_.size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::size() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnWindowProto.size)
  return _internal_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::_internal_mutable_size() {
  return &_impl_.size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::mutable_size() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnWindowProto.size)
  return _internal_mutable_size();
}

// repeated uint64 pad_left = 2;
inline int OneDnnWindowProto::_internal_pad_left_size() const {
  return _impl_.pad_left_.size();
}
inline int OneDnnWindowProto::pad_left_size() const {
  return _internal_pad_left_size();
}
inline void OneDnnWindowProto::clear_pad_left() {
  _impl_.pad_left_.Clear();
}
inline uint64_t OneDnnWindowProto::_internal_pad_left(int index) const {
  return _impl_.pad_left_.Get(index);
}
inline uint64_t OneDnnWindowProto::pad_left(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnWindowProto.pad_left)
  return _internal_pad_left(index);
}
inline void OneDnnWindowProto::set_pad_left(int index, uint64_t value) {
  _impl_.pad_left_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnWindowProto.pad_left)
}
inline void OneDnnWindowProto::_internal_add_pad_left(uint64_t value) {
  _impl_.pad_left_.Add(value);
}
inline void OneDnnWindowProto::add_pad_left(uint64_t value) {
  _internal_add_pad_left(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnWindowProto.pad_left)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::_internal_pad_left() const {
  return _impl_.pad_left_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::pad_left() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnWindowProto.pad_left)
  return _internal_pad_left();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::_internal_mutable_pad_left() {
  return &_impl_.pad_left_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::mutable_pad_left() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnWindowProto.pad_left)
  return _internal_mutable_pad_left();
}

// repeated uint64 pad_right = 3;
inline int OneDnnWindowProto::_internal_pad_right_size() const {
  return _impl_.pad_right_.size();
}
inline int OneDnnWindowProto::pad_right_size() const {
  return _internal_pad_right_size();
}
inline void OneDnnWindowProto::clear_pad_right() {
  _impl_.pad_right_.Clear();
}
inline uint64_t OneDnnWindowProto::_internal_pad_right(int index) const {
  return _impl_.pad_right_.Get(index);
}
inline uint64_t OneDnnWindowProto::pad_right(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnWindowProto.pad_right)
  return _internal_pad_right(index);
}
inline void OneDnnWindowProto::set_pad_right(int index, uint64_t value) {
  _impl_.pad_right_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnWindowProto.pad_right)
}
inline void OneDnnWindowProto::_internal_add_pad_right(uint64_t value) {
  _impl_.pad_right_.Add(value);
}
inline void OneDnnWindowProto::add_pad_right(uint64_t value) {
  _internal_add_pad_right(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnWindowProto.pad_right)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::_internal_pad_right() const {
  return _impl_.pad_right_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::pad_right() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnWindowProto.pad_right)
  return _internal_pad_right();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::_internal_mutable_pad_right() {
  return &_impl_.pad_right_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::mutable_pad_right() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnWindowProto.pad_right)
  return _internal_mutable_pad_right();
}

// repeated uint64 strides = 4;
inline int OneDnnWindowProto::_internal_strides_size() const {
  return _impl_.strides_.size();
}
inline int OneDnnWindowProto::strides_size() const {
  return _internal_strides_size();
}
inline void OneDnnWindowProto::clear_strides() {
  _impl_.strides_.Clear();
}
inline uint64_t OneDnnWindowProto::_internal_strides(int index) const {
  return _impl_.strides_.Get(index);
}
inline uint64_t OneDnnWindowProto::strides(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnWindowProto.strides)
  return _internal_strides(index);
}
inline void OneDnnWindowProto::set_strides(int index, uint64_t value) {
  _impl_.strides_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnWindowProto.strides)
}
inline void OneDnnWindowProto::_internal_add_strides(uint64_t value) {
  _impl_.strides_.Add(value);
}
inline void OneDnnWindowProto::add_strides(uint64_t value) {
  _internal_add_strides(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnWindowProto.strides)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::_internal_strides() const {
  return _impl_.strides_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::strides() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnWindowProto.strides)
  return _internal_strides();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::_internal_mutable_strides() {
  return &_impl_.strides_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::mutable_strides() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnWindowProto.strides)
  return _internal_mutable_strides();
}

// repeated uint64 window_dilations = 5;
inline int OneDnnWindowProto::_internal_window_dilations_size() const {
  return _impl_.window_dilations_.size();
}
inline int OneDnnWindowProto::window_dilations_size() const {
  return _internal_window_dilations_size();
}
inline void OneDnnWindowProto::clear_window_dilations() {
  _impl_.window_dilations_.Clear();
}
inline uint64_t OneDnnWindowProto::_internal_window_dilations(int index) const {
  return _impl_.window_dilations_.Get(index);
}
inline uint64_t OneDnnWindowProto::window_dilations(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnWindowProto.window_dilations)
  return _internal_window_dilations(index);
}
inline void OneDnnWindowProto::set_window_dilations(int index, uint64_t value) {
  _impl_.window_dilations_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnWindowProto.window_dilations)
}
inline void OneDnnWindowProto::_internal_add_window_dilations(uint64_t value) {
  _impl_.window_dilations_.Add(value);
}
inline void OneDnnWindowProto::add_window_dilations(uint64_t value) {
  _internal_add_window_dilations(value);
  // @@protoc_insertion_point(field_add:xla.cpu.OneDnnWindowProto.window_dilations)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::_internal_window_dilations() const {
  return _impl_.window_dilations_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
OneDnnWindowProto::window_dilations() const {
  // @@protoc_insertion_point(field_list:xla.cpu.OneDnnWindowProto.window_dilations)
  return _internal_window_dilations();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::_internal_mutable_window_dilations() {
  return &_impl_.window_dilations_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
OneDnnWindowProto::mutable_window_dilations() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.OneDnnWindowProto.window_dilations)
  return _internal_mutable_window_dilations();
}

// -------------------------------------------------------------------

// OneDnnNormConfig

// .xla.cpu.OneDnnNormConfig.ScaleAndShift rescale = 1;
inline void OneDnnNormConfig::clear_rescale() {
  _impl_.rescale_ = 0;
}
inline ::xla::cpu::OneDnnNormConfig_ScaleAndShift OneDnnNormConfig::_internal_rescale() const {
  return static_cast< ::xla::cpu::OneDnnNormConfig_ScaleAndShift >(_impl_.rescale_);
}
inline ::xla::cpu::OneDnnNormConfig_ScaleAndShift OneDnnNormConfig::rescale() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnNormConfig.rescale)
  return _internal_rescale();
}
inline void OneDnnNormConfig::_internal_set_rescale(::xla::cpu::OneDnnNormConfig_ScaleAndShift value) {
  
  _impl_.rescale_ = value;
}
inline void OneDnnNormConfig::set_rescale(::xla::cpu::OneDnnNormConfig_ScaleAndShift value) {
  _internal_set_rescale(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnNormConfig.rescale)
}

// int32 epsilon_typecast = 2;
inline void OneDnnNormConfig::clear_epsilon_typecast() {
  _impl_.epsilon_typecast_ = 0;
}
inline int32_t OneDnnNormConfig::_internal_epsilon_typecast() const {
  return _impl_.epsilon_typecast_;
}
inline int32_t OneDnnNormConfig::epsilon_typecast() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnNormConfig.epsilon_typecast)
  return _internal_epsilon_typecast();
}
inline void OneDnnNormConfig::_internal_set_epsilon_typecast(int32_t value) {
  
  _impl_.epsilon_typecast_ = value;
}
inline void OneDnnNormConfig::set_epsilon_typecast(int32_t value) {
  _internal_set_epsilon_typecast(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnNormConfig.epsilon_typecast)
}

// .xla.cpu.OneDnnFusionConfig fusions = 3;
inline bool OneDnnNormConfig::_internal_has_fusions() const {
  return this != internal_default_instance() && _impl_.fusions_ != nullptr;
}
inline bool OneDnnNormConfig::has_fusions() const {
  return _internal_has_fusions();
}
inline void OneDnnNormConfig::clear_fusions() {
  if (GetArenaForAllocation() == nullptr && _impl_.fusions_ != nullptr) {
    delete _impl_.fusions_;
  }
  _impl_.fusions_ = nullptr;
}
inline const ::xla::cpu::OneDnnFusionConfig& OneDnnNormConfig::_internal_fusions() const {
  const ::xla::cpu::OneDnnFusionConfig* p = _impl_.fusions_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnFusionConfig&>(
      ::xla::cpu::_OneDnnFusionConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnFusionConfig& OneDnnNormConfig::fusions() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnNormConfig.fusions)
  return _internal_fusions();
}
inline void OneDnnNormConfig::unsafe_arena_set_allocated_fusions(
    ::xla::cpu::OneDnnFusionConfig* fusions) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.fusions_);
  }
  _impl_.fusions_ = fusions;
  if (fusions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnNormConfig.fusions)
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnNormConfig::release_fusions() {
  
  ::xla::cpu::OneDnnFusionConfig* temp = _impl_.fusions_;
  _impl_.fusions_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnNormConfig::unsafe_arena_release_fusions() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnNormConfig.fusions)
  
  ::xla::cpu::OneDnnFusionConfig* temp = _impl_.fusions_;
  _impl_.fusions_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnNormConfig::_internal_mutable_fusions() {
  
  if (_impl_.fusions_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnFusionConfig>(GetArenaForAllocation());
    _impl_.fusions_ = p;
  }
  return _impl_.fusions_;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnNormConfig::mutable_fusions() {
  ::xla::cpu::OneDnnFusionConfig* _msg = _internal_mutable_fusions();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnNormConfig.fusions)
  return _msg;
}
inline void OneDnnNormConfig::set_allocated_fusions(::xla::cpu::OneDnnFusionConfig* fusions) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.fusions_;
  }
  if (fusions) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(fusions);
    if (message_arena != submessage_arena) {
      fusions = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fusions, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.fusions_ = fusions;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnNormConfig.fusions)
}

// -------------------------------------------------------------------

// OneDnnConvolutionConfig

// uint64 dims = 1;
inline void OneDnnConvolutionConfig::clear_dims() {
  _impl_.dims_ = uint64_t{0u};
}
inline uint64_t OneDnnConvolutionConfig::_internal_dims() const {
  return _impl_.dims_;
}
inline uint64_t OneDnnConvolutionConfig::dims() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.dims)
  return _internal_dims();
}
inline void OneDnnConvolutionConfig::_internal_set_dims(uint64_t value) {
  
  _impl_.dims_ = value;
}
inline void OneDnnConvolutionConfig::set_dims(uint64_t value) {
  _internal_set_dims(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnConvolutionConfig.dims)
}

// .xla.cpu.OneDnnTensorLayoutProto input = 2;
inline bool OneDnnConvolutionConfig::_internal_has_input() const {
  return this != internal_default_instance() && _impl_.input_ != nullptr;
}
inline bool OneDnnConvolutionConfig::has_input() const {
  return _internal_has_input();
}
inline void OneDnnConvolutionConfig::clear_input() {
  if (GetArenaForAllocation() == nullptr && _impl_.input_ != nullptr) {
    delete _impl_.input_;
  }
  _impl_.input_ = nullptr;
}
inline const ::xla::cpu::OneDnnTensorLayoutProto& OneDnnConvolutionConfig::_internal_input() const {
  const ::xla::cpu::OneDnnTensorLayoutProto* p = _impl_.input_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnTensorLayoutProto&>(
      ::xla::cpu::_OneDnnTensorLayoutProto_default_instance_);
}
inline const ::xla::cpu::OneDnnTensorLayoutProto& OneDnnConvolutionConfig::input() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.input)
  return _internal_input();
}
inline void OneDnnConvolutionConfig::unsafe_arena_set_allocated_input(
    ::xla::cpu::OneDnnTensorLayoutProto* input) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.input_);
  }
  _impl_.input_ = input;
  if (input) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnConvolutionConfig.input)
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::release_input() {
  
  ::xla::cpu::OneDnnTensorLayoutProto* temp = _impl_.input_;
  _impl_.input_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::unsafe_arena_release_input() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnConvolutionConfig.input)
  
  ::xla::cpu::OneDnnTensorLayoutProto* temp = _impl_.input_;
  _impl_.input_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::_internal_mutable_input() {
  
  if (_impl_.input_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnTensorLayoutProto>(GetArenaForAllocation());
    _impl_.input_ = p;
  }
  return _impl_.input_;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::mutable_input() {
  ::xla::cpu::OneDnnTensorLayoutProto* _msg = _internal_mutable_input();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnConvolutionConfig.input)
  return _msg;
}
inline void OneDnnConvolutionConfig::set_allocated_input(::xla::cpu::OneDnnTensorLayoutProto* input) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.input_;
  }
  if (input) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(input);
    if (message_arena != submessage_arena) {
      input = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, input, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.input_ = input;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnConvolutionConfig.input)
}

// .xla.cpu.OneDnnTensorLayoutProto kernel = 3;
inline bool OneDnnConvolutionConfig::_internal_has_kernel() const {
  return this != internal_default_instance() && _impl_.kernel_ != nullptr;
}
inline bool OneDnnConvolutionConfig::has_kernel() const {
  return _internal_has_kernel();
}
inline void OneDnnConvolutionConfig::clear_kernel() {
  if (GetArenaForAllocation() == nullptr && _impl_.kernel_ != nullptr) {
    delete _impl_.kernel_;
  }
  _impl_.kernel_ = nullptr;
}
inline const ::xla::cpu::OneDnnTensorLayoutProto& OneDnnConvolutionConfig::_internal_kernel() const {
  const ::xla::cpu::OneDnnTensorLayoutProto* p = _impl_.kernel_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnTensorLayoutProto&>(
      ::xla::cpu::_OneDnnTensorLayoutProto_default_instance_);
}
inline const ::xla::cpu::OneDnnTensorLayoutProto& OneDnnConvolutionConfig::kernel() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.kernel)
  return _internal_kernel();
}
inline void OneDnnConvolutionConfig::unsafe_arena_set_allocated_kernel(
    ::xla::cpu::OneDnnTensorLayoutProto* kernel) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.kernel_);
  }
  _impl_.kernel_ = kernel;
  if (kernel) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnConvolutionConfig.kernel)
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::release_kernel() {
  
  ::xla::cpu::OneDnnTensorLayoutProto* temp = _impl_.kernel_;
  _impl_.kernel_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::unsafe_arena_release_kernel() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnConvolutionConfig.kernel)
  
  ::xla::cpu::OneDnnTensorLayoutProto* temp = _impl_.kernel_;
  _impl_.kernel_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::_internal_mutable_kernel() {
  
  if (_impl_.kernel_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnTensorLayoutProto>(GetArenaForAllocation());
    _impl_.kernel_ = p;
  }
  return _impl_.kernel_;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::mutable_kernel() {
  ::xla::cpu::OneDnnTensorLayoutProto* _msg = _internal_mutable_kernel();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnConvolutionConfig.kernel)
  return _msg;
}
inline void OneDnnConvolutionConfig::set_allocated_kernel(::xla::cpu::OneDnnTensorLayoutProto* kernel) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.kernel_;
  }
  if (kernel) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(kernel);
    if (message_arena != submessage_arena) {
      kernel = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, kernel, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.kernel_ = kernel;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnConvolutionConfig.kernel)
}

// .xla.cpu.OneDnnTensorLayoutProto output = 4;
inline bool OneDnnConvolutionConfig::_internal_has_output() const {
  return this != internal_default_instance() && _impl_.output_ != nullptr;
}
inline bool OneDnnConvolutionConfig::has_output() const {
  return _internal_has_output();
}
inline void OneDnnConvolutionConfig::clear_output() {
  if (GetArenaForAllocation() == nullptr && _impl_.output_ != nullptr) {
    delete _impl_.output_;
  }
  _impl_.output_ = nullptr;
}
inline const ::xla::cpu::OneDnnTensorLayoutProto& OneDnnConvolutionConfig::_internal_output() const {
  const ::xla::cpu::OneDnnTensorLayoutProto* p = _impl_.output_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnTensorLayoutProto&>(
      ::xla::cpu::_OneDnnTensorLayoutProto_default_instance_);
}
inline const ::xla::cpu::OneDnnTensorLayoutProto& OneDnnConvolutionConfig::output() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.output)
  return _internal_output();
}
inline void OneDnnConvolutionConfig::unsafe_arena_set_allocated_output(
    ::xla::cpu::OneDnnTensorLayoutProto* output) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.output_);
  }
  _impl_.output_ = output;
  if (output) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnConvolutionConfig.output)
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::release_output() {
  
  ::xla::cpu::OneDnnTensorLayoutProto* temp = _impl_.output_;
  _impl_.output_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::unsafe_arena_release_output() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnConvolutionConfig.output)
  
  ::xla::cpu::OneDnnTensorLayoutProto* temp = _impl_.output_;
  _impl_.output_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::_internal_mutable_output() {
  
  if (_impl_.output_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnTensorLayoutProto>(GetArenaForAllocation());
    _impl_.output_ = p;
  }
  return _impl_.output_;
}
inline ::xla::cpu::OneDnnTensorLayoutProto* OneDnnConvolutionConfig::mutable_output() {
  ::xla::cpu::OneDnnTensorLayoutProto* _msg = _internal_mutable_output();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnConvolutionConfig.output)
  return _msg;
}
inline void OneDnnConvolutionConfig::set_allocated_output(::xla::cpu::OneDnnTensorLayoutProto* output) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.output_;
  }
  if (output) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(output);
    if (message_arena != submessage_arena) {
      output = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.output_ = output;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnConvolutionConfig.output)
}

// .xla.cpu.OneDnnWindowProto window = 5;
inline bool OneDnnConvolutionConfig::_internal_has_window() const {
  return this != internal_default_instance() && _impl_.window_ != nullptr;
}
inline bool OneDnnConvolutionConfig::has_window() const {
  return _internal_has_window();
}
inline void OneDnnConvolutionConfig::clear_window() {
  if (GetArenaForAllocation() == nullptr && _impl_.window_ != nullptr) {
    delete _impl_.window_;
  }
  _impl_.window_ = nullptr;
}
inline const ::xla::cpu::OneDnnWindowProto& OneDnnConvolutionConfig::_internal_window() const {
  const ::xla::cpu::OneDnnWindowProto* p = _impl_.window_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnWindowProto&>(
      ::xla::cpu::_OneDnnWindowProto_default_instance_);
}
inline const ::xla::cpu::OneDnnWindowProto& OneDnnConvolutionConfig::window() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.window)
  return _internal_window();
}
inline void OneDnnConvolutionConfig::unsafe_arena_set_allocated_window(
    ::xla::cpu::OneDnnWindowProto* window) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.window_);
  }
  _impl_.window_ = window;
  if (window) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnConvolutionConfig.window)
}
inline ::xla::cpu::OneDnnWindowProto* OneDnnConvolutionConfig::release_window() {
  
  ::xla::cpu::OneDnnWindowProto* temp = _impl_.window_;
  _impl_.window_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnWindowProto* OneDnnConvolutionConfig::unsafe_arena_release_window() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnConvolutionConfig.window)
  
  ::xla::cpu::OneDnnWindowProto* temp = _impl_.window_;
  _impl_.window_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnWindowProto* OneDnnConvolutionConfig::_internal_mutable_window() {
  
  if (_impl_.window_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnWindowProto>(GetArenaForAllocation());
    _impl_.window_ = p;
  }
  return _impl_.window_;
}
inline ::xla::cpu::OneDnnWindowProto* OneDnnConvolutionConfig::mutable_window() {
  ::xla::cpu::OneDnnWindowProto* _msg = _internal_mutable_window();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnConvolutionConfig.window)
  return _msg;
}
inline void OneDnnConvolutionConfig::set_allocated_window(::xla::cpu::OneDnnWindowProto* window) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.window_;
  }
  if (window) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(window);
    if (message_arena != submessage_arena) {
      window = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, window, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.window_ = window;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnConvolutionConfig.window)
}

// .xla.cpu.OneDnnFusionConfig fusions = 6;
inline bool OneDnnConvolutionConfig::_internal_has_fusions() const {
  return this != internal_default_instance() && _impl_.fusions_ != nullptr;
}
inline bool OneDnnConvolutionConfig::has_fusions() const {
  return _internal_has_fusions();
}
inline void OneDnnConvolutionConfig::clear_fusions() {
  if (GetArenaForAllocation() == nullptr && _impl_.fusions_ != nullptr) {
    delete _impl_.fusions_;
  }
  _impl_.fusions_ = nullptr;
}
inline const ::xla::cpu::OneDnnFusionConfig& OneDnnConvolutionConfig::_internal_fusions() const {
  const ::xla::cpu::OneDnnFusionConfig* p = _impl_.fusions_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnFusionConfig&>(
      ::xla::cpu::_OneDnnFusionConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnFusionConfig& OneDnnConvolutionConfig::fusions() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.fusions)
  return _internal_fusions();
}
inline void OneDnnConvolutionConfig::unsafe_arena_set_allocated_fusions(
    ::xla::cpu::OneDnnFusionConfig* fusions) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.fusions_);
  }
  _impl_.fusions_ = fusions;
  if (fusions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnConvolutionConfig.fusions)
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnConvolutionConfig::release_fusions() {
  
  ::xla::cpu::OneDnnFusionConfig* temp = _impl_.fusions_;
  _impl_.fusions_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnConvolutionConfig::unsafe_arena_release_fusions() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnConvolutionConfig.fusions)
  
  ::xla::cpu::OneDnnFusionConfig* temp = _impl_.fusions_;
  _impl_.fusions_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnConvolutionConfig::_internal_mutable_fusions() {
  
  if (_impl_.fusions_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnFusionConfig>(GetArenaForAllocation());
    _impl_.fusions_ = p;
  }
  return _impl_.fusions_;
}
inline ::xla::cpu::OneDnnFusionConfig* OneDnnConvolutionConfig::mutable_fusions() {
  ::xla::cpu::OneDnnFusionConfig* _msg = _internal_mutable_fusions();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnConvolutionConfig.fusions)
  return _msg;
}
inline void OneDnnConvolutionConfig::set_allocated_fusions(::xla::cpu::OneDnnFusionConfig* fusions) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.fusions_;
  }
  if (fusions) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(fusions);
    if (message_arena != submessage_arena) {
      fusions = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fusions, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.fusions_ = fusions;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnConvolutionConfig.fusions)
}

// uint64 feature_groups = 7;
inline void OneDnnConvolutionConfig::clear_feature_groups() {
  _impl_.feature_groups_ = uint64_t{0u};
}
inline uint64_t OneDnnConvolutionConfig::_internal_feature_groups() const {
  return _impl_.feature_groups_;
}
inline uint64_t OneDnnConvolutionConfig::feature_groups() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.feature_groups)
  return _internal_feature_groups();
}
inline void OneDnnConvolutionConfig::_internal_set_feature_groups(uint64_t value) {
  
  _impl_.feature_groups_ = value;
}
inline void OneDnnConvolutionConfig::set_feature_groups(uint64_t value) {
  _internal_set_feature_groups(value);
  // @@protoc_insertion_point(field_set:xla.cpu.OneDnnConvolutionConfig.feature_groups)
}

// .xla.cpu.OneDnnOptimizationConfig optimization_config = 8;
inline bool OneDnnConvolutionConfig::_internal_has_optimization_config() const {
  return this != internal_default_instance() && _impl_.optimization_config_ != nullptr;
}
inline bool OneDnnConvolutionConfig::has_optimization_config() const {
  return _internal_has_optimization_config();
}
inline void OneDnnConvolutionConfig::clear_optimization_config() {
  if (GetArenaForAllocation() == nullptr && _impl_.optimization_config_ != nullptr) {
    delete _impl_.optimization_config_;
  }
  _impl_.optimization_config_ = nullptr;
}
inline const ::xla::cpu::OneDnnOptimizationConfig& OneDnnConvolutionConfig::_internal_optimization_config() const {
  const ::xla::cpu::OneDnnOptimizationConfig* p = _impl_.optimization_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::OneDnnOptimizationConfig&>(
      ::xla::cpu::_OneDnnOptimizationConfig_default_instance_);
}
inline const ::xla::cpu::OneDnnOptimizationConfig& OneDnnConvolutionConfig::optimization_config() const {
  // @@protoc_insertion_point(field_get:xla.cpu.OneDnnConvolutionConfig.optimization_config)
  return _internal_optimization_config();
}
inline void OneDnnConvolutionConfig::unsafe_arena_set_allocated_optimization_config(
    ::xla::cpu::OneDnnOptimizationConfig* optimization_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimization_config_);
  }
  _impl_.optimization_config_ = optimization_config;
  if (optimization_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.OneDnnConvolutionConfig.optimization_config)
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnConvolutionConfig::release_optimization_config() {
  
  ::xla::cpu::OneDnnOptimizationConfig* temp = _impl_.optimization_config_;
  _impl_.optimization_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnConvolutionConfig::unsafe_arena_release_optimization_config() {
  // @@protoc_insertion_point(field_release:xla.cpu.OneDnnConvolutionConfig.optimization_config)
  
  ::xla::cpu::OneDnnOptimizationConfig* temp = _impl_.optimization_config_;
  _impl_.optimization_config_ = nullptr;
  return temp;
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnConvolutionConfig::_internal_mutable_optimization_config() {
  
  if (_impl_.optimization_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::OneDnnOptimizationConfig>(GetArenaForAllocation());
    _impl_.optimization_config_ = p;
  }
  return _impl_.optimization_config_;
}
inline ::xla::cpu::OneDnnOptimizationConfig* OneDnnConvolutionConfig::mutable_optimization_config() {
  ::xla::cpu::OneDnnOptimizationConfig* _msg = _internal_mutable_optimization_config();
  // @@protoc_insertion_point(field_mutable:xla.cpu.OneDnnConvolutionConfig.optimization_config)
  return _msg;
}
inline void OneDnnConvolutionConfig::set_allocated_optimization_config(::xla::cpu::OneDnnOptimizationConfig* optimization_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.optimization_config_;
  }
  if (optimization_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(optimization_config);
    if (message_arena != submessage_arena) {
      optimization_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.optimization_config_ = optimization_config;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.OneDnnConvolutionConfig.optimization_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace cpu
}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::cpu::OneDnnFusionConfig_FusionKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::cpu::OneDnnFusionConfig_FusionKind>() {
  return ::xla::cpu::OneDnnFusionConfig_FusionKind_descriptor();
}
template <> struct is_proto_enum< ::xla::cpu::OneDnnNormConfig_ScaleAndShift> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::cpu::OneDnnNormConfig_ScaleAndShift>() {
  return ::xla::cpu::OneDnnNormConfig_ScaleAndShift_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fonednn_5fconfig_2eproto
