import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
import keras
from keras.metrics import R2Score
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, PowerTransformer
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {tf.config.list_physical_devices('GPU')}")

# 设置随机种子以确保可重复性
np.random.seed(42)
tf.random.set_seed(42)

# 加载数据
train_validate_examples_path = 'inputs/train_validate_data.npy'
train_validate_labels_path = 'inputs/train_validate_label.npy'

train_validate_examples = np.load(train_validate_examples_path)
train_validate_labels = np.load(train_validate_labels_path)

print(f"Original data shapes - Examples: {train_validate_examples.shape}, Labels: {train_validate_labels.shape}")
print(f"Examples range: {np.min(train_validate_examples):.2e} to {np.max(train_validate_examples):.2e}")
print(f"Labels range: {np.min(train_validate_labels):.2e} to {np.max(train_validate_labels):.2e}")

# 分析每个标签参数的分布
print("\nAnalyzing label distributions:")
for i in range(train_validate_labels.shape[1]):
    col = train_validate_labels[:, i]
    print(f"Parameter {i}: min={np.min(col):.2e}, max={np.max(col):.2e}, "
          f"mean={np.mean(col):.2e}, std={np.std(col):.2e}, "
          f"range_ratio={np.max(col)/np.min(col):.2e}")

# 改进的数据预处理
print("\nApplying improved preprocessing...")

# 1. 对输入数据使用log10变换，处理零值和负值
train_validate_examples_safe = np.where(train_validate_examples <= 0, 1e-20, train_validate_examples)
train_validate_examples_log = np.log10(train_validate_examples_safe)

# 2. 对标签数据使用更智能的变换
train_validate_labels_processed = np.copy(train_validate_labels)
label_transforms = []  # 记录每个参数使用的变换类型

for i in range(train_validate_labels.shape[1]):
    col = train_validate_labels[:, i]
    range_ratio = np.max(col) / np.min(col) if np.min(col) > 0 else float('inf')
    
    if np.min(col) > 0 and range_ratio > 100:  # 如果都是正数且范围很大，使用log变换
        train_validate_labels_processed[:, i] = np.log10(col)
        label_transforms.append('log10')
        print(f"Parameter {i}: Applied log10 transform (range ratio: {range_ratio:.2e})")
    else:
        train_validate_labels_processed[:, i] = col
        label_transforms.append('none')
        print(f"Parameter {i}: No transform (range ratio: {range_ratio:.2e})")

# 3. 正确的数据分割
train_examples, validate_examples, train_labels, validate_labels = train_test_split(
    train_validate_examples_log, train_validate_labels_processed,
    test_size=0.2, random_state=42, shuffle=True
)

print(f"\nAfter split:")
print(f"Train: {train_examples.shape}, {train_labels.shape}")
print(f"Validation: {validate_examples.shape}, {validate_labels.shape}")

# 4. 标准化
examples_scaler = StandardScaler()
labels_scaler = RobustScaler()  # RobustScaler对异常值更稳健

train_examples_scaled = examples_scaler.fit_transform(train_examples)
validate_examples_scaled = examples_scaler.transform(validate_examples)

train_labels_scaled = labels_scaler.fit_transform(train_labels)
validate_labels_scaled = labels_scaler.transform(validate_labels)

print(f"\nAfter scaling:")
print(f"Train examples: mean={np.mean(train_examples_scaled):.3f}, std={np.std(train_examples_scaled):.3f}")
print(f"Train labels: mean={np.mean(train_labels_scaled):.3f}, std={np.std(train_labels_scaled):.3f}")

# 5. 创建数据集
BATCH_SIZE = 32  # 减小batch size以提高稳定性
SHUFFLE_BUFFER_SIZE = 1000

train_dataset = tf.data.Dataset.from_tensor_slices((train_examples_scaled, train_labels_scaled))
validate_dataset = tf.data.Dataset.from_tensor_slices((validate_examples_scaled, validate_labels_scaled))

train_dataset = train_dataset.shuffle(SHUFFLE_BUFFER_SIZE).batch(BATCH_SIZE)
validate_dataset = validate_dataset.batch(BATCH_SIZE)

# 6. 改进的模型架构
def create_model():
    model = keras.Sequential([
        keras.Input(shape=(51,)),
        
        # 第一层：较大的层来捕获复杂模式
        keras.layers.Dense(256, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.3),
        
        # 第二层：减少维度
        keras.layers.Dense(128, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.3),
        
        # 第三层：进一步减少
        keras.layers.Dense(64, activation='relu'),
        keras.layers.BatchNormalization(),
        keras.layers.Dropout(0.2),
        
        # 第四层：最后的特征提取
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dropout(0.1),
        
        # 输出层
        keras.layers.Dense(6)  # 6个参数
    ])
    return model

model = create_model()

# 7. 改进的编译设置
initial_learning_rate = 0.001
lr_schedule = keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate,
    decay_steps=1000,
    decay_rate=0.9,
    staircase=True
)

model.compile(
    optimizer=keras.optimizers.Adam(learning_rate=lr_schedule),
    loss='mse',
    metrics=[R2Score(class_aggregation='uniform_average'), 'mae']
)

model.summary()

# 8. 改进的回调函数
class ImprovedCallback(keras.callbacks.Callback):
    def __init__(self, validate_dataset, examples_scaler, labels_scaler, label_transforms, interval=50):
        super().__init__()
        self.validate_dataset = validate_dataset
        self.examples_scaler = examples_scaler
        self.labels_scaler = labels_scaler
        self.label_transforms = label_transforms
        self.interval = interval
        self.train_losses = []
        self.val_losses = []
        self.train_r2 = []
        self.val_r2 = []

    def on_epoch_end(self, epoch, logs=None):
        self.train_losses.append(logs.get("loss"))
        self.val_losses.append(logs.get("val_loss"))
        self.train_r2.append(logs.get("r2_score"))
        self.val_r2.append(logs.get("val_r2_score"))

        if (epoch + 1) % self.interval == 0:
            # 获取一个验证批次进行预测
            for val_x, val_y in self.validate_dataset.take(1):
                pred_scaled = self.model(val_x, training=False).numpy()
                true_scaled = val_y.numpy()
                
                # 反标准化
                pred_unscaled = self.labels_scaler.inverse_transform(pred_scaled)
                true_unscaled = self.labels_scaler.inverse_transform(true_scaled)
                
                # 反变换（如果使用了log变换）
                pred_original = np.copy(pred_unscaled)
                true_original = np.copy(true_unscaled)
                
                for i, transform in enumerate(self.label_transforms):
                    if transform == 'log10':
                        pred_original[:, i] = 10 ** pred_unscaled[:, i]
                        true_original[:, i] = 10 ** true_unscaled[:, i]
                
                # 计算R2分数
                r2_scores = []
                for i in range(6):
                    r2 = r2_score(true_original[:, i], pred_original[:, i])
                    r2_scores.append(r2)
                
                print(f"\nEpoch {epoch + 1}:")
                print(f"Loss: train={logs.get('loss'):.4f}, val={logs.get('val_loss'):.4f}")
                print(f"R2: train={logs.get('r2_score'):.4f}, val={logs.get('val_r2_score'):.4f}")
                print(f"Individual R2 scores: {[f'{r2:.3f}' for r2 in r2_scores]}")
                print("Example prediction vs true (first sample):")
                for i in range(6):
                    print(f"  Param {i}: pred={pred_original[0, i]:.2e}, true={true_original[0, i]:.2e}")
                break

    def on_train_end(self, logs=None):
        # 绘制训练历史
        fig, ((ax1, ax2)) = plt.subplots(1, 2, figsize=(15, 5))
        
        # Loss plot
        ax1.plot(range(1, len(self.train_losses)+1), self.train_losses, label="Training Loss")
        ax1.plot(range(1, len(self.val_losses)+1), self.val_losses, label="Validation Loss")
        ax1.set_xlabel("Epoch")
        ax1.set_ylabel("Loss (MSE)")
        ax1.set_title("Training vs Validation Loss")
        ax1.legend()
        ax1.grid(True)
        ax1.set_yscale('log')
        
        # R2 plot
        ax2.plot(range(1, len(self.train_r2)+1), self.train_r2, label="Training R2")
        ax2.plot(range(1, len(self.val_r2)+1), self.val_r2, label="Validation R2")
        ax2.set_xlabel("Epoch")
        ax2.set_ylabel("R2 Score")
        ax2.set_title("Training vs Validation R2")
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=150, bbox_inches='tight')
        plt.show()

# 9. 其他回调函数
callbacks = [
    ImprovedCallback(validate_dataset, examples_scaler, labels_scaler, label_transforms, interval=100),
    keras.callbacks.EarlyStopping(monitor='val_loss', patience=200, restore_best_weights=True),
    keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=100, min_lr=1e-6),
    keras.callbacks.ModelCheckpoint('best_model.h5', monitor='val_loss', save_best_only=True)
]

# 10. 训练模型
EPOCHS = 2000
print(f"\nStarting training for {EPOCHS} epochs...")

history = model.fit(
    train_dataset,
    epochs=EPOCHS,
    validation_data=validate_dataset,
    callbacks=callbacks,
    verbose=1
)

print("\nTraining completed!")
