// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/hlo_execution_profile_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "xla/service/hlo_profile_printer_data.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto;
namespace xla {
class HloExecutionProfileData;
struct HloExecutionProfileDataDefaultTypeInternal;
extern HloExecutionProfileDataDefaultTypeInternal _HloExecutionProfileData_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::HloExecutionProfileData* Arena::CreateMaybeMessage<::xla::HloExecutionProfileData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class HloExecutionProfileData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloExecutionProfileData) */ {
 public:
  inline HloExecutionProfileData() : HloExecutionProfileData(nullptr) {}
  ~HloExecutionProfileData() override;
  explicit PROTOBUF_CONSTEXPR HloExecutionProfileData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloExecutionProfileData(const HloExecutionProfileData& from);
  HloExecutionProfileData(HloExecutionProfileData&& from) noexcept
    : HloExecutionProfileData() {
    *this = ::std::move(from);
  }

  inline HloExecutionProfileData& operator=(const HloExecutionProfileData& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloExecutionProfileData& operator=(HloExecutionProfileData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloExecutionProfileData& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloExecutionProfileData* internal_default_instance() {
    return reinterpret_cast<const HloExecutionProfileData*>(
               &_HloExecutionProfileData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(HloExecutionProfileData& a, HloExecutionProfileData& b) {
    a.Swap(&b);
  }
  inline void Swap(HloExecutionProfileData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloExecutionProfileData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloExecutionProfileData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloExecutionProfileData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloExecutionProfileData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloExecutionProfileData& from) {
    HloExecutionProfileData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloExecutionProfileData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloExecutionProfileData";
  }
  protected:
  explicit HloExecutionProfileData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileCountersFieldNumber = 2,
    kPrinterDataFieldNumber = 1,
  };
  // repeated int64 profile_counters = 2;
  int profile_counters_size() const;
  private:
  int _internal_profile_counters_size() const;
  public:
  void clear_profile_counters();
  private:
  int64_t _internal_profile_counters(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_profile_counters() const;
  void _internal_add_profile_counters(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_profile_counters();
  public:
  int64_t profile_counters(int index) const;
  void set_profile_counters(int index, int64_t value);
  void add_profile_counters(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      profile_counters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_profile_counters();

  // .xla.HloProfilePrinterData printer_data = 1;
  bool has_printer_data() const;
  private:
  bool _internal_has_printer_data() const;
  public:
  void clear_printer_data();
  const ::xla::HloProfilePrinterData& printer_data() const;
  PROTOBUF_NODISCARD ::xla::HloProfilePrinterData* release_printer_data();
  ::xla::HloProfilePrinterData* mutable_printer_data();
  void set_allocated_printer_data(::xla::HloProfilePrinterData* printer_data);
  private:
  const ::xla::HloProfilePrinterData& _internal_printer_data() const;
  ::xla::HloProfilePrinterData* _internal_mutable_printer_data();
  public:
  void unsafe_arena_set_allocated_printer_data(
      ::xla::HloProfilePrinterData* printer_data);
  ::xla::HloProfilePrinterData* unsafe_arena_release_printer_data();

  // @@protoc_insertion_point(class_scope:xla.HloExecutionProfileData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > profile_counters_;
    mutable std::atomic<int> _profile_counters_cached_byte_size_;
    ::xla::HloProfilePrinterData* printer_data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HloExecutionProfileData

// .xla.HloProfilePrinterData printer_data = 1;
inline bool HloExecutionProfileData::_internal_has_printer_data() const {
  return this != internal_default_instance() && _impl_.printer_data_ != nullptr;
}
inline bool HloExecutionProfileData::has_printer_data() const {
  return _internal_has_printer_data();
}
inline const ::xla::HloProfilePrinterData& HloExecutionProfileData::_internal_printer_data() const {
  const ::xla::HloProfilePrinterData* p = _impl_.printer_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::HloProfilePrinterData&>(
      ::xla::_HloProfilePrinterData_default_instance_);
}
inline const ::xla::HloProfilePrinterData& HloExecutionProfileData::printer_data() const {
  // @@protoc_insertion_point(field_get:xla.HloExecutionProfileData.printer_data)
  return _internal_printer_data();
}
inline void HloExecutionProfileData::unsafe_arena_set_allocated_printer_data(
    ::xla::HloProfilePrinterData* printer_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.printer_data_);
  }
  _impl_.printer_data_ = printer_data;
  if (printer_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.HloExecutionProfileData.printer_data)
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::release_printer_data() {
  
  ::xla::HloProfilePrinterData* temp = _impl_.printer_data_;
  _impl_.printer_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::unsafe_arena_release_printer_data() {
  // @@protoc_insertion_point(field_release:xla.HloExecutionProfileData.printer_data)
  
  ::xla::HloProfilePrinterData* temp = _impl_.printer_data_;
  _impl_.printer_data_ = nullptr;
  return temp;
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::_internal_mutable_printer_data() {
  
  if (_impl_.printer_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloProfilePrinterData>(GetArenaForAllocation());
    _impl_.printer_data_ = p;
  }
  return _impl_.printer_data_;
}
inline ::xla::HloProfilePrinterData* HloExecutionProfileData::mutable_printer_data() {
  ::xla::HloProfilePrinterData* _msg = _internal_mutable_printer_data();
  // @@protoc_insertion_point(field_mutable:xla.HloExecutionProfileData.printer_data)
  return _msg;
}
inline void HloExecutionProfileData::set_allocated_printer_data(::xla::HloProfilePrinterData* printer_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.printer_data_);
  }
  if (printer_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(printer_data));
    if (message_arena != submessage_arena) {
      printer_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, printer_data, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.printer_data_ = printer_data;
  // @@protoc_insertion_point(field_set_allocated:xla.HloExecutionProfileData.printer_data)
}

// repeated int64 profile_counters = 2;
inline int HloExecutionProfileData::_internal_profile_counters_size() const {
  return _impl_.profile_counters_.size();
}
inline int HloExecutionProfileData::profile_counters_size() const {
  return _internal_profile_counters_size();
}
inline void HloExecutionProfileData::clear_profile_counters() {
  _impl_.profile_counters_.Clear();
}
inline int64_t HloExecutionProfileData::_internal_profile_counters(int index) const {
  return _impl_.profile_counters_.Get(index);
}
inline int64_t HloExecutionProfileData::profile_counters(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloExecutionProfileData.profile_counters)
  return _internal_profile_counters(index);
}
inline void HloExecutionProfileData::set_profile_counters(int index, int64_t value) {
  _impl_.profile_counters_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.HloExecutionProfileData.profile_counters)
}
inline void HloExecutionProfileData::_internal_add_profile_counters(int64_t value) {
  _impl_.profile_counters_.Add(value);
}
inline void HloExecutionProfileData::add_profile_counters(int64_t value) {
  _internal_add_profile_counters(value);
  // @@protoc_insertion_point(field_add:xla.HloExecutionProfileData.profile_counters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
HloExecutionProfileData::_internal_profile_counters() const {
  return _impl_.profile_counters_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
HloExecutionProfileData::profile_counters() const {
  // @@protoc_insertion_point(field_list:xla.HloExecutionProfileData.profile_counters)
  return _internal_profile_counters();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
HloExecutionProfileData::_internal_mutable_profile_counters() {
  return &_impl_.profile_counters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
HloExecutionProfileData::mutable_profile_counters() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloExecutionProfileData.profile_counters)
  return _internal_mutable_profile_counters();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fhlo_5fexecution_5fprofile_5fdata_2eproto
