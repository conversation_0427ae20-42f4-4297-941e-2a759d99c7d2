import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf

import keras
from keras.metrics import R2Score

import numpy as np

import matplotlib.pyplot as plt

print(np.__file__)
print(tf.__file__)

from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.model_selection import train_test_split


train_validate_examples_path = 'inputs/train_validate_data.npy'
train_validate_labels_path = 'inputs/train_validate_label.npy'

train_validate_examples = np.load(train_validate_examples_path)
train_validate_labels = np.load(train_validate_labels_path)

# print(train_validate_examples, train_validate_examples.shape, train_validate_examples.dtype, ": examples")
# print(train_validate_labels, train_validate_labels.shape, train_validate_labels.dtype, ": labels")


train_validate_examples = np.log10(train_validate_examples)
train_validate_labels = np.log10(train_validate_labels)


train_examples, validate_examples = train_test_split(train_validate_examples, test_size=0.2)
train_labels, validate_labels = train_test_split(train_validate_labels, test_size=0.2)

examples_scaler = StandardScaler()
labels_scaler = RobustScaler()


train_examples = examples_scaler.fit_transform(train_examples)
validate_examples = examples_scaler.transform(validate_examples)

train_labels = labels_scaler.fit_transform(train_labels)
validate_labels = labels_scaler.transform(validate_labels)


for name in ['train_examples', 'train_labels', 'validate_examples', 'validate_labels']:
    data = locals()[name]
    print(f"{name}:", data.shape, data.dtype)

train_dataset = tf.data.Dataset.from_tensor_slices((train_examples, train_labels))
validate_dataset = tf.data.Dataset.from_tensor_slices((validate_examples, validate_labels))

# print(type(train_dataset))

BATCH_SIZE = 64
SHUFFLE_BUFFER_SIZE = 100

train_dataset = train_dataset.shuffle(SHUFFLE_BUFFER_SIZE).batch(BATCH_SIZE)
validate_dataset = validate_dataset.batch(BATCH_SIZE)

model = keras.Sequential([
    keras.Input(shape=(51, )),
    keras.layers.Dense(64, activation='relu'),
    keras.layers.Dense(128, activation='relu'),
    keras.layers.Dense(64, activation='relu'),
    keras.layers.Dense(32, activation='relu'),
    keras.layers.Dense(6)
])



model.compile(optimizer=keras.optimizers.AdamW(),
              loss=keras.losses.MeanSquaredError(),
              metrics=[R2Score(class_aggregation='uniform_average'), 'mean_squared_logarithmic_error'])

# model.summary()


class CustomCallback(keras.callbacks.Callback):
    def __init__(self, validate_dataset, interval=10):
        super().__init__()
        self.validate_dataset = validate_dataset
        self.interval = interval
        self.train_losses = []
        self.val_losses = []

    def on_epoch_end(self, epoch, logs=None):
        self.train_losses.append(logs.get("loss"))
        self.val_losses.append(logs.get("val_loss"))

        if (epoch + 1) % self.interval == 0:
            for val_x, val_y in self.validate_dataset.take(1):
                pred = self.model(val_x, training=False).numpy()
                true = val_y.numpy()

                pred_original = labels_scaler.inverse_transform(np.copy(pred))
                true_original = labels_scaler.inverse_transform(np.copy(true))

                pred_original = 10 ** pred_original
                true_original = 10 ** true_original

                print(f"\nEpoch {epoch + 1} (inverse transformed):")
                print("Example Prediction:", pred_original[0])
                print("Target Label     :", true_original[0])
                break

    def on_train_end(self, logs=None):
        plt.figure(figsize=(8, 5))
        plt.plot(range(1, len(self.train_losses)+1), self.train_losses, label="Training Loss")
        plt.plot(range(1, len(self.val_losses)+1), self.val_losses, label="Validation Loss")
        plt.xlabel("Epoch")
        plt.ylabel("Loss (MSE)")
        plt.title("Training vs Validation Loss")
        plt.legend()
        plt.grid(True)
        plt.ylim(0.4, 0.6)
        plt.tight_layout()
        plt.show()

EPOCHS = 4000
model.fit(train_dataset,
          epochs=EPOCHS,
          validation_data=validate_dataset,
          callbacks=[CustomCallback(validate_dataset, interval=10)])