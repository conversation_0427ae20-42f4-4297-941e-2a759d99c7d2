// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/pjrt/distributed/protocol.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fdistributed_2fprotocol_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fdistributed_2fprotocol_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fpjrt_2fdistributed_2fprotocol_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
namespace xla {
class DeviceAttributeProto;
struct DeviceAttributeProtoDefaultTypeInternal;
extern DeviceAttributeProtoDefaultTypeInternal _DeviceAttributeProto_default_instance_;
class DeviceProto;
struct DeviceProtoDefaultTypeInternal;
extern DeviceProtoDefaultTypeInternal _DeviceProto_default_instance_;
class DeviceProto_AttributesEntry_DoNotUse;
struct DeviceProto_AttributesEntry_DoNotUseDefaultTypeInternal;
extern DeviceProto_AttributesEntry_DoNotUseDefaultTypeInternal _DeviceProto_AttributesEntry_DoNotUse_default_instance_;
class GlobalTopologyProto;
struct GlobalTopologyProtoDefaultTypeInternal;
extern GlobalTopologyProtoDefaultTypeInternal _GlobalTopologyProto_default_instance_;
class IntValuesProto;
struct IntValuesProtoDefaultTypeInternal;
extern IntValuesProtoDefaultTypeInternal _IntValuesProto_default_instance_;
class LocalTopologyProto;
struct LocalTopologyProtoDefaultTypeInternal;
extern LocalTopologyProtoDefaultTypeInternal _LocalTopologyProto_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::DeviceAttributeProto* Arena::CreateMaybeMessage<::xla::DeviceAttributeProto>(Arena*);
template<> ::xla::DeviceProto* Arena::CreateMaybeMessage<::xla::DeviceProto>(Arena*);
template<> ::xla::DeviceProto_AttributesEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::DeviceProto_AttributesEntry_DoNotUse>(Arena*);
template<> ::xla::GlobalTopologyProto* Arena::CreateMaybeMessage<::xla::GlobalTopologyProto>(Arena*);
template<> ::xla::IntValuesProto* Arena::CreateMaybeMessage<::xla::IntValuesProto>(Arena*);
template<> ::xla::LocalTopologyProto* Arena::CreateMaybeMessage<::xla::LocalTopologyProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class IntValuesProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.IntValuesProto) */ {
 public:
  inline IntValuesProto() : IntValuesProto(nullptr) {}
  ~IntValuesProto() override;
  explicit PROTOBUF_CONSTEXPR IntValuesProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IntValuesProto(const IntValuesProto& from);
  IntValuesProto(IntValuesProto&& from) noexcept
    : IntValuesProto() {
    *this = ::std::move(from);
  }

  inline IntValuesProto& operator=(const IntValuesProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline IntValuesProto& operator=(IntValuesProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IntValuesProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const IntValuesProto* internal_default_instance() {
    return reinterpret_cast<const IntValuesProto*>(
               &_IntValuesProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(IntValuesProto& a, IntValuesProto& b) {
    a.Swap(&b);
  }
  inline void Swap(IntValuesProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IntValuesProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IntValuesProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IntValuesProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IntValuesProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const IntValuesProto& from) {
    IntValuesProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IntValuesProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.IntValuesProto";
  }
  protected:
  explicit IntValuesProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 1,
  };
  // repeated int64 values = 1;
  int values_size() const;
  private:
  int _internal_values_size() const;
  public:
  void clear_values();
  private:
  int64_t _internal_values(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_values() const;
  void _internal_add_values(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_values();
  public:
  int64_t values(int index) const;
  void set_values(int index, int64_t value);
  void add_values(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_values();

  // @@protoc_insertion_point(class_scope:xla.IntValuesProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > values_;
    mutable std::atomic<int> _values_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
};
// -------------------------------------------------------------------

class DeviceAttributeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeviceAttributeProto) */ {
 public:
  inline DeviceAttributeProto() : DeviceAttributeProto(nullptr) {}
  ~DeviceAttributeProto() override;
  explicit PROTOBUF_CONSTEXPR DeviceAttributeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceAttributeProto(const DeviceAttributeProto& from);
  DeviceAttributeProto(DeviceAttributeProto&& from) noexcept
    : DeviceAttributeProto() {
    *this = ::std::move(from);
  }

  inline DeviceAttributeProto& operator=(const DeviceAttributeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceAttributeProto& operator=(DeviceAttributeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceAttributeProto& default_instance() {
    return *internal_default_instance();
  }
  enum AttributeCase {
    kStringValue = 1,
    kBoolValue = 2,
    kIntValue = 3,
    kIntValues = 4,
    kFloatValue = 5,
    ATTRIBUTE_NOT_SET = 0,
  };

  static inline const DeviceAttributeProto* internal_default_instance() {
    return reinterpret_cast<const DeviceAttributeProto*>(
               &_DeviceAttributeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DeviceAttributeProto& a, DeviceAttributeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceAttributeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceAttributeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceAttributeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceAttributeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceAttributeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceAttributeProto& from) {
    DeviceAttributeProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAttributeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeviceAttributeProto";
  }
  protected:
  explicit DeviceAttributeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStringValueFieldNumber = 1,
    kBoolValueFieldNumber = 2,
    kIntValueFieldNumber = 3,
    kIntValuesFieldNumber = 4,
    kFloatValueFieldNumber = 5,
  };
  // string string_value = 1;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(const std::string& value);
  std::string* _internal_mutable_string_value();
  public:

  // bool bool_value = 2;
  bool has_bool_value() const;
  private:
  bool _internal_has_bool_value() const;
  public:
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);
  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);
  public:

  // int64 int_value = 3;
  bool has_int_value() const;
  private:
  bool _internal_has_int_value() const;
  public:
  void clear_int_value();
  int64_t int_value() const;
  void set_int_value(int64_t value);
  private:
  int64_t _internal_int_value() const;
  void _internal_set_int_value(int64_t value);
  public:

  // .xla.IntValuesProto int_values = 4;
  bool has_int_values() const;
  private:
  bool _internal_has_int_values() const;
  public:
  void clear_int_values();
  const ::xla::IntValuesProto& int_values() const;
  PROTOBUF_NODISCARD ::xla::IntValuesProto* release_int_values();
  ::xla::IntValuesProto* mutable_int_values();
  void set_allocated_int_values(::xla::IntValuesProto* int_values);
  private:
  const ::xla::IntValuesProto& _internal_int_values() const;
  ::xla::IntValuesProto* _internal_mutable_int_values();
  public:
  void unsafe_arena_set_allocated_int_values(
      ::xla::IntValuesProto* int_values);
  ::xla::IntValuesProto* unsafe_arena_release_int_values();

  // float float_value = 5;
  bool has_float_value() const;
  private:
  bool _internal_has_float_value() const;
  public:
  void clear_float_value();
  float float_value() const;
  void set_float_value(float value);
  private:
  float _internal_float_value() const;
  void _internal_set_float_value(float value);
  public:

  void clear_attribute();
  AttributeCase attribute_case() const;
  // @@protoc_insertion_point(class_scope:xla.DeviceAttributeProto)
 private:
  class _Internal;
  void set_has_string_value();
  void set_has_bool_value();
  void set_has_int_value();
  void set_has_int_values();
  void set_has_float_value();

  inline bool has_attribute() const;
  inline void clear_has_attribute();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union AttributeUnion {
      constexpr AttributeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
      bool bool_value_;
      int64_t int_value_;
      ::xla::IntValuesProto* int_values_;
      float float_value_;
    } attribute_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
};
// -------------------------------------------------------------------

class DeviceProto_AttributesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceProto_AttributesEntry_DoNotUse, 
    std::string, ::xla::DeviceAttributeProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceProto_AttributesEntry_DoNotUse, 
    std::string, ::xla::DeviceAttributeProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  DeviceProto_AttributesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR DeviceProto_AttributesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DeviceProto_AttributesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DeviceProto_AttributesEntry_DoNotUse& other);
  static const DeviceProto_AttributesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceProto_AttributesEntry_DoNotUse*>(&_DeviceProto_AttributesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.DeviceProto.AttributesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
};

// -------------------------------------------------------------------

class DeviceProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeviceProto) */ {
 public:
  inline DeviceProto() : DeviceProto(nullptr) {}
  ~DeviceProto() override;
  explicit PROTOBUF_CONSTEXPR DeviceProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceProto(const DeviceProto& from);
  DeviceProto(DeviceProto&& from) noexcept
    : DeviceProto() {
    *this = ::std::move(from);
  }

  inline DeviceProto& operator=(const DeviceProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceProto& operator=(DeviceProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceProto* internal_default_instance() {
    return reinterpret_cast<const DeviceProto*>(
               &_DeviceProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DeviceProto& a, DeviceProto& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceProto& from) {
    DeviceProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeviceProto";
  }
  protected:
  explicit DeviceProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAttributesFieldNumber = 11,
    kNameFieldNumber = 2,
    kVendorFieldNumber = 3,
    kComputeCapabilityFieldNumber = 6,
    kDeviceKindFieldNumber = 8,
    kToStringFieldNumber = 9,
    kDebugStringFieldNumber = 10,
    kLocalDeviceOrdinalFieldNumber = 1,
    kGlobalDeviceIdFieldNumber = 4,
    kSliceIndexFieldNumber = 5,
    kCoreCountFieldNumber = 7,
  };
  // map<string, .xla.DeviceAttributeProto> attributes = 11;
  int attributes_size() const;
  private:
  int _internal_attributes_size() const;
  public:
  void clear_attributes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >&
      _internal_attributes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >*
      _internal_mutable_attributes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >&
      attributes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >*
      mutable_attributes();

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string vendor = 3;
  void clear_vendor();
  const std::string& vendor() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_vendor(ArgT0&& arg0, ArgT... args);
  std::string* mutable_vendor();
  PROTOBUF_NODISCARD std::string* release_vendor();
  void set_allocated_vendor(std::string* vendor);
  private:
  const std::string& _internal_vendor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_vendor(const std::string& value);
  std::string* _internal_mutable_vendor();
  public:

  // string compute_capability = 6;
  void clear_compute_capability();
  const std::string& compute_capability() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_compute_capability(ArgT0&& arg0, ArgT... args);
  std::string* mutable_compute_capability();
  PROTOBUF_NODISCARD std::string* release_compute_capability();
  void set_allocated_compute_capability(std::string* compute_capability);
  private:
  const std::string& _internal_compute_capability() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_compute_capability(const std::string& value);
  std::string* _internal_mutable_compute_capability();
  public:

  // string device_kind = 8;
  void clear_device_kind();
  const std::string& device_kind() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_kind(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_kind();
  PROTOBUF_NODISCARD std::string* release_device_kind();
  void set_allocated_device_kind(std::string* device_kind);
  private:
  const std::string& _internal_device_kind() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_kind(const std::string& value);
  std::string* _internal_mutable_device_kind();
  public:

  // string to_string = 9;
  void clear_to_string();
  const std::string& to_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_to_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_to_string();
  PROTOBUF_NODISCARD std::string* release_to_string();
  void set_allocated_to_string(std::string* to_string);
  private:
  const std::string& _internal_to_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_to_string(const std::string& value);
  std::string* _internal_mutable_to_string();
  public:

  // string debug_string = 10;
  void clear_debug_string();
  const std::string& debug_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_debug_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_debug_string();
  PROTOBUF_NODISCARD std::string* release_debug_string();
  void set_allocated_debug_string(std::string* debug_string);
  private:
  const std::string& _internal_debug_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_debug_string(const std::string& value);
  std::string* _internal_mutable_debug_string();
  public:

  // int32 local_device_ordinal = 1;
  void clear_local_device_ordinal();
  int32_t local_device_ordinal() const;
  void set_local_device_ordinal(int32_t value);
  private:
  int32_t _internal_local_device_ordinal() const;
  void _internal_set_local_device_ordinal(int32_t value);
  public:

  // int32 global_device_id = 4;
  void clear_global_device_id();
  int32_t global_device_id() const;
  void set_global_device_id(int32_t value);
  private:
  int32_t _internal_global_device_id() const;
  void _internal_set_global_device_id(int32_t value);
  public:

  // int32 slice_index = 5;
  void clear_slice_index();
  int32_t slice_index() const;
  void set_slice_index(int32_t value);
  private:
  int32_t _internal_slice_index() const;
  void _internal_set_slice_index(int32_t value);
  public:

  // int32 core_count = 7;
  void clear_core_count();
  int32_t core_count() const;
  void set_core_count(int32_t value);
  private:
  int32_t _internal_core_count() const;
  void _internal_set_core_count(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.DeviceProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        DeviceProto_AttributesEntry_DoNotUse,
        std::string, ::xla::DeviceAttributeProto,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attributes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vendor_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr compute_capability_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_kind_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr to_string_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr debug_string_;
    int32_t local_device_ordinal_;
    int32_t global_device_id_;
    int32_t slice_index_;
    int32_t core_count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
};
// -------------------------------------------------------------------

class LocalTopologyProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LocalTopologyProto) */ {
 public:
  inline LocalTopologyProto() : LocalTopologyProto(nullptr) {}
  ~LocalTopologyProto() override;
  explicit PROTOBUF_CONSTEXPR LocalTopologyProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LocalTopologyProto(const LocalTopologyProto& from);
  LocalTopologyProto(LocalTopologyProto&& from) noexcept
    : LocalTopologyProto() {
    *this = ::std::move(from);
  }

  inline LocalTopologyProto& operator=(const LocalTopologyProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocalTopologyProto& operator=(LocalTopologyProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LocalTopologyProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const LocalTopologyProto* internal_default_instance() {
    return reinterpret_cast<const LocalTopologyProto*>(
               &_LocalTopologyProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(LocalTopologyProto& a, LocalTopologyProto& b) {
    a.Swap(&b);
  }
  inline void Swap(LocalTopologyProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocalTopologyProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LocalTopologyProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LocalTopologyProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LocalTopologyProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LocalTopologyProto& from) {
    LocalTopologyProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocalTopologyProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LocalTopologyProto";
  }
  protected:
  explicit LocalTopologyProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDevicesFieldNumber = 3,
    kBootIdFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // repeated .xla.DeviceProto devices = 3;
  int devices_size() const;
  private:
  int _internal_devices_size() const;
  public:
  void clear_devices();
  ::xla::DeviceProto* mutable_devices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceProto >*
      mutable_devices();
  private:
  const ::xla::DeviceProto& _internal_devices(int index) const;
  ::xla::DeviceProto* _internal_add_devices();
  public:
  const ::xla::DeviceProto& devices(int index) const;
  ::xla::DeviceProto* add_devices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceProto >&
      devices() const;

  // string boot_id = 2;
  void clear_boot_id();
  const std::string& boot_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_boot_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_boot_id();
  PROTOBUF_NODISCARD std::string* release_boot_id();
  void set_allocated_boot_id(std::string* boot_id);
  private:
  const std::string& _internal_boot_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_boot_id(const std::string& value);
  std::string* _internal_mutable_boot_id();
  public:

  // int32 node_id = 1;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.LocalTopologyProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceProto > devices_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr boot_id_;
    int32_t node_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
};
// -------------------------------------------------------------------

class GlobalTopologyProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GlobalTopologyProto) */ {
 public:
  inline GlobalTopologyProto() : GlobalTopologyProto(nullptr) {}
  ~GlobalTopologyProto() override;
  explicit PROTOBUF_CONSTEXPR GlobalTopologyProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GlobalTopologyProto(const GlobalTopologyProto& from);
  GlobalTopologyProto(GlobalTopologyProto&& from) noexcept
    : GlobalTopologyProto() {
    *this = ::std::move(from);
  }

  inline GlobalTopologyProto& operator=(const GlobalTopologyProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GlobalTopologyProto& operator=(GlobalTopologyProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GlobalTopologyProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const GlobalTopologyProto* internal_default_instance() {
    return reinterpret_cast<const GlobalTopologyProto*>(
               &_GlobalTopologyProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GlobalTopologyProto& a, GlobalTopologyProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GlobalTopologyProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GlobalTopologyProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GlobalTopologyProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GlobalTopologyProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GlobalTopologyProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GlobalTopologyProto& from) {
    GlobalTopologyProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GlobalTopologyProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GlobalTopologyProto";
  }
  protected:
  explicit GlobalTopologyProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
  };
  // repeated .xla.LocalTopologyProto nodes = 1;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;
  public:
  void clear_nodes();
  ::xla::LocalTopologyProto* mutable_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LocalTopologyProto >*
      mutable_nodes();
  private:
  const ::xla::LocalTopologyProto& _internal_nodes(int index) const;
  ::xla::LocalTopologyProto* _internal_add_nodes();
  public:
  const ::xla::LocalTopologyProto& nodes(int index) const;
  ::xla::LocalTopologyProto* add_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LocalTopologyProto >&
      nodes() const;

  // @@protoc_insertion_point(class_scope:xla.GlobalTopologyProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LocalTopologyProto > nodes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fdistributed_2fprotocol_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// IntValuesProto

// repeated int64 values = 1;
inline int IntValuesProto::_internal_values_size() const {
  return _impl_.values_.size();
}
inline int IntValuesProto::values_size() const {
  return _internal_values_size();
}
inline void IntValuesProto::clear_values() {
  _impl_.values_.Clear();
}
inline int64_t IntValuesProto::_internal_values(int index) const {
  return _impl_.values_.Get(index);
}
inline int64_t IntValuesProto::values(int index) const {
  // @@protoc_insertion_point(field_get:xla.IntValuesProto.values)
  return _internal_values(index);
}
inline void IntValuesProto::set_values(int index, int64_t value) {
  _impl_.values_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.IntValuesProto.values)
}
inline void IntValuesProto::_internal_add_values(int64_t value) {
  _impl_.values_.Add(value);
}
inline void IntValuesProto::add_values(int64_t value) {
  _internal_add_values(value);
  // @@protoc_insertion_point(field_add:xla.IntValuesProto.values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
IntValuesProto::_internal_values() const {
  return _impl_.values_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
IntValuesProto::values() const {
  // @@protoc_insertion_point(field_list:xla.IntValuesProto.values)
  return _internal_values();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
IntValuesProto::_internal_mutable_values() {
  return &_impl_.values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
IntValuesProto::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:xla.IntValuesProto.values)
  return _internal_mutable_values();
}

// -------------------------------------------------------------------

// DeviceAttributeProto

// string string_value = 1;
inline bool DeviceAttributeProto::_internal_has_string_value() const {
  return attribute_case() == kStringValue;
}
inline bool DeviceAttributeProto::has_string_value() const {
  return _internal_has_string_value();
}
inline void DeviceAttributeProto::set_has_string_value() {
  _impl_._oneof_case_[0] = kStringValue;
}
inline void DeviceAttributeProto::clear_string_value() {
  if (_internal_has_string_value()) {
    _impl_.attribute_.string_value_.Destroy();
    clear_has_attribute();
  }
}
inline const std::string& DeviceAttributeProto::string_value() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAttributeProto.string_value)
  return _internal_string_value();
}
template <typename ArgT0, typename... ArgT>
inline void DeviceAttributeProto::set_string_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_string_value()) {
    clear_attribute();
    set_has_string_value();
    _impl_.attribute_.string_value_.InitDefault();
  }
  _impl_.attribute_.string_value_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceAttributeProto.string_value)
}
inline std::string* DeviceAttributeProto::mutable_string_value() {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:xla.DeviceAttributeProto.string_value)
  return _s;
}
inline const std::string& DeviceAttributeProto::_internal_string_value() const {
  if (_internal_has_string_value()) {
    return _impl_.attribute_.string_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void DeviceAttributeProto::_internal_set_string_value(const std::string& value) {
  if (!_internal_has_string_value()) {
    clear_attribute();
    set_has_string_value();
    _impl_.attribute_.string_value_.InitDefault();
  }
  _impl_.attribute_.string_value_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceAttributeProto::_internal_mutable_string_value() {
  if (!_internal_has_string_value()) {
    clear_attribute();
    set_has_string_value();
    _impl_.attribute_.string_value_.InitDefault();
  }
  return _impl_.attribute_.string_value_.Mutable(      GetArenaForAllocation());
}
inline std::string* DeviceAttributeProto::release_string_value() {
  // @@protoc_insertion_point(field_release:xla.DeviceAttributeProto.string_value)
  if (_internal_has_string_value()) {
    clear_has_attribute();
    return _impl_.attribute_.string_value_.Release();
  } else {
    return nullptr;
  }
}
inline void DeviceAttributeProto::set_allocated_string_value(std::string* string_value) {
  if (has_attribute()) {
    clear_attribute();
  }
  if (string_value != nullptr) {
    set_has_string_value();
    _impl_.attribute_.string_value_.InitAllocated(string_value, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceAttributeProto.string_value)
}

// bool bool_value = 2;
inline bool DeviceAttributeProto::_internal_has_bool_value() const {
  return attribute_case() == kBoolValue;
}
inline bool DeviceAttributeProto::has_bool_value() const {
  return _internal_has_bool_value();
}
inline void DeviceAttributeProto::set_has_bool_value() {
  _impl_._oneof_case_[0] = kBoolValue;
}
inline void DeviceAttributeProto::clear_bool_value() {
  if (_internal_has_bool_value()) {
    _impl_.attribute_.bool_value_ = false;
    clear_has_attribute();
  }
}
inline bool DeviceAttributeProto::_internal_bool_value() const {
  if (_internal_has_bool_value()) {
    return _impl_.attribute_.bool_value_;
  }
  return false;
}
inline void DeviceAttributeProto::_internal_set_bool_value(bool value) {
  if (!_internal_has_bool_value()) {
    clear_attribute();
    set_has_bool_value();
  }
  _impl_.attribute_.bool_value_ = value;
}
inline bool DeviceAttributeProto::bool_value() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAttributeProto.bool_value)
  return _internal_bool_value();
}
inline void DeviceAttributeProto::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  // @@protoc_insertion_point(field_set:xla.DeviceAttributeProto.bool_value)
}

// int64 int_value = 3;
inline bool DeviceAttributeProto::_internal_has_int_value() const {
  return attribute_case() == kIntValue;
}
inline bool DeviceAttributeProto::has_int_value() const {
  return _internal_has_int_value();
}
inline void DeviceAttributeProto::set_has_int_value() {
  _impl_._oneof_case_[0] = kIntValue;
}
inline void DeviceAttributeProto::clear_int_value() {
  if (_internal_has_int_value()) {
    _impl_.attribute_.int_value_ = int64_t{0};
    clear_has_attribute();
  }
}
inline int64_t DeviceAttributeProto::_internal_int_value() const {
  if (_internal_has_int_value()) {
    return _impl_.attribute_.int_value_;
  }
  return int64_t{0};
}
inline void DeviceAttributeProto::_internal_set_int_value(int64_t value) {
  if (!_internal_has_int_value()) {
    clear_attribute();
    set_has_int_value();
  }
  _impl_.attribute_.int_value_ = value;
}
inline int64_t DeviceAttributeProto::int_value() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAttributeProto.int_value)
  return _internal_int_value();
}
inline void DeviceAttributeProto::set_int_value(int64_t value) {
  _internal_set_int_value(value);
  // @@protoc_insertion_point(field_set:xla.DeviceAttributeProto.int_value)
}

// .xla.IntValuesProto int_values = 4;
inline bool DeviceAttributeProto::_internal_has_int_values() const {
  return attribute_case() == kIntValues;
}
inline bool DeviceAttributeProto::has_int_values() const {
  return _internal_has_int_values();
}
inline void DeviceAttributeProto::set_has_int_values() {
  _impl_._oneof_case_[0] = kIntValues;
}
inline void DeviceAttributeProto::clear_int_values() {
  if (_internal_has_int_values()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.attribute_.int_values_;
    }
    clear_has_attribute();
  }
}
inline ::xla::IntValuesProto* DeviceAttributeProto::release_int_values() {
  // @@protoc_insertion_point(field_release:xla.DeviceAttributeProto.int_values)
  if (_internal_has_int_values()) {
    clear_has_attribute();
    ::xla::IntValuesProto* temp = _impl_.attribute_.int_values_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.attribute_.int_values_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::IntValuesProto& DeviceAttributeProto::_internal_int_values() const {
  return _internal_has_int_values()
      ? *_impl_.attribute_.int_values_
      : reinterpret_cast< ::xla::IntValuesProto&>(::xla::_IntValuesProto_default_instance_);
}
inline const ::xla::IntValuesProto& DeviceAttributeProto::int_values() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAttributeProto.int_values)
  return _internal_int_values();
}
inline ::xla::IntValuesProto* DeviceAttributeProto::unsafe_arena_release_int_values() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.DeviceAttributeProto.int_values)
  if (_internal_has_int_values()) {
    clear_has_attribute();
    ::xla::IntValuesProto* temp = _impl_.attribute_.int_values_;
    _impl_.attribute_.int_values_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DeviceAttributeProto::unsafe_arena_set_allocated_int_values(::xla::IntValuesProto* int_values) {
  clear_attribute();
  if (int_values) {
    set_has_int_values();
    _impl_.attribute_.int_values_ = int_values;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.DeviceAttributeProto.int_values)
}
inline ::xla::IntValuesProto* DeviceAttributeProto::_internal_mutable_int_values() {
  if (!_internal_has_int_values()) {
    clear_attribute();
    set_has_int_values();
    _impl_.attribute_.int_values_ = CreateMaybeMessage< ::xla::IntValuesProto >(GetArenaForAllocation());
  }
  return _impl_.attribute_.int_values_;
}
inline ::xla::IntValuesProto* DeviceAttributeProto::mutable_int_values() {
  ::xla::IntValuesProto* _msg = _internal_mutable_int_values();
  // @@protoc_insertion_point(field_mutable:xla.DeviceAttributeProto.int_values)
  return _msg;
}

// float float_value = 5;
inline bool DeviceAttributeProto::_internal_has_float_value() const {
  return attribute_case() == kFloatValue;
}
inline bool DeviceAttributeProto::has_float_value() const {
  return _internal_has_float_value();
}
inline void DeviceAttributeProto::set_has_float_value() {
  _impl_._oneof_case_[0] = kFloatValue;
}
inline void DeviceAttributeProto::clear_float_value() {
  if (_internal_has_float_value()) {
    _impl_.attribute_.float_value_ = 0;
    clear_has_attribute();
  }
}
inline float DeviceAttributeProto::_internal_float_value() const {
  if (_internal_has_float_value()) {
    return _impl_.attribute_.float_value_;
  }
  return 0;
}
inline void DeviceAttributeProto::_internal_set_float_value(float value) {
  if (!_internal_has_float_value()) {
    clear_attribute();
    set_has_float_value();
  }
  _impl_.attribute_.float_value_ = value;
}
inline float DeviceAttributeProto::float_value() const {
  // @@protoc_insertion_point(field_get:xla.DeviceAttributeProto.float_value)
  return _internal_float_value();
}
inline void DeviceAttributeProto::set_float_value(float value) {
  _internal_set_float_value(value);
  // @@protoc_insertion_point(field_set:xla.DeviceAttributeProto.float_value)
}

inline bool DeviceAttributeProto::has_attribute() const {
  return attribute_case() != ATTRIBUTE_NOT_SET;
}
inline void DeviceAttributeProto::clear_has_attribute() {
  _impl_._oneof_case_[0] = ATTRIBUTE_NOT_SET;
}
inline DeviceAttributeProto::AttributeCase DeviceAttributeProto::attribute_case() const {
  return DeviceAttributeProto::AttributeCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DeviceProto

// int32 local_device_ordinal = 1;
inline void DeviceProto::clear_local_device_ordinal() {
  _impl_.local_device_ordinal_ = 0;
}
inline int32_t DeviceProto::_internal_local_device_ordinal() const {
  return _impl_.local_device_ordinal_;
}
inline int32_t DeviceProto::local_device_ordinal() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.local_device_ordinal)
  return _internal_local_device_ordinal();
}
inline void DeviceProto::_internal_set_local_device_ordinal(int32_t value) {
  
  _impl_.local_device_ordinal_ = value;
}
inline void DeviceProto::set_local_device_ordinal(int32_t value) {
  _internal_set_local_device_ordinal(value);
  // @@protoc_insertion_point(field_set:xla.DeviceProto.local_device_ordinal)
}

// string name = 2;
inline void DeviceProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& DeviceProto::name() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceProto.name)
}
inline std::string* DeviceProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:xla.DeviceProto.name)
  return _s;
}
inline const std::string& DeviceProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void DeviceProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProto::release_name() {
  // @@protoc_insertion_point(field_release:xla.DeviceProto.name)
  return _impl_.name_.Release();
}
inline void DeviceProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceProto.name)
}

// string vendor = 3;
inline void DeviceProto::clear_vendor() {
  _impl_.vendor_.ClearToEmpty();
}
inline const std::string& DeviceProto::vendor() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.vendor)
  return _internal_vendor();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProto::set_vendor(ArgT0&& arg0, ArgT... args) {
 
 _impl_.vendor_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceProto.vendor)
}
inline std::string* DeviceProto::mutable_vendor() {
  std::string* _s = _internal_mutable_vendor();
  // @@protoc_insertion_point(field_mutable:xla.DeviceProto.vendor)
  return _s;
}
inline const std::string& DeviceProto::_internal_vendor() const {
  return _impl_.vendor_.Get();
}
inline void DeviceProto::_internal_set_vendor(const std::string& value) {
  
  _impl_.vendor_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProto::_internal_mutable_vendor() {
  
  return _impl_.vendor_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProto::release_vendor() {
  // @@protoc_insertion_point(field_release:xla.DeviceProto.vendor)
  return _impl_.vendor_.Release();
}
inline void DeviceProto::set_allocated_vendor(std::string* vendor) {
  if (vendor != nullptr) {
    
  } else {
    
  }
  _impl_.vendor_.SetAllocated(vendor, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.vendor_.IsDefault()) {
    _impl_.vendor_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceProto.vendor)
}

// int32 global_device_id = 4;
inline void DeviceProto::clear_global_device_id() {
  _impl_.global_device_id_ = 0;
}
inline int32_t DeviceProto::_internal_global_device_id() const {
  return _impl_.global_device_id_;
}
inline int32_t DeviceProto::global_device_id() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.global_device_id)
  return _internal_global_device_id();
}
inline void DeviceProto::_internal_set_global_device_id(int32_t value) {
  
  _impl_.global_device_id_ = value;
}
inline void DeviceProto::set_global_device_id(int32_t value) {
  _internal_set_global_device_id(value);
  // @@protoc_insertion_point(field_set:xla.DeviceProto.global_device_id)
}

// int32 slice_index = 5;
inline void DeviceProto::clear_slice_index() {
  _impl_.slice_index_ = 0;
}
inline int32_t DeviceProto::_internal_slice_index() const {
  return _impl_.slice_index_;
}
inline int32_t DeviceProto::slice_index() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.slice_index)
  return _internal_slice_index();
}
inline void DeviceProto::_internal_set_slice_index(int32_t value) {
  
  _impl_.slice_index_ = value;
}
inline void DeviceProto::set_slice_index(int32_t value) {
  _internal_set_slice_index(value);
  // @@protoc_insertion_point(field_set:xla.DeviceProto.slice_index)
}

// string compute_capability = 6;
inline void DeviceProto::clear_compute_capability() {
  _impl_.compute_capability_.ClearToEmpty();
}
inline const std::string& DeviceProto::compute_capability() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.compute_capability)
  return _internal_compute_capability();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProto::set_compute_capability(ArgT0&& arg0, ArgT... args) {
 
 _impl_.compute_capability_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceProto.compute_capability)
}
inline std::string* DeviceProto::mutable_compute_capability() {
  std::string* _s = _internal_mutable_compute_capability();
  // @@protoc_insertion_point(field_mutable:xla.DeviceProto.compute_capability)
  return _s;
}
inline const std::string& DeviceProto::_internal_compute_capability() const {
  return _impl_.compute_capability_.Get();
}
inline void DeviceProto::_internal_set_compute_capability(const std::string& value) {
  
  _impl_.compute_capability_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProto::_internal_mutable_compute_capability() {
  
  return _impl_.compute_capability_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProto::release_compute_capability() {
  // @@protoc_insertion_point(field_release:xla.DeviceProto.compute_capability)
  return _impl_.compute_capability_.Release();
}
inline void DeviceProto::set_allocated_compute_capability(std::string* compute_capability) {
  if (compute_capability != nullptr) {
    
  } else {
    
  }
  _impl_.compute_capability_.SetAllocated(compute_capability, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.compute_capability_.IsDefault()) {
    _impl_.compute_capability_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceProto.compute_capability)
}

// int32 core_count = 7;
inline void DeviceProto::clear_core_count() {
  _impl_.core_count_ = 0;
}
inline int32_t DeviceProto::_internal_core_count() const {
  return _impl_.core_count_;
}
inline int32_t DeviceProto::core_count() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.core_count)
  return _internal_core_count();
}
inline void DeviceProto::_internal_set_core_count(int32_t value) {
  
  _impl_.core_count_ = value;
}
inline void DeviceProto::set_core_count(int32_t value) {
  _internal_set_core_count(value);
  // @@protoc_insertion_point(field_set:xla.DeviceProto.core_count)
}

// string device_kind = 8;
inline void DeviceProto::clear_device_kind() {
  _impl_.device_kind_.ClearToEmpty();
}
inline const std::string& DeviceProto::device_kind() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.device_kind)
  return _internal_device_kind();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProto::set_device_kind(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_kind_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceProto.device_kind)
}
inline std::string* DeviceProto::mutable_device_kind() {
  std::string* _s = _internal_mutable_device_kind();
  // @@protoc_insertion_point(field_mutable:xla.DeviceProto.device_kind)
  return _s;
}
inline const std::string& DeviceProto::_internal_device_kind() const {
  return _impl_.device_kind_.Get();
}
inline void DeviceProto::_internal_set_device_kind(const std::string& value) {
  
  _impl_.device_kind_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProto::_internal_mutable_device_kind() {
  
  return _impl_.device_kind_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProto::release_device_kind() {
  // @@protoc_insertion_point(field_release:xla.DeviceProto.device_kind)
  return _impl_.device_kind_.Release();
}
inline void DeviceProto::set_allocated_device_kind(std::string* device_kind) {
  if (device_kind != nullptr) {
    
  } else {
    
  }
  _impl_.device_kind_.SetAllocated(device_kind, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_kind_.IsDefault()) {
    _impl_.device_kind_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceProto.device_kind)
}

// string to_string = 9;
inline void DeviceProto::clear_to_string() {
  _impl_.to_string_.ClearToEmpty();
}
inline const std::string& DeviceProto::to_string() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.to_string)
  return _internal_to_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProto::set_to_string(ArgT0&& arg0, ArgT... args) {
 
 _impl_.to_string_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceProto.to_string)
}
inline std::string* DeviceProto::mutable_to_string() {
  std::string* _s = _internal_mutable_to_string();
  // @@protoc_insertion_point(field_mutable:xla.DeviceProto.to_string)
  return _s;
}
inline const std::string& DeviceProto::_internal_to_string() const {
  return _impl_.to_string_.Get();
}
inline void DeviceProto::_internal_set_to_string(const std::string& value) {
  
  _impl_.to_string_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProto::_internal_mutable_to_string() {
  
  return _impl_.to_string_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProto::release_to_string() {
  // @@protoc_insertion_point(field_release:xla.DeviceProto.to_string)
  return _impl_.to_string_.Release();
}
inline void DeviceProto::set_allocated_to_string(std::string* to_string) {
  if (to_string != nullptr) {
    
  } else {
    
  }
  _impl_.to_string_.SetAllocated(to_string, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.to_string_.IsDefault()) {
    _impl_.to_string_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceProto.to_string)
}

// string debug_string = 10;
inline void DeviceProto::clear_debug_string() {
  _impl_.debug_string_.ClearToEmpty();
}
inline const std::string& DeviceProto::debug_string() const {
  // @@protoc_insertion_point(field_get:xla.DeviceProto.debug_string)
  return _internal_debug_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProto::set_debug_string(ArgT0&& arg0, ArgT... args) {
 
 _impl_.debug_string_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.DeviceProto.debug_string)
}
inline std::string* DeviceProto::mutable_debug_string() {
  std::string* _s = _internal_mutable_debug_string();
  // @@protoc_insertion_point(field_mutable:xla.DeviceProto.debug_string)
  return _s;
}
inline const std::string& DeviceProto::_internal_debug_string() const {
  return _impl_.debug_string_.Get();
}
inline void DeviceProto::_internal_set_debug_string(const std::string& value) {
  
  _impl_.debug_string_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProto::_internal_mutable_debug_string() {
  
  return _impl_.debug_string_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProto::release_debug_string() {
  // @@protoc_insertion_point(field_release:xla.DeviceProto.debug_string)
  return _impl_.debug_string_.Release();
}
inline void DeviceProto::set_allocated_debug_string(std::string* debug_string) {
  if (debug_string != nullptr) {
    
  } else {
    
  }
  _impl_.debug_string_.SetAllocated(debug_string, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.debug_string_.IsDefault()) {
    _impl_.debug_string_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.DeviceProto.debug_string)
}

// map<string, .xla.DeviceAttributeProto> attributes = 11;
inline int DeviceProto::_internal_attributes_size() const {
  return _impl_.attributes_.size();
}
inline int DeviceProto::attributes_size() const {
  return _internal_attributes_size();
}
inline void DeviceProto::clear_attributes() {
  _impl_.attributes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >&
DeviceProto::_internal_attributes() const {
  return _impl_.attributes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >&
DeviceProto::attributes() const {
  // @@protoc_insertion_point(field_map:xla.DeviceProto.attributes)
  return _internal_attributes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >*
DeviceProto::_internal_mutable_attributes() {
  return _impl_.attributes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::DeviceAttributeProto >*
DeviceProto::mutable_attributes() {
  // @@protoc_insertion_point(field_mutable_map:xla.DeviceProto.attributes)
  return _internal_mutable_attributes();
}

// -------------------------------------------------------------------

// LocalTopologyProto

// int32 node_id = 1;
inline void LocalTopologyProto::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t LocalTopologyProto::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t LocalTopologyProto::node_id() const {
  // @@protoc_insertion_point(field_get:xla.LocalTopologyProto.node_id)
  return _internal_node_id();
}
inline void LocalTopologyProto::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void LocalTopologyProto::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:xla.LocalTopologyProto.node_id)
}

// string boot_id = 2;
inline void LocalTopologyProto::clear_boot_id() {
  _impl_.boot_id_.ClearToEmpty();
}
inline const std::string& LocalTopologyProto::boot_id() const {
  // @@protoc_insertion_point(field_get:xla.LocalTopologyProto.boot_id)
  return _internal_boot_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LocalTopologyProto::set_boot_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.boot_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.LocalTopologyProto.boot_id)
}
inline std::string* LocalTopologyProto::mutable_boot_id() {
  std::string* _s = _internal_mutable_boot_id();
  // @@protoc_insertion_point(field_mutable:xla.LocalTopologyProto.boot_id)
  return _s;
}
inline const std::string& LocalTopologyProto::_internal_boot_id() const {
  return _impl_.boot_id_.Get();
}
inline void LocalTopologyProto::_internal_set_boot_id(const std::string& value) {
  
  _impl_.boot_id_.Set(value, GetArenaForAllocation());
}
inline std::string* LocalTopologyProto::_internal_mutable_boot_id() {
  
  return _impl_.boot_id_.Mutable(GetArenaForAllocation());
}
inline std::string* LocalTopologyProto::release_boot_id() {
  // @@protoc_insertion_point(field_release:xla.LocalTopologyProto.boot_id)
  return _impl_.boot_id_.Release();
}
inline void LocalTopologyProto::set_allocated_boot_id(std::string* boot_id) {
  if (boot_id != nullptr) {
    
  } else {
    
  }
  _impl_.boot_id_.SetAllocated(boot_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.boot_id_.IsDefault()) {
    _impl_.boot_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.LocalTopologyProto.boot_id)
}

// repeated .xla.DeviceProto devices = 3;
inline int LocalTopologyProto::_internal_devices_size() const {
  return _impl_.devices_.size();
}
inline int LocalTopologyProto::devices_size() const {
  return _internal_devices_size();
}
inline void LocalTopologyProto::clear_devices() {
  _impl_.devices_.Clear();
}
inline ::xla::DeviceProto* LocalTopologyProto::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:xla.LocalTopologyProto.devices)
  return _impl_.devices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceProto >*
LocalTopologyProto::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:xla.LocalTopologyProto.devices)
  return &_impl_.devices_;
}
inline const ::xla::DeviceProto& LocalTopologyProto::_internal_devices(int index) const {
  return _impl_.devices_.Get(index);
}
inline const ::xla::DeviceProto& LocalTopologyProto::devices(int index) const {
  // @@protoc_insertion_point(field_get:xla.LocalTopologyProto.devices)
  return _internal_devices(index);
}
inline ::xla::DeviceProto* LocalTopologyProto::_internal_add_devices() {
  return _impl_.devices_.Add();
}
inline ::xla::DeviceProto* LocalTopologyProto::add_devices() {
  ::xla::DeviceProto* _add = _internal_add_devices();
  // @@protoc_insertion_point(field_add:xla.LocalTopologyProto.devices)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceProto >&
LocalTopologyProto::devices() const {
  // @@protoc_insertion_point(field_list:xla.LocalTopologyProto.devices)
  return _impl_.devices_;
}

// -------------------------------------------------------------------

// GlobalTopologyProto

// repeated .xla.LocalTopologyProto nodes = 1;
inline int GlobalTopologyProto::_internal_nodes_size() const {
  return _impl_.nodes_.size();
}
inline int GlobalTopologyProto::nodes_size() const {
  return _internal_nodes_size();
}
inline void GlobalTopologyProto::clear_nodes() {
  _impl_.nodes_.Clear();
}
inline ::xla::LocalTopologyProto* GlobalTopologyProto::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:xla.GlobalTopologyProto.nodes)
  return _impl_.nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LocalTopologyProto >*
GlobalTopologyProto::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:xla.GlobalTopologyProto.nodes)
  return &_impl_.nodes_;
}
inline const ::xla::LocalTopologyProto& GlobalTopologyProto::_internal_nodes(int index) const {
  return _impl_.nodes_.Get(index);
}
inline const ::xla::LocalTopologyProto& GlobalTopologyProto::nodes(int index) const {
  // @@protoc_insertion_point(field_get:xla.GlobalTopologyProto.nodes)
  return _internal_nodes(index);
}
inline ::xla::LocalTopologyProto* GlobalTopologyProto::_internal_add_nodes() {
  return _impl_.nodes_.Add();
}
inline ::xla::LocalTopologyProto* GlobalTopologyProto::add_nodes() {
  ::xla::LocalTopologyProto* _add = _internal_add_nodes();
  // @@protoc_insertion_point(field_add:xla.GlobalTopologyProto.nodes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::LocalTopologyProto >&
GlobalTopologyProto::nodes() const {
  // @@protoc_insertion_point(field_list:xla.GlobalTopologyProto.nodes)
  return _impl_.nodes_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fdistributed_2fprotocol_2eproto
