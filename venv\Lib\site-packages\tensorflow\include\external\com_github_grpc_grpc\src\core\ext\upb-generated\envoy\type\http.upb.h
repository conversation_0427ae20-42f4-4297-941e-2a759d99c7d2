/* This file was generated by upbc (the upb compiler) from the input
 * file:
 *
 *     envoy/type/http.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#ifndef ENVOY_TYPE_HTTP_PROTO_UPB_H_
#define ENVOY_TYPE_HTTP_PROTO_UPB_H_

#include "upb/generated_util.h"
#include "upb/msg.h"
#include "upb/decode.h"
#include "upb/encode.h"

#include "upb/port_def.inc"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
  envoy_type_HTTP1 = 0,
  envoy_type_HTTP2 = 1,
  envoy_type_HTTP3 = 2
} envoy_type_CodecClientType;


#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port_undef.inc"

#endif  /* ENVOY_TYPE_HTTP_PROTO_UPB_H_ */
