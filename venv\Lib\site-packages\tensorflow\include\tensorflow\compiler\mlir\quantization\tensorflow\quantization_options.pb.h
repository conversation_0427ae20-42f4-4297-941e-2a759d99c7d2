// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/quantization/tensorflow/quantization_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/mlir/quantization/stablehlo/quantization_config.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
namespace tensorflow {
namespace quantization {
class QuantizationComponentSpec;
struct QuantizationComponentSpecDefaultTypeInternal;
extern QuantizationComponentSpecDefaultTypeInternal _QuantizationComponentSpec_default_instance_;
class QuantizationMethod;
struct QuantizationMethodDefaultTypeInternal;
extern QuantizationMethodDefaultTypeInternal _QuantizationMethod_default_instance_;
class QuantizationOptions;
struct QuantizationOptionsDefaultTypeInternal;
extern QuantizationOptionsDefaultTypeInternal _QuantizationOptions_default_instance_;
class QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse;
struct QuantizationOptions_RepresentativeDatasetsEntry_DoNotUseDefaultTypeInternal;
extern QuantizationOptions_RepresentativeDatasetsEntry_DoNotUseDefaultTypeInternal _QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse_default_instance_;
class RepresentativeDataSample;
struct RepresentativeDataSampleDefaultTypeInternal;
extern RepresentativeDataSampleDefaultTypeInternal _RepresentativeDataSample_default_instance_;
class RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse;
struct RepresentativeDataSample_TensorProtoInputsEntry_DoNotUseDefaultTypeInternal;
extern RepresentativeDataSample_TensorProtoInputsEntry_DoNotUseDefaultTypeInternal _RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse_default_instance_;
class RepresentativeDatasetFile;
struct RepresentativeDatasetFileDefaultTypeInternal;
extern RepresentativeDatasetFileDefaultTypeInternal _RepresentativeDatasetFile_default_instance_;
class UnitWiseQuantizationSpec;
struct UnitWiseQuantizationSpecDefaultTypeInternal;
extern UnitWiseQuantizationSpecDefaultTypeInternal _UnitWiseQuantizationSpec_default_instance_;
class UnitWiseQuantizationSpec_QuantizationUnit;
struct UnitWiseQuantizationSpec_QuantizationUnitDefaultTypeInternal;
extern UnitWiseQuantizationSpec_QuantizationUnitDefaultTypeInternal _UnitWiseQuantizationSpec_QuantizationUnit_default_instance_;
}  // namespace quantization
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::quantization::QuantizationComponentSpec* Arena::CreateMaybeMessage<::tensorflow::quantization::QuantizationComponentSpec>(Arena*);
template<> ::tensorflow::quantization::QuantizationMethod* Arena::CreateMaybeMessage<::tensorflow::quantization::QuantizationMethod>(Arena*);
template<> ::tensorflow::quantization::QuantizationOptions* Arena::CreateMaybeMessage<::tensorflow::quantization::QuantizationOptions>(Arena*);
template<> ::tensorflow::quantization::QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::quantization::QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::quantization::RepresentativeDataSample* Arena::CreateMaybeMessage<::tensorflow::quantization::RepresentativeDataSample>(Arena*);
template<> ::tensorflow::quantization::RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::quantization::RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::quantization::RepresentativeDatasetFile* Arena::CreateMaybeMessage<::tensorflow::quantization::RepresentativeDatasetFile>(Arena*);
template<> ::tensorflow::quantization::UnitWiseQuantizationSpec* Arena::CreateMaybeMessage<::tensorflow::quantization::UnitWiseQuantizationSpec>(Arena*);
template<> ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* Arena::CreateMaybeMessage<::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace quantization {

enum QuantizationMethod_PresetMethod : int {
  QuantizationMethod_PresetMethod_METHOD_UNSPECIFIED = 0,
  QuantizationMethod_PresetMethod_METHOD_NO_QUANTIZE = 1,
  QuantizationMethod_PresetMethod_METHOD_STATIC_RANGE_INT8 = 2,
  QuantizationMethod_PresetMethod_METHOD_DYNAMIC_RANGE_INT8 = 3,
  QuantizationMethod_PresetMethod_METHOD_STATIC_RANGE_WEIGHT_ONLY_INT8 = 4,
  QuantizationMethod_PresetMethod_QuantizationMethod_PresetMethod_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationMethod_PresetMethod_QuantizationMethod_PresetMethod_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationMethod_PresetMethod_IsValid(int value);
constexpr QuantizationMethod_PresetMethod QuantizationMethod_PresetMethod_PresetMethod_MIN = QuantizationMethod_PresetMethod_METHOD_UNSPECIFIED;
constexpr QuantizationMethod_PresetMethod QuantizationMethod_PresetMethod_PresetMethod_MAX = QuantizationMethod_PresetMethod_METHOD_STATIC_RANGE_WEIGHT_ONLY_INT8;
constexpr int QuantizationMethod_PresetMethod_PresetMethod_ARRAYSIZE = QuantizationMethod_PresetMethod_PresetMethod_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationMethod_PresetMethod_descriptor();
template<typename T>
inline const std::string& QuantizationMethod_PresetMethod_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationMethod_PresetMethod>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationMethod_PresetMethod_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationMethod_PresetMethod_descriptor(), enum_t_value);
}
inline bool QuantizationMethod_PresetMethod_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationMethod_PresetMethod* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationMethod_PresetMethod>(
    QuantizationMethod_PresetMethod_descriptor(), name, value);
}
enum QuantizationComponentSpec_QuantizationComponent : int {
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_UNSPECIFIED = 0,
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_ACTIVATION = 1,
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_WEIGHT = 2,
  QuantizationComponentSpec_QuantizationComponent_COMPONENT_BIAS = 3,
  QuantizationComponentSpec_QuantizationComponent_QuantizationComponentSpec_QuantizationComponent_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationComponentSpec_QuantizationComponent_QuantizationComponentSpec_QuantizationComponent_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationComponentSpec_QuantizationComponent_IsValid(int value);
constexpr QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MIN = QuantizationComponentSpec_QuantizationComponent_COMPONENT_UNSPECIFIED;
constexpr QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MAX = QuantizationComponentSpec_QuantizationComponent_COMPONENT_BIAS;
constexpr int QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_ARRAYSIZE = QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationComponentSpec_QuantizationComponent_descriptor();
template<typename T>
inline const std::string& QuantizationComponentSpec_QuantizationComponent_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationComponentSpec_QuantizationComponent>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationComponentSpec_QuantizationComponent_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationComponentSpec_QuantizationComponent_descriptor(), enum_t_value);
}
inline bool QuantizationComponentSpec_QuantizationComponent_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationComponentSpec_QuantizationComponent* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationComponentSpec_QuantizationComponent>(
    QuantizationComponentSpec_QuantizationComponent_descriptor(), name, value);
}
enum QuantizationComponentSpec_TensorType : int {
  QuantizationComponentSpec_TensorType_TENSORTYPE_UNSPECIFIED = 0,
  QuantizationComponentSpec_TensorType_TENSORTYPE_INT_4 = 1,
  QuantizationComponentSpec_TensorType_TENSORTYPE_INT_8 = 2,
  QuantizationComponentSpec_TensorType_TENSORTYPE_INT_32 = 3,
  QuantizationComponentSpec_TensorType_QuantizationComponentSpec_TensorType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationComponentSpec_TensorType_QuantizationComponentSpec_TensorType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationComponentSpec_TensorType_IsValid(int value);
constexpr QuantizationComponentSpec_TensorType QuantizationComponentSpec_TensorType_TensorType_MIN = QuantizationComponentSpec_TensorType_TENSORTYPE_UNSPECIFIED;
constexpr QuantizationComponentSpec_TensorType QuantizationComponentSpec_TensorType_TensorType_MAX = QuantizationComponentSpec_TensorType_TENSORTYPE_INT_32;
constexpr int QuantizationComponentSpec_TensorType_TensorType_ARRAYSIZE = QuantizationComponentSpec_TensorType_TensorType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationComponentSpec_TensorType_descriptor();
template<typename T>
inline const std::string& QuantizationComponentSpec_TensorType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationComponentSpec_TensorType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationComponentSpec_TensorType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationComponentSpec_TensorType_descriptor(), enum_t_value);
}
inline bool QuantizationComponentSpec_TensorType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationComponentSpec_TensorType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationComponentSpec_TensorType>(
    QuantizationComponentSpec_TensorType_descriptor(), name, value);
}
enum OpSet : int {
  OP_SET_UNSPECIFIED = 0,
  TF = 1,
  XLA = 2,
  UNIFORM_QUANTIZED = 3,
  STABLEHLO = 4,
  OpSet_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OpSet_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OpSet_IsValid(int value);
constexpr OpSet OpSet_MIN = OP_SET_UNSPECIFIED;
constexpr OpSet OpSet_MAX = STABLEHLO;
constexpr int OpSet_ARRAYSIZE = OpSet_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OpSet_descriptor();
template<typename T>
inline const std::string& OpSet_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OpSet>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OpSet_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OpSet_descriptor(), enum_t_value);
}
inline bool OpSet_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OpSet* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OpSet>(
    OpSet_descriptor(), name, value);
}
// ===================================================================

class QuantizationMethod final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.QuantizationMethod) */ {
 public:
  inline QuantizationMethod() : QuantizationMethod(nullptr) {}
  ~QuantizationMethod() override;
  explicit PROTOBUF_CONSTEXPR QuantizationMethod(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationMethod(const QuantizationMethod& from);
  QuantizationMethod(QuantizationMethod&& from) noexcept
    : QuantizationMethod() {
    *this = ::std::move(from);
  }

  inline QuantizationMethod& operator=(const QuantizationMethod& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationMethod& operator=(QuantizationMethod&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationMethod& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationMethod* internal_default_instance() {
    return reinterpret_cast<const QuantizationMethod*>(
               &_QuantizationMethod_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(QuantizationMethod& a, QuantizationMethod& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationMethod* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationMethod* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationMethod* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationMethod>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationMethod& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationMethod& from) {
    QuantizationMethod::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationMethod* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.QuantizationMethod";
  }
  protected:
  explicit QuantizationMethod(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef QuantizationMethod_PresetMethod PresetMethod;
  static constexpr PresetMethod METHOD_UNSPECIFIED =
    QuantizationMethod_PresetMethod_METHOD_UNSPECIFIED;
  static constexpr PresetMethod METHOD_NO_QUANTIZE =
    QuantizationMethod_PresetMethod_METHOD_NO_QUANTIZE;
  static constexpr PresetMethod METHOD_STATIC_RANGE_INT8 =
    QuantizationMethod_PresetMethod_METHOD_STATIC_RANGE_INT8;
  static constexpr PresetMethod METHOD_DYNAMIC_RANGE_INT8 =
    QuantizationMethod_PresetMethod_METHOD_DYNAMIC_RANGE_INT8;
  static constexpr PresetMethod METHOD_STATIC_RANGE_WEIGHT_ONLY_INT8 =
    QuantizationMethod_PresetMethod_METHOD_STATIC_RANGE_WEIGHT_ONLY_INT8;
  static inline bool PresetMethod_IsValid(int value) {
    return QuantizationMethod_PresetMethod_IsValid(value);
  }
  static constexpr PresetMethod PresetMethod_MIN =
    QuantizationMethod_PresetMethod_PresetMethod_MIN;
  static constexpr PresetMethod PresetMethod_MAX =
    QuantizationMethod_PresetMethod_PresetMethod_MAX;
  static constexpr int PresetMethod_ARRAYSIZE =
    QuantizationMethod_PresetMethod_PresetMethod_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  PresetMethod_descriptor() {
    return QuantizationMethod_PresetMethod_descriptor();
  }
  template<typename T>
  static inline const std::string& PresetMethod_Name(T enum_t_value) {
    static_assert(::std::is_same<T, PresetMethod>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function PresetMethod_Name.");
    return QuantizationMethod_PresetMethod_Name(enum_t_value);
  }
  static inline bool PresetMethod_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      PresetMethod* value) {
    return QuantizationMethod_PresetMethod_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kQuantizationComponentSpecsFieldNumber = 3,
    kPresetMethodFieldNumber = 4,
  };
  // repeated .tensorflow.quantization.QuantizationComponentSpec quantization_component_specs = 3;
  int quantization_component_specs_size() const;
  private:
  int _internal_quantization_component_specs_size() const;
  public:
  void clear_quantization_component_specs();
  ::tensorflow::quantization::QuantizationComponentSpec* mutable_quantization_component_specs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::QuantizationComponentSpec >*
      mutable_quantization_component_specs();
  private:
  const ::tensorflow::quantization::QuantizationComponentSpec& _internal_quantization_component_specs(int index) const;
  ::tensorflow::quantization::QuantizationComponentSpec* _internal_add_quantization_component_specs();
  public:
  const ::tensorflow::quantization::QuantizationComponentSpec& quantization_component_specs(int index) const;
  ::tensorflow::quantization::QuantizationComponentSpec* add_quantization_component_specs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::QuantizationComponentSpec >&
      quantization_component_specs() const;

  // .tensorflow.quantization.QuantizationMethod.PresetMethod preset_method = 4;
  void clear_preset_method();
  ::tensorflow::quantization::QuantizationMethod_PresetMethod preset_method() const;
  void set_preset_method(::tensorflow::quantization::QuantizationMethod_PresetMethod value);
  private:
  ::tensorflow::quantization::QuantizationMethod_PresetMethod _internal_preset_method() const;
  void _internal_set_preset_method(::tensorflow::quantization::QuantizationMethod_PresetMethod value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.QuantizationMethod)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::QuantizationComponentSpec > quantization_component_specs_;
    int preset_method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class QuantizationComponentSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.QuantizationComponentSpec) */ {
 public:
  inline QuantizationComponentSpec() : QuantizationComponentSpec(nullptr) {}
  ~QuantizationComponentSpec() override;
  explicit PROTOBUF_CONSTEXPR QuantizationComponentSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationComponentSpec(const QuantizationComponentSpec& from);
  QuantizationComponentSpec(QuantizationComponentSpec&& from) noexcept
    : QuantizationComponentSpec() {
    *this = ::std::move(from);
  }

  inline QuantizationComponentSpec& operator=(const QuantizationComponentSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationComponentSpec& operator=(QuantizationComponentSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationComponentSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationComponentSpec* internal_default_instance() {
    return reinterpret_cast<const QuantizationComponentSpec*>(
               &_QuantizationComponentSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(QuantizationComponentSpec& a, QuantizationComponentSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationComponentSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationComponentSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationComponentSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationComponentSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationComponentSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationComponentSpec& from) {
    QuantizationComponentSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationComponentSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.QuantizationComponentSpec";
  }
  protected:
  explicit QuantizationComponentSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef QuantizationComponentSpec_QuantizationComponent QuantizationComponent;
  static constexpr QuantizationComponent COMPONENT_UNSPECIFIED =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_UNSPECIFIED;
  static constexpr QuantizationComponent COMPONENT_ACTIVATION =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_ACTIVATION;
  static constexpr QuantizationComponent COMPONENT_WEIGHT =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_WEIGHT;
  static constexpr QuantizationComponent COMPONENT_BIAS =
    QuantizationComponentSpec_QuantizationComponent_COMPONENT_BIAS;
  static inline bool QuantizationComponent_IsValid(int value) {
    return QuantizationComponentSpec_QuantizationComponent_IsValid(value);
  }
  static constexpr QuantizationComponent QuantizationComponent_MIN =
    QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MIN;
  static constexpr QuantizationComponent QuantizationComponent_MAX =
    QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_MAX;
  static constexpr int QuantizationComponent_ARRAYSIZE =
    QuantizationComponentSpec_QuantizationComponent_QuantizationComponent_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  QuantizationComponent_descriptor() {
    return QuantizationComponentSpec_QuantizationComponent_descriptor();
  }
  template<typename T>
  static inline const std::string& QuantizationComponent_Name(T enum_t_value) {
    static_assert(::std::is_same<T, QuantizationComponent>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function QuantizationComponent_Name.");
    return QuantizationComponentSpec_QuantizationComponent_Name(enum_t_value);
  }
  static inline bool QuantizationComponent_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      QuantizationComponent* value) {
    return QuantizationComponentSpec_QuantizationComponent_Parse(name, value);
  }

  typedef QuantizationComponentSpec_TensorType TensorType;
  static constexpr TensorType TENSORTYPE_UNSPECIFIED =
    QuantizationComponentSpec_TensorType_TENSORTYPE_UNSPECIFIED;
  static constexpr TensorType TENSORTYPE_INT_4 =
    QuantizationComponentSpec_TensorType_TENSORTYPE_INT_4;
  static constexpr TensorType TENSORTYPE_INT_8 =
    QuantizationComponentSpec_TensorType_TENSORTYPE_INT_8;
  static constexpr TensorType TENSORTYPE_INT_32 =
    QuantizationComponentSpec_TensorType_TENSORTYPE_INT_32;
  static inline bool TensorType_IsValid(int value) {
    return QuantizationComponentSpec_TensorType_IsValid(value);
  }
  static constexpr TensorType TensorType_MIN =
    QuantizationComponentSpec_TensorType_TensorType_MIN;
  static constexpr TensorType TensorType_MAX =
    QuantizationComponentSpec_TensorType_TensorType_MAX;
  static constexpr int TensorType_ARRAYSIZE =
    QuantizationComponentSpec_TensorType_TensorType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  TensorType_descriptor() {
    return QuantizationComponentSpec_TensorType_descriptor();
  }
  template<typename T>
  static inline const std::string& TensorType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, TensorType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function TensorType_Name.");
    return QuantizationComponentSpec_TensorType_Name(enum_t_value);
  }
  static inline bool TensorType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      TensorType* value) {
    return QuantizationComponentSpec_TensorType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kQuantizationComponentFieldNumber = 1,
    kTensorTypeFieldNumber = 2,
  };
  // .tensorflow.quantization.QuantizationComponentSpec.QuantizationComponent quantization_component = 1;
  void clear_quantization_component();
  ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent quantization_component() const;
  void set_quantization_component(::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent value);
  private:
  ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent _internal_quantization_component() const;
  void _internal_set_quantization_component(::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent value);
  public:

  // .tensorflow.quantization.QuantizationComponentSpec.TensorType tensor_type = 2;
  void clear_tensor_type();
  ::tensorflow::quantization::QuantizationComponentSpec_TensorType tensor_type() const;
  void set_tensor_type(::tensorflow::quantization::QuantizationComponentSpec_TensorType value);
  private:
  ::tensorflow::quantization::QuantizationComponentSpec_TensorType _internal_tensor_type() const;
  void _internal_set_tensor_type(::tensorflow::quantization::QuantizationComponentSpec_TensorType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.QuantizationComponentSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int quantization_component_;
    int tensor_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class UnitWiseQuantizationSpec_QuantizationUnit final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit) */ {
 public:
  inline UnitWiseQuantizationSpec_QuantizationUnit() : UnitWiseQuantizationSpec_QuantizationUnit(nullptr) {}
  ~UnitWiseQuantizationSpec_QuantizationUnit() override;
  explicit PROTOBUF_CONSTEXPR UnitWiseQuantizationSpec_QuantizationUnit(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UnitWiseQuantizationSpec_QuantizationUnit(const UnitWiseQuantizationSpec_QuantizationUnit& from);
  UnitWiseQuantizationSpec_QuantizationUnit(UnitWiseQuantizationSpec_QuantizationUnit&& from) noexcept
    : UnitWiseQuantizationSpec_QuantizationUnit() {
    *this = ::std::move(from);
  }

  inline UnitWiseQuantizationSpec_QuantizationUnit& operator=(const UnitWiseQuantizationSpec_QuantizationUnit& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnitWiseQuantizationSpec_QuantizationUnit& operator=(UnitWiseQuantizationSpec_QuantizationUnit&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UnitWiseQuantizationSpec_QuantizationUnit& default_instance() {
    return *internal_default_instance();
  }
  static inline const UnitWiseQuantizationSpec_QuantizationUnit* internal_default_instance() {
    return reinterpret_cast<const UnitWiseQuantizationSpec_QuantizationUnit*>(
               &_UnitWiseQuantizationSpec_QuantizationUnit_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UnitWiseQuantizationSpec_QuantizationUnit& a, UnitWiseQuantizationSpec_QuantizationUnit& b) {
    a.Swap(&b);
  }
  inline void Swap(UnitWiseQuantizationSpec_QuantizationUnit* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UnitWiseQuantizationSpec_QuantizationUnit* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UnitWiseQuantizationSpec_QuantizationUnit* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UnitWiseQuantizationSpec_QuantizationUnit>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UnitWiseQuantizationSpec_QuantizationUnit& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UnitWiseQuantizationSpec_QuantizationUnit& from) {
    UnitWiseQuantizationSpec_QuantizationUnit::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnitWiseQuantizationSpec_QuantizationUnit* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit";
  }
  protected:
  explicit UnitWiseQuantizationSpec_QuantizationUnit(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpTypeFieldNumber = 1,
    kNodeNameFieldNumber = 2,
    kFuncNameFieldNumber = 3,
  };
  // string op_type = 1;
  void clear_op_type();
  const std::string& op_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_type();
  PROTOBUF_NODISCARD std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  private:
  const std::string& _internal_op_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_type(const std::string& value);
  std::string* _internal_mutable_op_type();
  public:

  // string node_name = 2;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // string func_name = 3;
  void clear_func_name();
  const std::string& func_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_func_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_func_name();
  PROTOBUF_NODISCARD std::string* release_func_name();
  void set_allocated_func_name(std::string* func_name);
  private:
  const std::string& _internal_func_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_func_name(const std::string& value);
  std::string* _internal_mutable_func_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr func_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class UnitWiseQuantizationSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.UnitWiseQuantizationSpec) */ {
 public:
  inline UnitWiseQuantizationSpec() : UnitWiseQuantizationSpec(nullptr) {}
  ~UnitWiseQuantizationSpec() override;
  explicit PROTOBUF_CONSTEXPR UnitWiseQuantizationSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UnitWiseQuantizationSpec(const UnitWiseQuantizationSpec& from);
  UnitWiseQuantizationSpec(UnitWiseQuantizationSpec&& from) noexcept
    : UnitWiseQuantizationSpec() {
    *this = ::std::move(from);
  }

  inline UnitWiseQuantizationSpec& operator=(const UnitWiseQuantizationSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnitWiseQuantizationSpec& operator=(UnitWiseQuantizationSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UnitWiseQuantizationSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const UnitWiseQuantizationSpec* internal_default_instance() {
    return reinterpret_cast<const UnitWiseQuantizationSpec*>(
               &_UnitWiseQuantizationSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(UnitWiseQuantizationSpec& a, UnitWiseQuantizationSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(UnitWiseQuantizationSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UnitWiseQuantizationSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UnitWiseQuantizationSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UnitWiseQuantizationSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UnitWiseQuantizationSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UnitWiseQuantizationSpec& from) {
    UnitWiseQuantizationSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnitWiseQuantizationSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.UnitWiseQuantizationSpec";
  }
  protected:
  explicit UnitWiseQuantizationSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef UnitWiseQuantizationSpec_QuantizationUnit QuantizationUnit;

  // accessors -------------------------------------------------------

  enum : int {
    kUnitFieldNumber = 5,
    kQuantizationMethodFieldNumber = 6,
  };
  // repeated .tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit unit = 5;
  int unit_size() const;
  private:
  int _internal_unit_size() const;
  public:
  void clear_unit();
  ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* mutable_unit(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit >*
      mutable_unit();
  private:
  const ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit& _internal_unit(int index) const;
  ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* _internal_add_unit();
  public:
  const ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit& unit(int index) const;
  ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* add_unit();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit >&
      unit() const;

  // .tensorflow.quantization.QuantizationMethod quantization_method = 6;
  bool has_quantization_method() const;
  private:
  bool _internal_has_quantization_method() const;
  public:
  void clear_quantization_method();
  const ::tensorflow::quantization::QuantizationMethod& quantization_method() const;
  PROTOBUF_NODISCARD ::tensorflow::quantization::QuantizationMethod* release_quantization_method();
  ::tensorflow::quantization::QuantizationMethod* mutable_quantization_method();
  void set_allocated_quantization_method(::tensorflow::quantization::QuantizationMethod* quantization_method);
  private:
  const ::tensorflow::quantization::QuantizationMethod& _internal_quantization_method() const;
  ::tensorflow::quantization::QuantizationMethod* _internal_mutable_quantization_method();
  public:
  void unsafe_arena_set_allocated_quantization_method(
      ::tensorflow::quantization::QuantizationMethod* quantization_method);
  ::tensorflow::quantization::QuantizationMethod* unsafe_arena_release_quantization_method();

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.UnitWiseQuantizationSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit > unit_;
    ::tensorflow::quantization::QuantizationMethod* quantization_method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse& other);
  static const RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse*>(&_RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.quantization.RepresentativeDataSample.TensorProtoInputsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};

// -------------------------------------------------------------------

class RepresentativeDataSample final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.RepresentativeDataSample) */ {
 public:
  inline RepresentativeDataSample() : RepresentativeDataSample(nullptr) {}
  ~RepresentativeDataSample() override;
  explicit PROTOBUF_CONSTEXPR RepresentativeDataSample(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RepresentativeDataSample(const RepresentativeDataSample& from);
  RepresentativeDataSample(RepresentativeDataSample&& from) noexcept
    : RepresentativeDataSample() {
    *this = ::std::move(from);
  }

  inline RepresentativeDataSample& operator=(const RepresentativeDataSample& from) {
    CopyFrom(from);
    return *this;
  }
  inline RepresentativeDataSample& operator=(RepresentativeDataSample&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RepresentativeDataSample& default_instance() {
    return *internal_default_instance();
  }
  static inline const RepresentativeDataSample* internal_default_instance() {
    return reinterpret_cast<const RepresentativeDataSample*>(
               &_RepresentativeDataSample_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RepresentativeDataSample& a, RepresentativeDataSample& b) {
    a.Swap(&b);
  }
  inline void Swap(RepresentativeDataSample* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RepresentativeDataSample* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RepresentativeDataSample* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RepresentativeDataSample>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RepresentativeDataSample& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RepresentativeDataSample& from) {
    RepresentativeDataSample::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RepresentativeDataSample* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.RepresentativeDataSample";
  }
  protected:
  explicit RepresentativeDataSample(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kTensorProtoInputsFieldNumber = 2,
  };
  // map<string, .tensorflow.TensorProto> tensor_proto_inputs = 2;
  int tensor_proto_inputs_size() const;
  private:
  int _internal_tensor_proto_inputs_size() const;
  public:
  void clear_tensor_proto_inputs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
      _internal_tensor_proto_inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
      _internal_mutable_tensor_proto_inputs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
      tensor_proto_inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
      mutable_tensor_proto_inputs();

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.RepresentativeDataSample)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        RepresentativeDataSample_TensorProtoInputsEntry_DoNotUse,
        std::string, ::tensorflow::TensorProto,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> tensor_proto_inputs_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class RepresentativeDatasetFile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.RepresentativeDatasetFile) */ {
 public:
  inline RepresentativeDatasetFile() : RepresentativeDatasetFile(nullptr) {}
  ~RepresentativeDatasetFile() override;
  explicit PROTOBUF_CONSTEXPR RepresentativeDatasetFile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RepresentativeDatasetFile(const RepresentativeDatasetFile& from);
  RepresentativeDatasetFile(RepresentativeDatasetFile&& from) noexcept
    : RepresentativeDatasetFile() {
    *this = ::std::move(from);
  }

  inline RepresentativeDatasetFile& operator=(const RepresentativeDatasetFile& from) {
    CopyFrom(from);
    return *this;
  }
  inline RepresentativeDatasetFile& operator=(RepresentativeDatasetFile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RepresentativeDatasetFile& default_instance() {
    return *internal_default_instance();
  }
  enum DatasetFileCase {
    kTfrecordFilePath = 1,
    DATASET_FILE_NOT_SET = 0,
  };

  static inline const RepresentativeDatasetFile* internal_default_instance() {
    return reinterpret_cast<const RepresentativeDatasetFile*>(
               &_RepresentativeDatasetFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RepresentativeDatasetFile& a, RepresentativeDatasetFile& b) {
    a.Swap(&b);
  }
  inline void Swap(RepresentativeDatasetFile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RepresentativeDatasetFile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RepresentativeDatasetFile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RepresentativeDatasetFile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RepresentativeDatasetFile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RepresentativeDatasetFile& from) {
    RepresentativeDatasetFile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RepresentativeDatasetFile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.RepresentativeDatasetFile";
  }
  protected:
  explicit RepresentativeDatasetFile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTfrecordFilePathFieldNumber = 1,
  };
  // string tfrecord_file_path = 1;
  bool has_tfrecord_file_path() const;
  private:
  bool _internal_has_tfrecord_file_path() const;
  public:
  void clear_tfrecord_file_path();
  const std::string& tfrecord_file_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tfrecord_file_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tfrecord_file_path();
  PROTOBUF_NODISCARD std::string* release_tfrecord_file_path();
  void set_allocated_tfrecord_file_path(std::string* tfrecord_file_path);
  private:
  const std::string& _internal_tfrecord_file_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tfrecord_file_path(const std::string& value);
  std::string* _internal_mutable_tfrecord_file_path();
  public:

  void clear_dataset_file();
  DatasetFileCase dataset_file_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.quantization.RepresentativeDatasetFile)
 private:
  class _Internal;
  void set_has_tfrecord_file_path();

  inline bool has_dataset_file() const;
  inline void clear_has_dataset_file();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union DatasetFileUnion {
      constexpr DatasetFileUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tfrecord_file_path_;
    } dataset_file_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse, 
    std::string, ::tensorflow::quantization::RepresentativeDatasetFile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse, 
    std::string, ::tensorflow::quantization::RepresentativeDatasetFile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse& other);
  static const QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse*>(&_QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.quantization.QuantizationOptions.RepresentativeDatasetsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};

// -------------------------------------------------------------------

class QuantizationOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.QuantizationOptions) */ {
 public:
  inline QuantizationOptions() : QuantizationOptions(nullptr) {}
  ~QuantizationOptions() override;
  explicit PROTOBUF_CONSTEXPR QuantizationOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationOptions(const QuantizationOptions& from);
  QuantizationOptions(QuantizationOptions&& from) noexcept
    : QuantizationOptions() {
    *this = ::std::move(from);
  }

  inline QuantizationOptions& operator=(const QuantizationOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationOptions& operator=(QuantizationOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationOptions* internal_default_instance() {
    return reinterpret_cast<const QuantizationOptions*>(
               &_QuantizationOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(QuantizationOptions& a, QuantizationOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationOptions& from) {
    QuantizationOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.QuantizationOptions";
  }
  protected:
  explicit QuantizationOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kTagsFieldNumber = 5,
    kSignatureKeysFieldNumber = 6,
    kRepresentativeDatasetsFieldNumber = 7,
    kUnitWiseQuantizationSpecsFieldNumber = 17,
    kQuantizationMethodFieldNumber = 1,
    kCalibrationOptionsFieldNumber = 15,
    kDebuggerConfigFieldNumber = 16,
    kMinNumElementsForWeightsFieldNumber = 8,
    kOpSetFieldNumber = 2,
    kFreezeAllVariablesFieldNumber = 9,
    kEnablePerChannelQuantizationFieldNumber = 10,
    kEnableTwoInputTensorsFieldNumber = 11,
    kExperimentalEnableTpuModelSupportFieldNumber = 12,
    kEnableLegacyWeightOnlyFieldNumber = 13,
    kForceGraphModeCalibrationFieldNumber = 14,
  };
  // repeated string tags = 5;
  int tags_size() const;
  private:
  int _internal_tags_size() const;
  public:
  void clear_tags();
  const std::string& tags(int index) const;
  std::string* mutable_tags(int index);
  void set_tags(int index, const std::string& value);
  void set_tags(int index, std::string&& value);
  void set_tags(int index, const char* value);
  void set_tags(int index, const char* value, size_t size);
  std::string* add_tags();
  void add_tags(const std::string& value);
  void add_tags(std::string&& value);
  void add_tags(const char* value);
  void add_tags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& tags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_tags();
  private:
  const std::string& _internal_tags(int index) const;
  std::string* _internal_add_tags();
  public:

  // repeated string signature_keys = 6;
  int signature_keys_size() const;
  private:
  int _internal_signature_keys_size() const;
  public:
  void clear_signature_keys();
  const std::string& signature_keys(int index) const;
  std::string* mutable_signature_keys(int index);
  void set_signature_keys(int index, const std::string& value);
  void set_signature_keys(int index, std::string&& value);
  void set_signature_keys(int index, const char* value);
  void set_signature_keys(int index, const char* value, size_t size);
  std::string* add_signature_keys();
  void add_signature_keys(const std::string& value);
  void add_signature_keys(std::string&& value);
  void add_signature_keys(const char* value);
  void add_signature_keys(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& signature_keys() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_signature_keys();
  private:
  const std::string& _internal_signature_keys(int index) const;
  std::string* _internal_add_signature_keys();
  public:

  // map<string, .tensorflow.quantization.RepresentativeDatasetFile> representative_datasets = 7;
  int representative_datasets_size() const;
  private:
  int _internal_representative_datasets_size() const;
  public:
  void clear_representative_datasets();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >&
      _internal_representative_datasets() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >*
      _internal_mutable_representative_datasets();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >&
      representative_datasets() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >*
      mutable_representative_datasets();

  // repeated .tensorflow.quantization.UnitWiseQuantizationSpec unit_wise_quantization_specs = 17;
  int unit_wise_quantization_specs_size() const;
  private:
  int _internal_unit_wise_quantization_specs_size() const;
  public:
  void clear_unit_wise_quantization_specs();
  ::tensorflow::quantization::UnitWiseQuantizationSpec* mutable_unit_wise_quantization_specs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec >*
      mutable_unit_wise_quantization_specs();
  private:
  const ::tensorflow::quantization::UnitWiseQuantizationSpec& _internal_unit_wise_quantization_specs(int index) const;
  ::tensorflow::quantization::UnitWiseQuantizationSpec* _internal_add_unit_wise_quantization_specs();
  public:
  const ::tensorflow::quantization::UnitWiseQuantizationSpec& unit_wise_quantization_specs(int index) const;
  ::tensorflow::quantization::UnitWiseQuantizationSpec* add_unit_wise_quantization_specs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec >&
      unit_wise_quantization_specs() const;

  // .tensorflow.quantization.QuantizationMethod quantization_method = 1;
  bool has_quantization_method() const;
  private:
  bool _internal_has_quantization_method() const;
  public:
  void clear_quantization_method();
  const ::tensorflow::quantization::QuantizationMethod& quantization_method() const;
  PROTOBUF_NODISCARD ::tensorflow::quantization::QuantizationMethod* release_quantization_method();
  ::tensorflow::quantization::QuantizationMethod* mutable_quantization_method();
  void set_allocated_quantization_method(::tensorflow::quantization::QuantizationMethod* quantization_method);
  private:
  const ::tensorflow::quantization::QuantizationMethod& _internal_quantization_method() const;
  ::tensorflow::quantization::QuantizationMethod* _internal_mutable_quantization_method();
  public:
  void unsafe_arena_set_allocated_quantization_method(
      ::tensorflow::quantization::QuantizationMethod* quantization_method);
  ::tensorflow::quantization::QuantizationMethod* unsafe_arena_release_quantization_method();

  // .stablehlo.quantization.CalibrationOptions calibration_options = 15;
  bool has_calibration_options() const;
  private:
  bool _internal_has_calibration_options() const;
  public:
  void clear_calibration_options();
  const ::stablehlo::quantization::CalibrationOptions& calibration_options() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::CalibrationOptions* release_calibration_options();
  ::stablehlo::quantization::CalibrationOptions* mutable_calibration_options();
  void set_allocated_calibration_options(::stablehlo::quantization::CalibrationOptions* calibration_options);
  private:
  const ::stablehlo::quantization::CalibrationOptions& _internal_calibration_options() const;
  ::stablehlo::quantization::CalibrationOptions* _internal_mutable_calibration_options();
  public:
  void unsafe_arena_set_allocated_calibration_options(
      ::stablehlo::quantization::CalibrationOptions* calibration_options);
  ::stablehlo::quantization::CalibrationOptions* unsafe_arena_release_calibration_options();

  // .stablehlo.quantization.DebuggerConfig debugger_config = 16;
  bool has_debugger_config() const;
  private:
  bool _internal_has_debugger_config() const;
  public:
  void clear_debugger_config();
  const ::stablehlo::quantization::DebuggerConfig& debugger_config() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::DebuggerConfig* release_debugger_config();
  ::stablehlo::quantization::DebuggerConfig* mutable_debugger_config();
  void set_allocated_debugger_config(::stablehlo::quantization::DebuggerConfig* debugger_config);
  private:
  const ::stablehlo::quantization::DebuggerConfig& _internal_debugger_config() const;
  ::stablehlo::quantization::DebuggerConfig* _internal_mutable_debugger_config();
  public:
  void unsafe_arena_set_allocated_debugger_config(
      ::stablehlo::quantization::DebuggerConfig* debugger_config);
  ::stablehlo::quantization::DebuggerConfig* unsafe_arena_release_debugger_config();

  // int64 min_num_elements_for_weights = 8;
  void clear_min_num_elements_for_weights();
  int64_t min_num_elements_for_weights() const;
  void set_min_num_elements_for_weights(int64_t value);
  private:
  int64_t _internal_min_num_elements_for_weights() const;
  void _internal_set_min_num_elements_for_weights(int64_t value);
  public:

  // .tensorflow.quantization.OpSet op_set = 2;
  void clear_op_set();
  ::tensorflow::quantization::OpSet op_set() const;
  void set_op_set(::tensorflow::quantization::OpSet value);
  private:
  ::tensorflow::quantization::OpSet _internal_op_set() const;
  void _internal_set_op_set(::tensorflow::quantization::OpSet value);
  public:

  // optional bool freeze_all_variables = 9;
  bool has_freeze_all_variables() const;
  private:
  bool _internal_has_freeze_all_variables() const;
  public:
  void clear_freeze_all_variables();
  bool freeze_all_variables() const;
  void set_freeze_all_variables(bool value);
  private:
  bool _internal_freeze_all_variables() const;
  void _internal_set_freeze_all_variables(bool value);
  public:

  // optional bool enable_per_channel_quantization = 10;
  bool has_enable_per_channel_quantization() const;
  private:
  bool _internal_has_enable_per_channel_quantization() const;
  public:
  void clear_enable_per_channel_quantization();
  bool enable_per_channel_quantization() const;
  void set_enable_per_channel_quantization(bool value);
  private:
  bool _internal_enable_per_channel_quantization() const;
  void _internal_set_enable_per_channel_quantization(bool value);
  public:

  // bool enable_two_input_tensors = 11;
  void clear_enable_two_input_tensors();
  bool enable_two_input_tensors() const;
  void set_enable_two_input_tensors(bool value);
  private:
  bool _internal_enable_two_input_tensors() const;
  void _internal_set_enable_two_input_tensors(bool value);
  public:

  // bool experimental_enable_tpu_model_support = 12;
  void clear_experimental_enable_tpu_model_support();
  bool experimental_enable_tpu_model_support() const;
  void set_experimental_enable_tpu_model_support(bool value);
  private:
  bool _internal_experimental_enable_tpu_model_support() const;
  void _internal_set_experimental_enable_tpu_model_support(bool value);
  public:

  // bool enable_legacy_weight_only = 13;
  void clear_enable_legacy_weight_only();
  bool enable_legacy_weight_only() const;
  void set_enable_legacy_weight_only(bool value);
  private:
  bool _internal_enable_legacy_weight_only() const;
  void _internal_set_enable_legacy_weight_only(bool value);
  public:

  // bool force_graph_mode_calibration = 14;
  void clear_force_graph_mode_calibration();
  bool force_graph_mode_calibration() const;
  void set_force_graph_mode_calibration(bool value);
  private:
  bool _internal_force_graph_mode_calibration() const;
  void _internal_set_force_graph_mode_calibration(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.QuantizationOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> tags_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> signature_keys_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        QuantizationOptions_RepresentativeDatasetsEntry_DoNotUse,
        std::string, ::tensorflow::quantization::RepresentativeDatasetFile,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> representative_datasets_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec > unit_wise_quantization_specs_;
    ::tensorflow::quantization::QuantizationMethod* quantization_method_;
    ::stablehlo::quantization::CalibrationOptions* calibration_options_;
    ::stablehlo::quantization::DebuggerConfig* debugger_config_;
    int64_t min_num_elements_for_weights_;
    int op_set_;
    bool freeze_all_variables_;
    bool enable_per_channel_quantization_;
    bool enable_two_input_tensors_;
    bool experimental_enable_tpu_model_support_;
    bool enable_legacy_weight_only_;
    bool force_graph_mode_calibration_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// QuantizationMethod

// .tensorflow.quantization.QuantizationMethod.PresetMethod preset_method = 4;
inline void QuantizationMethod::clear_preset_method() {
  _impl_.preset_method_ = 0;
}
inline ::tensorflow::quantization::QuantizationMethod_PresetMethod QuantizationMethod::_internal_preset_method() const {
  return static_cast< ::tensorflow::quantization::QuantizationMethod_PresetMethod >(_impl_.preset_method_);
}
inline ::tensorflow::quantization::QuantizationMethod_PresetMethod QuantizationMethod::preset_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationMethod.preset_method)
  return _internal_preset_method();
}
inline void QuantizationMethod::_internal_set_preset_method(::tensorflow::quantization::QuantizationMethod_PresetMethod value) {
  
  _impl_.preset_method_ = value;
}
inline void QuantizationMethod::set_preset_method(::tensorflow::quantization::QuantizationMethod_PresetMethod value) {
  _internal_set_preset_method(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationMethod.preset_method)
}

// repeated .tensorflow.quantization.QuantizationComponentSpec quantization_component_specs = 3;
inline int QuantizationMethod::_internal_quantization_component_specs_size() const {
  return _impl_.quantization_component_specs_.size();
}
inline int QuantizationMethod::quantization_component_specs_size() const {
  return _internal_quantization_component_specs_size();
}
inline void QuantizationMethod::clear_quantization_component_specs() {
  _impl_.quantization_component_specs_.Clear();
}
inline ::tensorflow::quantization::QuantizationComponentSpec* QuantizationMethod::mutable_quantization_component_specs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationMethod.quantization_component_specs)
  return _impl_.quantization_component_specs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::QuantizationComponentSpec >*
QuantizationMethod::mutable_quantization_component_specs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.QuantizationMethod.quantization_component_specs)
  return &_impl_.quantization_component_specs_;
}
inline const ::tensorflow::quantization::QuantizationComponentSpec& QuantizationMethod::_internal_quantization_component_specs(int index) const {
  return _impl_.quantization_component_specs_.Get(index);
}
inline const ::tensorflow::quantization::QuantizationComponentSpec& QuantizationMethod::quantization_component_specs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationMethod.quantization_component_specs)
  return _internal_quantization_component_specs(index);
}
inline ::tensorflow::quantization::QuantizationComponentSpec* QuantizationMethod::_internal_add_quantization_component_specs() {
  return _impl_.quantization_component_specs_.Add();
}
inline ::tensorflow::quantization::QuantizationComponentSpec* QuantizationMethod::add_quantization_component_specs() {
  ::tensorflow::quantization::QuantizationComponentSpec* _add = _internal_add_quantization_component_specs();
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationMethod.quantization_component_specs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::QuantizationComponentSpec >&
QuantizationMethod::quantization_component_specs() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.QuantizationMethod.quantization_component_specs)
  return _impl_.quantization_component_specs_;
}

// -------------------------------------------------------------------

// QuantizationComponentSpec

// .tensorflow.quantization.QuantizationComponentSpec.QuantizationComponent quantization_component = 1;
inline void QuantizationComponentSpec::clear_quantization_component() {
  _impl_.quantization_component_ = 0;
}
inline ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec::_internal_quantization_component() const {
  return static_cast< ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent >(_impl_.quantization_component_);
}
inline ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent QuantizationComponentSpec::quantization_component() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationComponentSpec.quantization_component)
  return _internal_quantization_component();
}
inline void QuantizationComponentSpec::_internal_set_quantization_component(::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent value) {
  
  _impl_.quantization_component_ = value;
}
inline void QuantizationComponentSpec::set_quantization_component(::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent value) {
  _internal_set_quantization_component(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationComponentSpec.quantization_component)
}

// .tensorflow.quantization.QuantizationComponentSpec.TensorType tensor_type = 2;
inline void QuantizationComponentSpec::clear_tensor_type() {
  _impl_.tensor_type_ = 0;
}
inline ::tensorflow::quantization::QuantizationComponentSpec_TensorType QuantizationComponentSpec::_internal_tensor_type() const {
  return static_cast< ::tensorflow::quantization::QuantizationComponentSpec_TensorType >(_impl_.tensor_type_);
}
inline ::tensorflow::quantization::QuantizationComponentSpec_TensorType QuantizationComponentSpec::tensor_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationComponentSpec.tensor_type)
  return _internal_tensor_type();
}
inline void QuantizationComponentSpec::_internal_set_tensor_type(::tensorflow::quantization::QuantizationComponentSpec_TensorType value) {
  
  _impl_.tensor_type_ = value;
}
inline void QuantizationComponentSpec::set_tensor_type(::tensorflow::quantization::QuantizationComponentSpec_TensorType value) {
  _internal_set_tensor_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationComponentSpec.tensor_type)
}

// -------------------------------------------------------------------

// UnitWiseQuantizationSpec_QuantizationUnit

// string op_type = 1;
inline void UnitWiseQuantizationSpec_QuantizationUnit::clear_op_type() {
  _impl_.op_type_.ClearToEmpty();
}
inline const std::string& UnitWiseQuantizationSpec_QuantizationUnit::op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.op_type)
  return _internal_op_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnitWiseQuantizationSpec_QuantizationUnit::set_op_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.op_type)
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::mutable_op_type() {
  std::string* _s = _internal_mutable_op_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.op_type)
  return _s;
}
inline const std::string& UnitWiseQuantizationSpec_QuantizationUnit::_internal_op_type() const {
  return _impl_.op_type_.Get();
}
inline void UnitWiseQuantizationSpec_QuantizationUnit::_internal_set_op_type(const std::string& value) {
  
  _impl_.op_type_.Set(value, GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::_internal_mutable_op_type() {
  
  return _impl_.op_type_.Mutable(GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::release_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.op_type)
  return _impl_.op_type_.Release();
}
inline void UnitWiseQuantizationSpec_QuantizationUnit::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    
  } else {
    
  }
  _impl_.op_type_.SetAllocated(op_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_type_.IsDefault()) {
    _impl_.op_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.op_type)
}

// string node_name = 2;
inline void UnitWiseQuantizationSpec_QuantizationUnit::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& UnitWiseQuantizationSpec_QuantizationUnit::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnitWiseQuantizationSpec_QuantizationUnit::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.node_name)
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.node_name)
  return _s;
}
inline const std::string& UnitWiseQuantizationSpec_QuantizationUnit::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void UnitWiseQuantizationSpec_QuantizationUnit::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.node_name)
  return _impl_.node_name_.Release();
}
inline void UnitWiseQuantizationSpec_QuantizationUnit::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.node_name)
}

// string func_name = 3;
inline void UnitWiseQuantizationSpec_QuantizationUnit::clear_func_name() {
  _impl_.func_name_.ClearToEmpty();
}
inline const std::string& UnitWiseQuantizationSpec_QuantizationUnit::func_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.func_name)
  return _internal_func_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnitWiseQuantizationSpec_QuantizationUnit::set_func_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.func_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.func_name)
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::mutable_func_name() {
  std::string* _s = _internal_mutable_func_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.func_name)
  return _s;
}
inline const std::string& UnitWiseQuantizationSpec_QuantizationUnit::_internal_func_name() const {
  return _impl_.func_name_.Get();
}
inline void UnitWiseQuantizationSpec_QuantizationUnit::_internal_set_func_name(const std::string& value) {
  
  _impl_.func_name_.Set(value, GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::_internal_mutable_func_name() {
  
  return _impl_.func_name_.Mutable(GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationSpec_QuantizationUnit::release_func_name() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.func_name)
  return _impl_.func_name_.Release();
}
inline void UnitWiseQuantizationSpec_QuantizationUnit::set_allocated_func_name(std::string* func_name) {
  if (func_name != nullptr) {
    
  } else {
    
  }
  _impl_.func_name_.SetAllocated(func_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.func_name_.IsDefault()) {
    _impl_.func_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit.func_name)
}

// -------------------------------------------------------------------

// UnitWiseQuantizationSpec

// repeated .tensorflow.quantization.UnitWiseQuantizationSpec.QuantizationUnit unit = 5;
inline int UnitWiseQuantizationSpec::_internal_unit_size() const {
  return _impl_.unit_.size();
}
inline int UnitWiseQuantizationSpec::unit_size() const {
  return _internal_unit_size();
}
inline void UnitWiseQuantizationSpec::clear_unit() {
  _impl_.unit_.Clear();
}
inline ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* UnitWiseQuantizationSpec::mutable_unit(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationSpec.unit)
  return _impl_.unit_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit >*
UnitWiseQuantizationSpec::mutable_unit() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.UnitWiseQuantizationSpec.unit)
  return &_impl_.unit_;
}
inline const ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit& UnitWiseQuantizationSpec::_internal_unit(int index) const {
  return _impl_.unit_.Get(index);
}
inline const ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit& UnitWiseQuantizationSpec::unit(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationSpec.unit)
  return _internal_unit(index);
}
inline ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* UnitWiseQuantizationSpec::_internal_add_unit() {
  return _impl_.unit_.Add();
}
inline ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* UnitWiseQuantizationSpec::add_unit() {
  ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit* _add = _internal_add_unit();
  // @@protoc_insertion_point(field_add:tensorflow.quantization.UnitWiseQuantizationSpec.unit)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec_QuantizationUnit >&
UnitWiseQuantizationSpec::unit() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.UnitWiseQuantizationSpec.unit)
  return _impl_.unit_;
}

// .tensorflow.quantization.QuantizationMethod quantization_method = 6;
inline bool UnitWiseQuantizationSpec::_internal_has_quantization_method() const {
  return this != internal_default_instance() && _impl_.quantization_method_ != nullptr;
}
inline bool UnitWiseQuantizationSpec::has_quantization_method() const {
  return _internal_has_quantization_method();
}
inline void UnitWiseQuantizationSpec::clear_quantization_method() {
  if (GetArenaForAllocation() == nullptr && _impl_.quantization_method_ != nullptr) {
    delete _impl_.quantization_method_;
  }
  _impl_.quantization_method_ = nullptr;
}
inline const ::tensorflow::quantization::QuantizationMethod& UnitWiseQuantizationSpec::_internal_quantization_method() const {
  const ::tensorflow::quantization::QuantizationMethod* p = _impl_.quantization_method_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::quantization::QuantizationMethod&>(
      ::tensorflow::quantization::_QuantizationMethod_default_instance_);
}
inline const ::tensorflow::quantization::QuantizationMethod& UnitWiseQuantizationSpec::quantization_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationSpec.quantization_method)
  return _internal_quantization_method();
}
inline void UnitWiseQuantizationSpec::unsafe_arena_set_allocated_quantization_method(
    ::tensorflow::quantization::QuantizationMethod* quantization_method) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_method_);
  }
  _impl_.quantization_method_ = quantization_method;
  if (quantization_method) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.UnitWiseQuantizationSpec.quantization_method)
}
inline ::tensorflow::quantization::QuantizationMethod* UnitWiseQuantizationSpec::release_quantization_method() {
  
  ::tensorflow::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::quantization::QuantizationMethod* UnitWiseQuantizationSpec::unsafe_arena_release_quantization_method() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.UnitWiseQuantizationSpec.quantization_method)
  
  ::tensorflow::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
  return temp;
}
inline ::tensorflow::quantization::QuantizationMethod* UnitWiseQuantizationSpec::_internal_mutable_quantization_method() {
  
  if (_impl_.quantization_method_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::quantization::QuantizationMethod>(GetArenaForAllocation());
    _impl_.quantization_method_ = p;
  }
  return _impl_.quantization_method_;
}
inline ::tensorflow::quantization::QuantizationMethod* UnitWiseQuantizationSpec::mutable_quantization_method() {
  ::tensorflow::quantization::QuantizationMethod* _msg = _internal_mutable_quantization_method();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationSpec.quantization_method)
  return _msg;
}
inline void UnitWiseQuantizationSpec::set_allocated_quantization_method(::tensorflow::quantization::QuantizationMethod* quantization_method) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.quantization_method_;
  }
  if (quantization_method) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(quantization_method);
    if (message_arena != submessage_arena) {
      quantization_method = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantization_method, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.quantization_method_ = quantization_method;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.UnitWiseQuantizationSpec.quantization_method)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RepresentativeDataSample

// map<string, .tensorflow.TensorProto> tensor_proto_inputs = 2;
inline int RepresentativeDataSample::_internal_tensor_proto_inputs_size() const {
  return _impl_.tensor_proto_inputs_.size();
}
inline int RepresentativeDataSample::tensor_proto_inputs_size() const {
  return _internal_tensor_proto_inputs_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
RepresentativeDataSample::_internal_tensor_proto_inputs() const {
  return _impl_.tensor_proto_inputs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
RepresentativeDataSample::tensor_proto_inputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.quantization.RepresentativeDataSample.tensor_proto_inputs)
  return _internal_tensor_proto_inputs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
RepresentativeDataSample::_internal_mutable_tensor_proto_inputs() {
  return _impl_.tensor_proto_inputs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
RepresentativeDataSample::mutable_tensor_proto_inputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.quantization.RepresentativeDataSample.tensor_proto_inputs)
  return _internal_mutable_tensor_proto_inputs();
}

// -------------------------------------------------------------------

// RepresentativeDatasetFile

// string tfrecord_file_path = 1;
inline bool RepresentativeDatasetFile::_internal_has_tfrecord_file_path() const {
  return dataset_file_case() == kTfrecordFilePath;
}
inline bool RepresentativeDatasetFile::has_tfrecord_file_path() const {
  return _internal_has_tfrecord_file_path();
}
inline void RepresentativeDatasetFile::set_has_tfrecord_file_path() {
  _impl_._oneof_case_[0] = kTfrecordFilePath;
}
inline void RepresentativeDatasetFile::clear_tfrecord_file_path() {
  if (_internal_has_tfrecord_file_path()) {
    _impl_.dataset_file_.tfrecord_file_path_.Destroy();
    clear_has_dataset_file();
  }
}
inline const std::string& RepresentativeDatasetFile::tfrecord_file_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.RepresentativeDatasetFile.tfrecord_file_path)
  return _internal_tfrecord_file_path();
}
template <typename ArgT0, typename... ArgT>
inline void RepresentativeDatasetFile::set_tfrecord_file_path(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_tfrecord_file_path()) {
    clear_dataset_file();
    set_has_tfrecord_file_path();
    _impl_.dataset_file_.tfrecord_file_path_.InitDefault();
  }
  _impl_.dataset_file_.tfrecord_file_path_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.RepresentativeDatasetFile.tfrecord_file_path)
}
inline std::string* RepresentativeDatasetFile::mutable_tfrecord_file_path() {
  std::string* _s = _internal_mutable_tfrecord_file_path();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.RepresentativeDatasetFile.tfrecord_file_path)
  return _s;
}
inline const std::string& RepresentativeDatasetFile::_internal_tfrecord_file_path() const {
  if (_internal_has_tfrecord_file_path()) {
    return _impl_.dataset_file_.tfrecord_file_path_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void RepresentativeDatasetFile::_internal_set_tfrecord_file_path(const std::string& value) {
  if (!_internal_has_tfrecord_file_path()) {
    clear_dataset_file();
    set_has_tfrecord_file_path();
    _impl_.dataset_file_.tfrecord_file_path_.InitDefault();
  }
  _impl_.dataset_file_.tfrecord_file_path_.Set(value, GetArenaForAllocation());
}
inline std::string* RepresentativeDatasetFile::_internal_mutable_tfrecord_file_path() {
  if (!_internal_has_tfrecord_file_path()) {
    clear_dataset_file();
    set_has_tfrecord_file_path();
    _impl_.dataset_file_.tfrecord_file_path_.InitDefault();
  }
  return _impl_.dataset_file_.tfrecord_file_path_.Mutable(      GetArenaForAllocation());
}
inline std::string* RepresentativeDatasetFile::release_tfrecord_file_path() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.RepresentativeDatasetFile.tfrecord_file_path)
  if (_internal_has_tfrecord_file_path()) {
    clear_has_dataset_file();
    return _impl_.dataset_file_.tfrecord_file_path_.Release();
  } else {
    return nullptr;
  }
}
inline void RepresentativeDatasetFile::set_allocated_tfrecord_file_path(std::string* tfrecord_file_path) {
  if (has_dataset_file()) {
    clear_dataset_file();
  }
  if (tfrecord_file_path != nullptr) {
    set_has_tfrecord_file_path();
    _impl_.dataset_file_.tfrecord_file_path_.InitAllocated(tfrecord_file_path, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.RepresentativeDatasetFile.tfrecord_file_path)
}

inline bool RepresentativeDatasetFile::has_dataset_file() const {
  return dataset_file_case() != DATASET_FILE_NOT_SET;
}
inline void RepresentativeDatasetFile::clear_has_dataset_file() {
  _impl_._oneof_case_[0] = DATASET_FILE_NOT_SET;
}
inline RepresentativeDatasetFile::DatasetFileCase RepresentativeDatasetFile::dataset_file_case() const {
  return RepresentativeDatasetFile::DatasetFileCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// QuantizationOptions

// .tensorflow.quantization.QuantizationMethod quantization_method = 1;
inline bool QuantizationOptions::_internal_has_quantization_method() const {
  return this != internal_default_instance() && _impl_.quantization_method_ != nullptr;
}
inline bool QuantizationOptions::has_quantization_method() const {
  return _internal_has_quantization_method();
}
inline void QuantizationOptions::clear_quantization_method() {
  if (GetArenaForAllocation() == nullptr && _impl_.quantization_method_ != nullptr) {
    delete _impl_.quantization_method_;
  }
  _impl_.quantization_method_ = nullptr;
}
inline const ::tensorflow::quantization::QuantizationMethod& QuantizationOptions::_internal_quantization_method() const {
  const ::tensorflow::quantization::QuantizationMethod* p = _impl_.quantization_method_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::quantization::QuantizationMethod&>(
      ::tensorflow::quantization::_QuantizationMethod_default_instance_);
}
inline const ::tensorflow::quantization::QuantizationMethod& QuantizationOptions::quantization_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.quantization_method)
  return _internal_quantization_method();
}
inline void QuantizationOptions::unsafe_arena_set_allocated_quantization_method(
    ::tensorflow::quantization::QuantizationMethod* quantization_method) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_method_);
  }
  _impl_.quantization_method_ = quantization_method;
  if (quantization_method) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.QuantizationOptions.quantization_method)
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::release_quantization_method() {
  
  ::tensorflow::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::unsafe_arena_release_quantization_method() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.QuantizationOptions.quantization_method)
  
  ::tensorflow::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
  return temp;
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::_internal_mutable_quantization_method() {
  
  if (_impl_.quantization_method_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::quantization::QuantizationMethod>(GetArenaForAllocation());
    _impl_.quantization_method_ = p;
  }
  return _impl_.quantization_method_;
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::mutable_quantization_method() {
  ::tensorflow::quantization::QuantizationMethod* _msg = _internal_mutable_quantization_method();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.quantization_method)
  return _msg;
}
inline void QuantizationOptions::set_allocated_quantization_method(::tensorflow::quantization::QuantizationMethod* quantization_method) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.quantization_method_;
  }
  if (quantization_method) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(quantization_method);
    if (message_arena != submessage_arena) {
      quantization_method = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantization_method, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.quantization_method_ = quantization_method;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.QuantizationOptions.quantization_method)
}

// .tensorflow.quantization.OpSet op_set = 2;
inline void QuantizationOptions::clear_op_set() {
  _impl_.op_set_ = 0;
}
inline ::tensorflow::quantization::OpSet QuantizationOptions::_internal_op_set() const {
  return static_cast< ::tensorflow::quantization::OpSet >(_impl_.op_set_);
}
inline ::tensorflow::quantization::OpSet QuantizationOptions::op_set() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.op_set)
  return _internal_op_set();
}
inline void QuantizationOptions::_internal_set_op_set(::tensorflow::quantization::OpSet value) {
  
  _impl_.op_set_ = value;
}
inline void QuantizationOptions::set_op_set(::tensorflow::quantization::OpSet value) {
  _internal_set_op_set(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.op_set)
}

// repeated .tensorflow.quantization.UnitWiseQuantizationSpec unit_wise_quantization_specs = 17;
inline int QuantizationOptions::_internal_unit_wise_quantization_specs_size() const {
  return _impl_.unit_wise_quantization_specs_.size();
}
inline int QuantizationOptions::unit_wise_quantization_specs_size() const {
  return _internal_unit_wise_quantization_specs_size();
}
inline void QuantizationOptions::clear_unit_wise_quantization_specs() {
  _impl_.unit_wise_quantization_specs_.Clear();
}
inline ::tensorflow::quantization::UnitWiseQuantizationSpec* QuantizationOptions::mutable_unit_wise_quantization_specs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_specs)
  return _impl_.unit_wise_quantization_specs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec >*
QuantizationOptions::mutable_unit_wise_quantization_specs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_specs)
  return &_impl_.unit_wise_quantization_specs_;
}
inline const ::tensorflow::quantization::UnitWiseQuantizationSpec& QuantizationOptions::_internal_unit_wise_quantization_specs(int index) const {
  return _impl_.unit_wise_quantization_specs_.Get(index);
}
inline const ::tensorflow::quantization::UnitWiseQuantizationSpec& QuantizationOptions::unit_wise_quantization_specs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_specs)
  return _internal_unit_wise_quantization_specs(index);
}
inline ::tensorflow::quantization::UnitWiseQuantizationSpec* QuantizationOptions::_internal_add_unit_wise_quantization_specs() {
  return _impl_.unit_wise_quantization_specs_.Add();
}
inline ::tensorflow::quantization::UnitWiseQuantizationSpec* QuantizationOptions::add_unit_wise_quantization_specs() {
  ::tensorflow::quantization::UnitWiseQuantizationSpec* _add = _internal_add_unit_wise_quantization_specs();
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_specs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationSpec >&
QuantizationOptions::unit_wise_quantization_specs() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_specs)
  return _impl_.unit_wise_quantization_specs_;
}

// repeated string tags = 5;
inline int QuantizationOptions::_internal_tags_size() const {
  return _impl_.tags_.size();
}
inline int QuantizationOptions::tags_size() const {
  return _internal_tags_size();
}
inline void QuantizationOptions::clear_tags() {
  _impl_.tags_.Clear();
}
inline std::string* QuantizationOptions::add_tags() {
  std::string* _s = _internal_add_tags();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.quantization.QuantizationOptions.tags)
  return _s;
}
inline const std::string& QuantizationOptions::_internal_tags(int index) const {
  return _impl_.tags_.Get(index);
}
inline const std::string& QuantizationOptions::tags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.tags)
  return _internal_tags(index);
}
inline std::string* QuantizationOptions::mutable_tags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.tags)
  return _impl_.tags_.Mutable(index);
}
inline void QuantizationOptions::set_tags(int index, const std::string& value) {
  _impl_.tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.tags)
}
inline void QuantizationOptions::set_tags(int index, std::string&& value) {
  _impl_.tags_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.tags)
}
inline void QuantizationOptions::set_tags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.quantization.QuantizationOptions.tags)
}
inline void QuantizationOptions::set_tags(int index, const char* value, size_t size) {
  _impl_.tags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.quantization.QuantizationOptions.tags)
}
inline std::string* QuantizationOptions::_internal_add_tags() {
  return _impl_.tags_.Add();
}
inline void QuantizationOptions::add_tags(const std::string& value) {
  _impl_.tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationOptions.tags)
}
inline void QuantizationOptions::add_tags(std::string&& value) {
  _impl_.tags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationOptions.tags)
}
inline void QuantizationOptions::add_tags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.quantization.QuantizationOptions.tags)
}
inline void QuantizationOptions::add_tags(const char* value, size_t size) {
  _impl_.tags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.quantization.QuantizationOptions.tags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
QuantizationOptions::tags() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.QuantizationOptions.tags)
  return _impl_.tags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
QuantizationOptions::mutable_tags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.QuantizationOptions.tags)
  return &_impl_.tags_;
}

// repeated string signature_keys = 6;
inline int QuantizationOptions::_internal_signature_keys_size() const {
  return _impl_.signature_keys_.size();
}
inline int QuantizationOptions::signature_keys_size() const {
  return _internal_signature_keys_size();
}
inline void QuantizationOptions::clear_signature_keys() {
  _impl_.signature_keys_.Clear();
}
inline std::string* QuantizationOptions::add_signature_keys() {
  std::string* _s = _internal_add_signature_keys();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.quantization.QuantizationOptions.signature_keys)
  return _s;
}
inline const std::string& QuantizationOptions::_internal_signature_keys(int index) const {
  return _impl_.signature_keys_.Get(index);
}
inline const std::string& QuantizationOptions::signature_keys(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.signature_keys)
  return _internal_signature_keys(index);
}
inline std::string* QuantizationOptions::mutable_signature_keys(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.signature_keys)
  return _impl_.signature_keys_.Mutable(index);
}
inline void QuantizationOptions::set_signature_keys(int index, const std::string& value) {
  _impl_.signature_keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline void QuantizationOptions::set_signature_keys(int index, std::string&& value) {
  _impl_.signature_keys_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline void QuantizationOptions::set_signature_keys(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.signature_keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline void QuantizationOptions::set_signature_keys(int index, const char* value, size_t size) {
  _impl_.signature_keys_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline std::string* QuantizationOptions::_internal_add_signature_keys() {
  return _impl_.signature_keys_.Add();
}
inline void QuantizationOptions::add_signature_keys(const std::string& value) {
  _impl_.signature_keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline void QuantizationOptions::add_signature_keys(std::string&& value) {
  _impl_.signature_keys_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline void QuantizationOptions::add_signature_keys(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.signature_keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline void QuantizationOptions::add_signature_keys(const char* value, size_t size) {
  _impl_.signature_keys_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.quantization.QuantizationOptions.signature_keys)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
QuantizationOptions::signature_keys() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.QuantizationOptions.signature_keys)
  return _impl_.signature_keys_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
QuantizationOptions::mutable_signature_keys() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.QuantizationOptions.signature_keys)
  return &_impl_.signature_keys_;
}

// map<string, .tensorflow.quantization.RepresentativeDatasetFile> representative_datasets = 7;
inline int QuantizationOptions::_internal_representative_datasets_size() const {
  return _impl_.representative_datasets_.size();
}
inline int QuantizationOptions::representative_datasets_size() const {
  return _internal_representative_datasets_size();
}
inline void QuantizationOptions::clear_representative_datasets() {
  _impl_.representative_datasets_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >&
QuantizationOptions::_internal_representative_datasets() const {
  return _impl_.representative_datasets_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >&
QuantizationOptions::representative_datasets() const {
  // @@protoc_insertion_point(field_map:tensorflow.quantization.QuantizationOptions.representative_datasets)
  return _internal_representative_datasets();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >*
QuantizationOptions::_internal_mutable_representative_datasets() {
  return _impl_.representative_datasets_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::quantization::RepresentativeDatasetFile >*
QuantizationOptions::mutable_representative_datasets() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.quantization.QuantizationOptions.representative_datasets)
  return _internal_mutable_representative_datasets();
}

// int64 min_num_elements_for_weights = 8;
inline void QuantizationOptions::clear_min_num_elements_for_weights() {
  _impl_.min_num_elements_for_weights_ = int64_t{0};
}
inline int64_t QuantizationOptions::_internal_min_num_elements_for_weights() const {
  return _impl_.min_num_elements_for_weights_;
}
inline int64_t QuantizationOptions::min_num_elements_for_weights() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.min_num_elements_for_weights)
  return _internal_min_num_elements_for_weights();
}
inline void QuantizationOptions::_internal_set_min_num_elements_for_weights(int64_t value) {
  
  _impl_.min_num_elements_for_weights_ = value;
}
inline void QuantizationOptions::set_min_num_elements_for_weights(int64_t value) {
  _internal_set_min_num_elements_for_weights(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.min_num_elements_for_weights)
}

// optional bool freeze_all_variables = 9;
inline bool QuantizationOptions::_internal_has_freeze_all_variables() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool QuantizationOptions::has_freeze_all_variables() const {
  return _internal_has_freeze_all_variables();
}
inline void QuantizationOptions::clear_freeze_all_variables() {
  _impl_.freeze_all_variables_ = false;
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline bool QuantizationOptions::_internal_freeze_all_variables() const {
  return _impl_.freeze_all_variables_;
}
inline bool QuantizationOptions::freeze_all_variables() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
  return _internal_freeze_all_variables();
}
inline void QuantizationOptions::_internal_set_freeze_all_variables(bool value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.freeze_all_variables_ = value;
}
inline void QuantizationOptions::set_freeze_all_variables(bool value) {
  _internal_set_freeze_all_variables(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
}

// optional bool enable_per_channel_quantization = 10;
inline bool QuantizationOptions::_internal_has_enable_per_channel_quantization() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool QuantizationOptions::has_enable_per_channel_quantization() const {
  return _internal_has_enable_per_channel_quantization();
}
inline void QuantizationOptions::clear_enable_per_channel_quantization() {
  _impl_.enable_per_channel_quantization_ = false;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline bool QuantizationOptions::_internal_enable_per_channel_quantization() const {
  return _impl_.enable_per_channel_quantization_;
}
inline bool QuantizationOptions::enable_per_channel_quantization() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.enable_per_channel_quantization)
  return _internal_enable_per_channel_quantization();
}
inline void QuantizationOptions::_internal_set_enable_per_channel_quantization(bool value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.enable_per_channel_quantization_ = value;
}
inline void QuantizationOptions::set_enable_per_channel_quantization(bool value) {
  _internal_set_enable_per_channel_quantization(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.enable_per_channel_quantization)
}

// bool enable_two_input_tensors = 11;
inline void QuantizationOptions::clear_enable_two_input_tensors() {
  _impl_.enable_two_input_tensors_ = false;
}
inline bool QuantizationOptions::_internal_enable_two_input_tensors() const {
  return _impl_.enable_two_input_tensors_;
}
inline bool QuantizationOptions::enable_two_input_tensors() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.enable_two_input_tensors)
  return _internal_enable_two_input_tensors();
}
inline void QuantizationOptions::_internal_set_enable_two_input_tensors(bool value) {
  
  _impl_.enable_two_input_tensors_ = value;
}
inline void QuantizationOptions::set_enable_two_input_tensors(bool value) {
  _internal_set_enable_two_input_tensors(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.enable_two_input_tensors)
}

// bool experimental_enable_tpu_model_support = 12;
inline void QuantizationOptions::clear_experimental_enable_tpu_model_support() {
  _impl_.experimental_enable_tpu_model_support_ = false;
}
inline bool QuantizationOptions::_internal_experimental_enable_tpu_model_support() const {
  return _impl_.experimental_enable_tpu_model_support_;
}
inline bool QuantizationOptions::experimental_enable_tpu_model_support() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.experimental_enable_tpu_model_support)
  return _internal_experimental_enable_tpu_model_support();
}
inline void QuantizationOptions::_internal_set_experimental_enable_tpu_model_support(bool value) {
  
  _impl_.experimental_enable_tpu_model_support_ = value;
}
inline void QuantizationOptions::set_experimental_enable_tpu_model_support(bool value) {
  _internal_set_experimental_enable_tpu_model_support(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.experimental_enable_tpu_model_support)
}

// bool enable_legacy_weight_only = 13;
inline void QuantizationOptions::clear_enable_legacy_weight_only() {
  _impl_.enable_legacy_weight_only_ = false;
}
inline bool QuantizationOptions::_internal_enable_legacy_weight_only() const {
  return _impl_.enable_legacy_weight_only_;
}
inline bool QuantizationOptions::enable_legacy_weight_only() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.enable_legacy_weight_only)
  return _internal_enable_legacy_weight_only();
}
inline void QuantizationOptions::_internal_set_enable_legacy_weight_only(bool value) {
  
  _impl_.enable_legacy_weight_only_ = value;
}
inline void QuantizationOptions::set_enable_legacy_weight_only(bool value) {
  _internal_set_enable_legacy_weight_only(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.enable_legacy_weight_only)
}

// bool force_graph_mode_calibration = 14;
inline void QuantizationOptions::clear_force_graph_mode_calibration() {
  _impl_.force_graph_mode_calibration_ = false;
}
inline bool QuantizationOptions::_internal_force_graph_mode_calibration() const {
  return _impl_.force_graph_mode_calibration_;
}
inline bool QuantizationOptions::force_graph_mode_calibration() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.force_graph_mode_calibration)
  return _internal_force_graph_mode_calibration();
}
inline void QuantizationOptions::_internal_set_force_graph_mode_calibration(bool value) {
  
  _impl_.force_graph_mode_calibration_ = value;
}
inline void QuantizationOptions::set_force_graph_mode_calibration(bool value) {
  _internal_set_force_graph_mode_calibration(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.force_graph_mode_calibration)
}

// .stablehlo.quantization.CalibrationOptions calibration_options = 15;
inline bool QuantizationOptions::_internal_has_calibration_options() const {
  return this != internal_default_instance() && _impl_.calibration_options_ != nullptr;
}
inline bool QuantizationOptions::has_calibration_options() const {
  return _internal_has_calibration_options();
}
inline const ::stablehlo::quantization::CalibrationOptions& QuantizationOptions::_internal_calibration_options() const {
  const ::stablehlo::quantization::CalibrationOptions* p = _impl_.calibration_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::CalibrationOptions&>(
      ::stablehlo::quantization::_CalibrationOptions_default_instance_);
}
inline const ::stablehlo::quantization::CalibrationOptions& QuantizationOptions::calibration_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.calibration_options)
  return _internal_calibration_options();
}
inline void QuantizationOptions::unsafe_arena_set_allocated_calibration_options(
    ::stablehlo::quantization::CalibrationOptions* calibration_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.calibration_options_);
  }
  _impl_.calibration_options_ = calibration_options;
  if (calibration_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.QuantizationOptions.calibration_options)
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationOptions::release_calibration_options() {
  
  ::stablehlo::quantization::CalibrationOptions* temp = _impl_.calibration_options_;
  _impl_.calibration_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationOptions::unsafe_arena_release_calibration_options() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.QuantizationOptions.calibration_options)
  
  ::stablehlo::quantization::CalibrationOptions* temp = _impl_.calibration_options_;
  _impl_.calibration_options_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationOptions::_internal_mutable_calibration_options() {
  
  if (_impl_.calibration_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::CalibrationOptions>(GetArenaForAllocation());
    _impl_.calibration_options_ = p;
  }
  return _impl_.calibration_options_;
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationOptions::mutable_calibration_options() {
  ::stablehlo::quantization::CalibrationOptions* _msg = _internal_mutable_calibration_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.calibration_options)
  return _msg;
}
inline void QuantizationOptions::set_allocated_calibration_options(::stablehlo::quantization::CalibrationOptions* calibration_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.calibration_options_);
  }
  if (calibration_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(calibration_options));
    if (message_arena != submessage_arena) {
      calibration_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, calibration_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.calibration_options_ = calibration_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.QuantizationOptions.calibration_options)
}

// .stablehlo.quantization.DebuggerConfig debugger_config = 16;
inline bool QuantizationOptions::_internal_has_debugger_config() const {
  return this != internal_default_instance() && _impl_.debugger_config_ != nullptr;
}
inline bool QuantizationOptions::has_debugger_config() const {
  return _internal_has_debugger_config();
}
inline const ::stablehlo::quantization::DebuggerConfig& QuantizationOptions::_internal_debugger_config() const {
  const ::stablehlo::quantization::DebuggerConfig* p = _impl_.debugger_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::DebuggerConfig&>(
      ::stablehlo::quantization::_DebuggerConfig_default_instance_);
}
inline const ::stablehlo::quantization::DebuggerConfig& QuantizationOptions::debugger_config() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.debugger_config)
  return _internal_debugger_config();
}
inline void QuantizationOptions::unsafe_arena_set_allocated_debugger_config(
    ::stablehlo::quantization::DebuggerConfig* debugger_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debugger_config_);
  }
  _impl_.debugger_config_ = debugger_config;
  if (debugger_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.QuantizationOptions.debugger_config)
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationOptions::release_debugger_config() {
  
  ::stablehlo::quantization::DebuggerConfig* temp = _impl_.debugger_config_;
  _impl_.debugger_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationOptions::unsafe_arena_release_debugger_config() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.QuantizationOptions.debugger_config)
  
  ::stablehlo::quantization::DebuggerConfig* temp = _impl_.debugger_config_;
  _impl_.debugger_config_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationOptions::_internal_mutable_debugger_config() {
  
  if (_impl_.debugger_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::DebuggerConfig>(GetArenaForAllocation());
    _impl_.debugger_config_ = p;
  }
  return _impl_.debugger_config_;
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationOptions::mutable_debugger_config() {
  ::stablehlo::quantization::DebuggerConfig* _msg = _internal_mutable_debugger_config();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.debugger_config)
  return _msg;
}
inline void QuantizationOptions::set_allocated_debugger_config(::stablehlo::quantization::DebuggerConfig* debugger_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debugger_config_);
  }
  if (debugger_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debugger_config));
    if (message_arena != submessage_arena) {
      debugger_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debugger_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.debugger_config_ = debugger_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.QuantizationOptions.debugger_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace quantization
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::quantization::QuantizationMethod_PresetMethod> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::QuantizationMethod_PresetMethod>() {
  return ::tensorflow::quantization::QuantizationMethod_PresetMethod_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent>() {
  return ::tensorflow::quantization::QuantizationComponentSpec_QuantizationComponent_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::QuantizationComponentSpec_TensorType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::QuantizationComponentSpec_TensorType>() {
  return ::tensorflow::quantization::QuantizationComponentSpec_TensorType_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::OpSet> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::OpSet>() {
  return ::tensorflow::quantization::OpSet_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto
